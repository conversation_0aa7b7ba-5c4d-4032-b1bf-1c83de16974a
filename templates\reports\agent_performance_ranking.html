{% extends 'base.html' %}

{% block title %}Agent Performance Ranking{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Sales Agent Performance Ranking</h1>
        <a href="{{ url_for('reports') }}" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Reports
        </a>
    </div>

    <!-- Performance Summary -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Agents</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ agents|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Sales</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_sales|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Average per Agent</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ average_per_agent|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Top Performer</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ top_agent_name }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Agent Performance Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Sales Agent Performance Ranking</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Agent Name</th>
                            <th>Division</th>
                            <th>Total Orders</th>
                            <th>Total Sales</th>
                            <th>Average Order Value</th>
                            <th>Performance Score</th>
                            <th>Target Achievement</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for agent in agents %}
                        <tr>
                            <td>
                                {% if loop.index <= 3 %}
                                <span class="badge badge-{% if loop.index == 1 %}warning{% elif loop.index == 2 %}secondary{% else %}dark{% endif %}">
                                    #{{ loop.index }}
                                </span>
                                {% else %}
                                {{ loop.index }}
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ agent.agent_name }}</strong>
                                {% if loop.index == 1 %}
                                <i class="fas fa-trophy text-warning ml-1" title="Top Performer"></i>
                                {% elif loop.index == 2 %}
                                <i class="fas fa-medal text-secondary ml-1" title="Second Place"></i>
                                {% elif loop.index == 3 %}
                                <i class="fas fa-award text-dark ml-1" title="Third Place"></i>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-info">{{ agent.division }}</span>
                            </td>
                            <td>{{ agent.total_orders }}</td>
                            <td>{{ agent.total_sales|format_currency }}</td>
                            <td>{{ agent.average_order_value|format_currency }}</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{% if agent.performance_score >= 90 %}success{% elif agent.performance_score >= 70 %}info{% elif agent.performance_score >= 50 %}warning{% else %}danger{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ agent.performance_score }}%"
                                         aria-valuenow="{{ agent.performance_score }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ agent.performance_score }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-{% if agent.target_achievement >= 100 %}success{% elif agent.target_achievement >= 80 %}info{% elif agent.target_achievement >= 60 %}warning{% else %}danger{% endif %}">
                                    {{ agent.target_achievement }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 4, "desc" ]], // Sort by total sales descending
        "pageLength": 25
    });
});
</script>
{% endblock %}
