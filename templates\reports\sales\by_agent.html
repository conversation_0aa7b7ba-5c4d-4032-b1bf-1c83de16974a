{% extends 'base.html' %}

{% block title %}Sales by Agent Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Sales by Agent Report</h4>
                    <div>
                        <button class="btn btn-light" id="printByAgentBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form action="{{ url_for('sales_by_agent_report') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Sales Agent</span>
                                    </div>
                                    <select name="agent" class="form-control">
                                        <option value="">All Agents</option>
                                        {% for agent_item in agents %}
                                        <option value="{{ agent_item.sales_agent }}" {% if selected_agent == agent_item.sales_agent %}selected{% endif %}>
                                            {{ agent_item.sales_agent }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-4 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt"></i> Showing data from {{ start_date }} to {{ end_date }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        {% if selected_agent %}
                                            Sales Summary for {{ selected_agent }}
                                        {% else %}
                                            Sales Summary for All Agents
                                        {% endif %}
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <p><strong>Total Orders:</strong> {{ summary.order_count }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Total Sales:</strong> {{ "%.2f"|format(summary.total_sales or 0) }} PKR</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Average Order:</strong> {{ "%.2f"|format(summary.average_order or 0) }} PKR</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Customers:</strong> {{ summary.customer_count }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-7">
                            <h5>Orders</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Items</th>
                                            <th>Status</th>
                                            <th>Sales Agent</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if sales_data %}
                                            {% for order in sales_data %}
                                            <tr>
                                                <td>{{ order.order_id }}</td>
                                                <td>{{ order.customer_name }}</td>
                                                <td>{{ order.order_date }}</td>
                                                <td>{{ "%.2f"|format(order.order_amount) }}</td>
                                                <td>{{ order.item_count }}</td>
                                                <td>
                                                    <span class="badge 
                                                        {% if order.status == 'Delivered' %}badge-success
                                                        {% elif order.status == 'Dispatched' %}badge-info
                                                        {% elif order.status == 'Approved' %}badge-primary
                                                        {% elif order.status == 'Placed' %}badge-secondary
                                                        {% else %}badge-warning
                                                        {% endif %}">
                                                        {{ order.status }}
                                                    </span>
                                                </td>
                                                <td>{{ order.sales_agent }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center">No orders found for the selected criteria</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="col-md-5">
                            <h5>Agent Comparison</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Sales Agent</th>
                                            <th>Orders</th>
                                            <th>Sales</th>
                                            <th>Avg. Order</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if agent_comparison %}
                                            {% for agent in agent_comparison %}
                                            <tr {% if selected_agent == agent.sales_agent %}class="table-primary"{% endif %}>
                                                <td>{{ agent.sales_agent }}</td>
                                                <td>{{ agent.order_count }}</td>
                                                <td>{{ "%.2f"|format(agent.agent_sales) }}</td>
                                                <td>{{ "%.2f"|format(agent.average_order) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center">No agent comparison data available</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                            
                            {% if agent_comparison %}
                            <div class="mt-4">
                                <h5>Sales Distribution</h5>
                                <div class="progress" style="height: 30px;">
                                    {% for agent in agent_comparison %}
                                        {% if summary.total_sales %}
                                            {% set percentage = (agent.agent_sales / summary.total_sales * 100)|round(1) %}
                                        {% else %}
                                            {% set percentage = 0 %}
                                        {% endif %}
                                        <div class="progress-bar 
                                            {% if loop.index % 5 == 1 %}bg-primary
                                            {% elif loop.index % 5 == 2 %}bg-success
                                            {% elif loop.index % 5 == 3 %}bg-info
                                            {% elif loop.index % 5 == 4 %}bg-warning
                                            {% else %}bg-danger
                                            {% endif %}" 
                                            role="progressbar" 
                                            style="width: {{ percentage }}%" 
                                            title="{{ agent.sales_agent }}: {{ percentage }}%">
                                            {% if percentage > 5 %}{{ percentage }}%{% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                                <div class="mt-2">
                                    {% for agent in agent_comparison %}
                                        {% if loop.index % 5 == 1 %}
                                        <span class="badge badge-primary">{{ agent.sales_agent }}</span>
                                        {% elif loop.index % 5 == 2 %}
                                        <span class="badge badge-success">{{ agent.sales_agent }}</span>
                                        {% elif loop.index % 5 == 3 %}
                                        <span class="badge badge-info">{{ agent.sales_agent }}</span>
                                        {% elif loop.index % 5 == 4 %}
                                        <span class="badge badge-warning">{{ agent.sales_agent }}</span>
                                        {% else %}
                                        <span class="badge badge-danger">{{ agent.sales_agent }}</span>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printByAgentBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}