{% extends "base.html" %}

{% block title %}Duplicate Detection - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Duplicate Detection Styles */
    .duplicate-dashboard {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .duplicate-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .duplicate-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .duplicate-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Statistics Cards */
    .stats-cards {
        margin-bottom: 25px;
    }

    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 120px;
        display: flex;
        align-items: center;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 20px;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .stats-content p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Duplicate Section */
    .duplicate-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .section-title i {
        margin-right: 10px;
        color: #ff9800;
    }

    .duplicate-table {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }

    .duplicate-table thead {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
    }

    .duplicate-table thead th {
        border: none;
        padding: 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .duplicate-table tbody td {
        border: none;
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #ecf0f1;
    }

    .duplicate-table tbody tr:hover {
        background: #fff3e0;
    }

    .severity-badge {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .severity-high {
        background: #ffebee;
        color: #c62828;
        border: 1px solid #c62828;
    }

    .severity-medium {
        background: #fff3e0;
        color: #ef6c00;
        border: 1px solid #ef6c00;
    }

    .severity-low {
        background: #f3e5f5;
        color: #7b1fa2;
        border: 1px solid #7b1fa2;
    }

    .btn-action {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        margin: 0 2px;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-merge {
        background: #4caf50;
        color: white;
    }

    .btn-merge:hover {
        background: #388e3c;
        transform: translateY(-1px);
        color: white;
    }

    .btn-ignore {
        background: #9e9e9e;
        color: white;
    }

    .btn-ignore:hover {
        background: #757575;
        transform: translateY(-1px);
        color: white;
    }

    .btn-view {
        background: #2196f3;
        color: white;
    }

    .btn-view:hover {
        background: #1976d2;
        transform: translateY(-1px);
        color: white;
    }

    .no-duplicates {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
    }

    .no-duplicates i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #bdc3c7;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .duplicate-title {
            font-size: 1.5rem;
        }
        
        .stats-card {
            height: auto;
            padding: 15px;
        }
        
        .stats-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        
        .stats-content h3 {
            font-size: 1.4rem;
        }
        
        .duplicate-table {
            font-size: 0.85rem;
        }
        
        .btn-action {
            padding: 4px 8px;
            font-size: 0.75rem;
        }
    }
</style>

<div class="duplicate-dashboard">
    <div class="dashboard-container">
        <!-- Header -->
        <div class="duplicate-header">
            <h1 class="duplicate-title">
                <i class="fas fa-search mr-3"></i>Duplicate Detection
            </h1>
            <p class="duplicate-subtitle">Identify and manage duplicate entries across the system</p>
        </div>

        <!-- Statistics Cards -->
        <div class="row stats-cards">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-content">
                        <h3>{{ summary_stats.duplicate_customers }}</h3>
                        <p>Duplicate Customers</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stats-content">
                        <h3>{{ summary_stats.duplicate_orders }}</h3>
                        <p>Duplicate Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stats-content">
                        <h3>{{ summary_stats.duplicate_invoices }}</h3>
                        <p>Duplicate Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stats-content">
                        <h3>{{ summary_stats.duplicate_products }}</h3>
                        <p>Duplicate Products</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Duplicate Customers Section -->
        <div class="duplicate-section">
            <div class="section-title">
                <div>
                    <i class="fas fa-users"></i>Duplicate Customers
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-warning" onclick="exportDuplicates('customers')">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            {% if duplicate_customers %}
            <div class="table-responsive">
                <table class="table duplicate-table">
                    <thead>
                        <tr>
                            <th>Customer Name</th>
                            <th>Phone</th>
                            <th>Count</th>
                            <th>Order IDs</th>
                            <th>Severity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in duplicate_customers %}
                        <tr>
                            <td>{{ customer.customer_name }}</td>
                            <td>{{ customer.customer_phone }}</td>
                            <td>{{ customer.count }}</td>
                            <td>{{ customer.order_ids }}</td>
                            <td>
                                {% if customer.count > 3 %}
                                <span class="severity-badge severity-high">High</span>
                                {% elif customer.count > 2 %}
                                <span class="severity-badge severity-medium">Medium</span>
                                {% else %}
                                <span class="severity-badge severity-low">Low</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-action btn-merge" onclick="mergeDuplicates('customer', '{{ customer.customer_name }}')">
                                    <i class="fas fa-object-group"></i> Merge
                                </button>
                                <button class="btn btn-action btn-view" onclick="viewDetails('customer', '{{ customer.customer_name }}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="no-duplicates">
                <i class="fas fa-check-circle"></i>
                <p>No duplicate customers found</p>
            </div>
            {% endif %}
        </div>

        <!-- Duplicate Orders Section -->
        <div class="duplicate-section">
            <div class="section-title">
                <div>
                    <i class="fas fa-shopping-cart"></i>Duplicate Orders
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-warning" onclick="exportDuplicates('orders')">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            {% if duplicate_orders %}
            <div class="table-responsive">
                <table class="table duplicate-table">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Count</th>
                            <th>Order IDs</th>
                            <th>Severity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in duplicate_orders %}
                        <tr>
                            <td>{{ order.customer_name }}</td>
                            <td>₹{{ order.order_amount }}</td>
                            <td>{{ order.order_date }}</td>
                            <td>{{ order.count }}</td>
                            <td>{{ order.order_ids }}</td>
                            <td>
                                {% if order.count > 2 %}
                                <span class="severity-badge severity-high">High</span>
                                {% else %}
                                <span class="severity-badge severity-medium">Medium</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-action btn-merge" onclick="mergeDuplicates('order', '{{ order.order_ids }}')">
                                    <i class="fas fa-object-group"></i> Merge
                                </button>
                                <button class="btn btn-action btn-view" onclick="viewDetails('order', '{{ order.order_ids }}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="no-duplicates">
                <i class="fas fa-check-circle"></i>
                <p>No duplicate orders found</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Duplicate Detection JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize functionality
    console.log('Duplicate detection system initialized');
});

function mergeDuplicates(type, ids) {
    // Show confirmation dialog
    if (confirm(`Are you sure you want to merge these duplicate ${type} records?`)) {
        // In a real implementation, this would call an API endpoint
        alert(`Merging ${type} records: ${ids}`);
    }
}

function viewDetails(type, ids) {
    // In a real implementation, this would open a modal or navigate to a details page
    alert(`Viewing details for ${type}: ${ids}`);
}

function exportDuplicates(type) {
    // In a real implementation, this would generate and download a report
    alert(`Exporting ${type} duplicates report`);
}
</script>

{% endblock %}
