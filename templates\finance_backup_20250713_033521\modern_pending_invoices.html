{% extends 'base.html' %}

{% block title %}Pending Invoices - Finance - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<style>
/* Modern 2025 Finance Pending Invoices UI */
.finance-pending {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.finance-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.finance-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #f6c23e, #dda20a, #e74a3b);
}

.finance-title {
    background: linear-gradient(45deg, #f6c23e, #e74a3b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.modern-finance-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modern-finance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.modern-btn-finance {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
}

.modern-btn-finance:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.modern-btn-warning {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    box-shadow: 0 10px 20px rgba(246, 194, 62, 0.3);
}

.modern-btn-danger {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    box-shadow: 0 10px 20px rgba(231, 74, 59, 0.3);
}

.stat-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.stat-card-modern.warning::before {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
}

.stat-card-modern.danger::before {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
}

.stat-card-modern.success::before {
    background: linear-gradient(135deg, #1cc88a, #13855c);
}

.stat-card-modern.info::before {
    background: linear-gradient(135deg, #36b9cc, #258391);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.stat-number-modern {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stat-label-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0;
}

.invoice-row {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.invoice-row:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.priority-urgent {
    border-left: 5px solid #e74a3b;
}

.priority-high {
    border-left: 5px solid #f6c23e;
}

.priority-normal {
    border-left: 5px solid #1cc88a;
}

@media (max-width: 768px) {
    .finance-title {
        font-size: 2rem;
    }
    .stat-number-modern {
        font-size: 2.5rem;
    }
}
</style>

<div class="finance-pending">
    <div class="container-fluid">
        <!-- Modern Header -->
        <div class="finance-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-file-invoice-dollar mr-3"></i>Pending Invoices
                    </h1>
                    <p class="text-muted mb-0 mt-2">Orders with delivery challans awaiting invoice generation</p>
                </div>
                <div>
                    <a href="{{ url_for('finance') }}" class="modern-btn-finance">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Finance
                    </a>
                    <button class="modern-btn-finance modern-btn-success" onclick="bulkGenerate()">
                        <i class="fas fa-file-invoice mr-2"></i>Bulk Generate
                    </button>
                </div>
            </div>
        </div>

        <!-- Modern Performance Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern warning">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-warning">{{ summary.total_pending or pending_invoices|length }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-file-invoice-dollar mr-2 text-warning"></i>Total Pending
                            </div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern danger">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-danger">{{ summary.urgent_count or 0 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-exclamation-triangle mr-2 text-danger"></i>Urgent
                            </div>
                        </div>
                        <div class="text-danger">
                            <i class="fas fa-fire fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern info">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-info">{{ summary.high_count or 0 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-arrow-up mr-2 text-info"></i>High Priority
                            </div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-level-up-alt fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern success">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-success">Rs. {{ "{:,.0f}"|format(summary.total_amount or 0) }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-money-bill-wave mr-2 text-success"></i>Total Amount
                            </div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-dollar-sign fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Pending Invoices List -->
        <div class="modern-finance-card">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <div>
                    <h4 class="font-weight-bold text-primary mb-1">
                        <i class="fas fa-list mr-2"></i>Pending Invoice Generation
                    </h4>
                    <p class="text-muted mb-0">Review and process pending invoices</p>
                </div>
                <div>
                    <button class="modern-btn-finance modern-btn-warning" onclick="filterUrgent()">
                        <i class="fas fa-filter mr-2"></i>Urgent Only
                    </button>
                </div>
            </div>

            {% if pending_invoices %}
                {% for invoice in pending_invoices %}
                <div class="invoice-row priority-{{ invoice.priority|lower if invoice.priority else 'normal' }}">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <div class="font-weight-bold text-primary">{{ invoice.order_id }}</div>
                            <small class="text-muted">{{ invoice.order_date[:10] if invoice.order_date else 'N/A' }}</small>
                        </div>
                        <div class="col-md-3">
                            <div class="font-weight-bold">{{ invoice.customer_name }}</div>
                            <small class="text-muted">{{ invoice.customer_address[:50] if invoice.customer_address else 'No address' }}...</small>
                        </div>
                        <div class="col-md-2">
                            <div class="font-weight-bold text-success">Rs. {{ "{:,.0f}"|format(invoice.order_amount) }}</div>
                            <small class="text-muted">Order Amount</small>
                        </div>
                        <div class="col-md-2">
                            <span class="badge badge-{{ 'danger' if invoice.priority == 'urgent' else 'warning' if invoice.priority == 'high' else 'success' }} badge-lg">
                                {{ invoice.priority|title if invoice.priority else 'Normal' }}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm modern-btn-finance modern-btn-success" onclick="generateInvoice('{{ invoice.order_id }}')">
                                    <i class="fas fa-file-invoice"></i> Generate
                                </button>
                                <button class="btn btn-sm modern-btn-finance" onclick="viewDetails('{{ invoice.order_id }}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="btn btn-sm modern-btn-finance modern-btn-warning" onclick="holdInvoice('{{ invoice.order_id }}')">
                                    <i class="fas fa-pause"></i> Hold
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                    <h4 class="text-muted">No Pending Invoices</h4>
                    <p class="text-muted">All invoices have been generated and processed.</p>
                    <a href="{{ url_for('finance') }}" class="modern-btn-finance">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Finance Dashboard
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function generateInvoice(orderId) {
    if (confirm('Generate invoice for order ' + orderId + '?')) {
        fetch(`/finance/generate-invoice/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Invoice generated successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.message || 'Error generating invoice', 'error');
            }
        })
        .catch(error => {
            showNotification('Error generating invoice', 'error');
        });
    }
}

function viewDetails(orderId) {
    window.location.href = `/finance/pending-invoice/${orderId}`;
}

function holdInvoice(orderId) {
    const reason = prompt('Enter reason for holding this invoice:');
    if (reason) {
        fetch(`/finance/hold-invoice/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({reason: reason})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Invoice held successfully!', 'warning');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.message || 'Error holding invoice', 'error');
            }
        })
        .catch(error => {
            showNotification('Error holding invoice', 'error');
        });
    }
}

function bulkGenerate() {
    if (confirm('Generate all pending invoices? This action cannot be undone.')) {
        showNotification('Bulk generating invoices...', 'info');
        // Implement bulk generation logic
    }
}

function filterUrgent() {
    const rows = document.querySelectorAll('.invoice-row');
    rows.forEach(row => {
        if (row.classList.contains('priority-urgent')) {
            row.style.display = 'block';
        } else {
            row.style.display = 'none';
        }
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>
{% endblock %}
