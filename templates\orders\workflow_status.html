{% extends "base.html" %}

{% block title %}Order Workflow - {{ order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Workflow Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-cogs"></i> Order Workflow Status - {{ order_id }}
                            </h4>
                            <small>Customer: {{ workflow_status.order.customer_name }}</small>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="badge badge-{% if workflow_status.order.status == 'Approved' %}success{% elif workflow_status.order.status == 'Cancelled' %}danger{% elif workflow_status.order.status == 'Pending Review' %}warning{% else %}secondary{% endif %} badge-lg">
                                {{ workflow_status.order.status }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Order Summary -->
                        <div class="col-md-6">
                            <h6><strong>Order Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Order ID:</strong></td>
                                    <td>{{ workflow_status.order.order_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>{{ workflow_status.order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Date:</strong></td>
                                    <td>{{ workflow_status.order.order_date | format_datetime }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Amount:</strong></td>
                                    <td>₹{{ workflow_status.order.order_amount | format_currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if workflow_status.order.priority == 'urgent' %}danger{% elif workflow_status.order.priority == 'high' %}warning{% else %}secondary{% endif %}">
                                            {{ workflow_status.order.priority|title }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if workflow_status.order.payment_status == 'paid' %}success{% elif workflow_status.order.payment_status == 'partial' %}warning{% else %}danger{% endif %}">
                                            {{ workflow_status.order.payment_status|title }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Workflow Progress -->
                        <div class="col-md-6">
                            <h6><strong>Workflow Progress</strong></h6>
                            <div class="progress-container">
                                {% set progress_steps = ['Placed', 'Approved', 'Processing', 'Ready for Pickup', 'Dispatched', 'Delivered'] %}
                                {% set current_step = workflow_status.order.status %}
                                {% set current_index = progress_steps.index(current_step) if current_step in progress_steps else 0 %}
                                {% set progress_percentage = ((current_index + 1) / progress_steps|length * 100)|round %}
                                
                                <div class="progress mb-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" 
                                         style="width: {{ progress_percentage }}%"
                                         aria-valuenow="{{ progress_percentage }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ progress_percentage }}%
                                    </div>
                                </div>
                                
                                <div class="workflow-steps">
                                    {% for step in progress_steps %}
                                    <div class="step {% if loop.index0 <= current_index %}completed{% endif %}">
                                        <i class="fas fa-{% if loop.index0 <= current_index %}check-circle text-success{% else %}circle text-muted{% endif %}"></i>
                                        <span class="{% if loop.index0 <= current_index %}text-success{% else %}text-muted{% endif %}">{{ step }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <strong>Workflow Complete:</strong> 
                                <span class="badge badge-{% if workflow_status.workflow_complete %}success{% else %}warning{% endif %}">
                                    {% if workflow_status.workflow_complete %}Yes{% else %}No{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Manual Actions -->
            {% if workflow_status.order.status in ['Placed', 'Pending Review'] %}
            <div class="card mb-4">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-hand-paper"></i> Manual Actions Required</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <form method="POST" action="{{ url_for('approve_order_manual', order_id=order_id) }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="form-group">
                                    <label for="approval_notes">Approval Notes:</label>
                                    <textarea name="approval_notes" id="approval_notes" class="form-control" rows="3" placeholder="Enter approval notes..."></textarea>
                                </div>
                                <button type="submit" class="btn btn-success" onclick="return confirm('Approve this order?')">
                                    <i class="fas fa-check"></i> Approve Order
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="POST" action="{{ url_for('reject_order_manual', order_id=order_id) }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="form-group">
                                    <label for="rejection_reason">Rejection Reason:</label>
                                    <textarea name="rejection_reason" id="rejection_reason" class="form-control" rows="3" placeholder="Enter rejection reason..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Reject this order?')">
                                    <i class="fas fa-times"></i> Reject Order
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Inventory Allocations -->
            {% if workflow_status.allocations %}
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-boxes"></i> Inventory Allocations</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Batch Number</th>
                                    <th>Allocated Qty</th>
                                    <th>Warehouse</th>
                                    <th>Allocation Type</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for allocation in workflow_status.allocations %}
                                <tr>
                                    <td>{{ allocation.product_name or allocation.product_id }}</td>
                                    <td>{{ allocation.batch_number }}</td>
                                    <td>{{ allocation.allocated_quantity }}</td>
                                    <td>{{ allocation.warehouse_name or allocation.warehouse_id }}</td>
                                    <td>
                                        <span class="badge badge-{% if allocation.allocation_type == 'automatic' %}primary{% else %}info{% endif %}">
                                            {{ allocation.allocation_type|title }}
                                        </span>
                                    </td>
                                    <td>{{ allocation.allocation_date | format_datetime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <span class="badge badge-success">{{ allocation.status|title }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Audit Trail -->
            {% if workflow_status.audit_trail %}
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Audit Trail</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for log in workflow_status.audit_trail %}
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-{% if log.action == 'order_created' %}plus{% elif log.action == 'manual_approval' %}check{% elif log.action == 'manual_rejection' %}times{% else %}info{% endif %}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ log.action|replace('_', ' ')|title }}</h6>
                                <p class="timeline-description">
                                    {% if log.new_values %}
                                    {% set values = log.new_values | from_json %}
                                    {% for key, value in values.items() %}
                                    <strong>{{ key|replace('_', ' ')|title }}:</strong> {{ value }}<br>
                                    {% endfor %}
                                    {% endif %}
                                </p>
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> {{ log.timestamp | format_datetime }}
                                    {% if log.user_id %}| <i class="fas fa-user"></i> {{ log.user_id }}{% endif %}
                                    {% if log.ip_address %}| <i class="fas fa-map-marker-alt"></i> {{ log.ip_address }}{% endif %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('orders') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Orders
                        </a>
                        <a href="{{ url_for('view_order', order_id=order_id) }}" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Order Details
                        </a>
                        <a href="{{ url_for('allocate_inventory_page', order_id=order_id) }}" class="btn btn-info">
                            <i class="fas fa-boxes"></i> Manage Inventory
                        </a>
                        {% if workflow_status.order.status == 'Approved' %}
                        <a href="{{ url_for('generate_challan', order_id=order_id) }}" class="btn btn-warning">
                            <i class="fas fa-file-alt"></i> Generate Challan
                        </a>
                        {% endif %}
                        <button class="btn btn-success" onclick="window.location.reload()">
                            <i class="fas fa-sync"></i> Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}

.workflow-steps .step {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.workflow-steps .step i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--primary);
}

.timeline-title {
    margin-bottom: 5px;
    color: var(--primary);
}

.timeline-description {
    margin-bottom: 10px;
    color: #666;
}

.progress-container {
    margin: 20px 0;
}
</style>
{% endblock %}
