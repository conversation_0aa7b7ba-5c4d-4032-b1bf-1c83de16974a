<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCS Tracking - Browser vs Terminal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-numbers {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .test-number {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s;
        }
        .test-number:hover {
            border-color: #007bff;
        }
        .test-number.testing {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .test-number.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-number.failed {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .result-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .result-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card.success { background: #28a745; }
        .stat-card.error { background: #dc3545; }
        .stat-card.warning { background: #ffc107; color: #212529; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .server-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .server-online { background: #d4edda; color: #155724; }
        .server-offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 TCS Tracking: Browser vs Terminal Consistency Test</h1>
        
        <!-- Server Status -->
        <div class="test-section">
            <h2>🌐 Server Status</h2>
            <div id="serverStatus" class="server-status">Checking server...</div>
            <button onclick="checkServerStatus()">Refresh Status</button>
        </div>

        <!-- Test Numbers -->
        <div class="test-section">
            <h2>📦 Test Tracking Numbers</h2>
            <p>Click on any tracking number to test it individually, or use the buttons below for bulk testing.</p>
            
            <div class="test-numbers" id="testNumbers">
                <!-- Will be populated by JavaScript -->
            </div>
            
            <div style="margin-top: 20px;">
                <button onclick="testAllNumbers()">Test All Numbers</button>
                <button onclick="testSampleNumbers()">Test Sample Numbers</button>
                <button onclick="clearResults()">Clear Results</button>
                <button onclick="exportResults()">Export Results</button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="test-section">
            <h2>📊 Test Statistics</h2>
            <div class="stats">
                <div class="stat-card" id="totalStat">
                    <div style="font-size: 24px; font-weight: bold;" id="totalCount">0</div>
                    <div>Total Tested</div>
                </div>
                <div class="stat-card success" id="successStat">
                    <div style="font-size: 24px; font-weight: bold;" id="successCount">0</div>
                    <div>Successful</div>
                </div>
                <div class="stat-card error" id="errorStat">
                    <div style="font-size: 24px; font-weight: bold;" id="errorCount">0</div>
                    <div>Failed</div>
                </div>
                <div class="stat-card warning" id="rateStat">
                    <div style="font-size: 24px; font-weight: bold;" id="successRate">0%</div>
                    <div>Success Rate</div>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="test-section">
            <h2>📋 Test Results</h2>
            <div class="results" id="results">
                <p>No tests run yet. Click on tracking numbers above or use the test buttons.</p>
            </div>
        </div>
    </div>

    <script>
        // Test data - tracking numbers that reportedly work in terminal
        const testTrackingNumbers = [
            '31442083522',
            '31442083523', 
            '31442083525',
            '31442083524',
            '31442083393',
            '31442083394',
            '31442083325',
            '31442083485',
            '31442083484',
            '31442083486',
            '31442083487',
            '31442084039',
            '31442084040',
            '31442084038',
            '31442084041'
        ];

        // Global variables
        let testResults = [];
        let isTestingInProgress = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            populateTestNumbers();
            checkServerStatus();
        });

        function populateTestNumbers() {
            const container = document.getElementById('testNumbers');
            container.innerHTML = '';
            
            testTrackingNumbers.forEach(number => {
                const div = document.createElement('div');
                div.className = 'test-number';
                div.id = `number-${number}`;
                div.innerHTML = `
                    <div style="font-weight: bold;">${number}</div>
                    <div style="font-size: 12px; color: #666;">Click to test</div>
                `;
                div.onclick = () => testSingleNumber(number);
                container.appendChild(div);
            });
        }

        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.textContent = 'Checking server...';
            statusDiv.className = 'server-status';
            
            try {
                const response = await fetch('http://localhost:5001/health');
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    statusDiv.textContent = `✅ Server Online - CORS: ${data.cors_enabled ? 'Enabled' : 'Disabled'}, Scraper: ${data.scraper_available ? 'Available' : 'Unavailable'}`;
                    statusDiv.className = 'server-status server-online';
                } else {
                    statusDiv.textContent = '❌ Server responding but unhealthy';
                    statusDiv.className = 'server-status server-offline';
                }
            } catch (error) {
                statusDiv.textContent = `❌ Server Offline - ${error.message}`;
                statusDiv.className = 'server-status server-offline';
            }
        }

        async function testSingleNumber(trackingNumber) {
            if (isTestingInProgress) return;
            
            const numberDiv = document.getElementById(`number-${trackingNumber}`);
            numberDiv.className = 'test-number testing';
            
            addResult(`🔄 Testing ${trackingNumber}...`, 'info');
            
            try {
                const startTime = Date.now();
                
                const response = await fetch('http://localhost:5001/api/track-tcs-public', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ tracking_number: trackingNumber })
                });
                
                const duration = Date.now() - startTime;
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const trackingData = data.data;
                    numberDiv.className = 'test-number success';
                    
                    const result = {
                        trackingNumber,
                        success: true,
                        status: trackingData.current_status,
                        route: `${trackingData.origin} → ${trackingData.destination}`,
                        history: trackingData.track_history?.length || 0,
                        duration
                    };
                    
                    testResults.push(result);
                    
                    addResult(
                        `✅ ${trackingNumber}: ${result.status} (${result.route}) - ${result.history} history entries (${duration}ms)`,
                        'success'
                    );
                } else {
                    numberDiv.className = 'test-number failed';
                    
                    const error = data.error || 'Unknown error';
                    const result = {
                        trackingNumber,
                        success: false,
                        error,
                        duration
                    };
                    
                    testResults.push(result);
                    
                    addResult(
                        `❌ ${trackingNumber}: ${error} (${duration}ms)`,
                        'error'
                    );
                }
            } catch (error) {
                numberDiv.className = 'test-number failed';
                
                const result = {
                    trackingNumber,
                    success: false,
                    error: error.message,
                    duration: 0
                };
                
                testResults.push(result);
                
                addResult(
                    `💥 ${trackingNumber}: ${error.message}`,
                    'error'
                );
            }
            
            updateStatistics();
        }

        async function testAllNumbers() {
            if (isTestingInProgress) return;
            
            isTestingInProgress = true;
            clearResults();
            
            addResult('🚀 Starting comprehensive test of all tracking numbers...', 'info');
            
            for (let i = 0; i < testTrackingNumbers.length; i++) {
                const number = testTrackingNumbers[i];
                addResult(`📦 Testing ${i + 1}/${testTrackingNumbers.length}: ${number}`, 'info');
                
                await testSingleNumber(number);
                
                // Add delay between requests
                if (i < testTrackingNumbers.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            
            addResult('🏁 All tests completed!', 'info');
            isTestingInProgress = false;
        }

        async function testSampleNumbers() {
            if (isTestingInProgress) return;
            
            const sampleNumbers = testTrackingNumbers.slice(0, 5);
            isTestingInProgress = true;
            
            addResult('🧪 Testing sample tracking numbers...', 'info');
            
            for (const number of sampleNumbers) {
                await testSingleNumber(number);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            isTestingInProgress = false;
        }

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result-item ${type === 'success' ? 'result-success' : type === 'error' ? 'result-error' : ''}`;
            resultDiv.innerHTML = `
                <div style="font-size: 12px; color: #666;">${new Date().toLocaleTimeString()}</div>
                <div>${message}</div>
            `;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function updateStatistics() {
            const total = testResults.length;
            const successful = testResults.filter(r => r.success).length;
            const failed = total - successful;
            const successRate = total > 0 ? Math.round((successful / total) * 100) : 0;
            
            document.getElementById('totalCount').textContent = total;
            document.getElementById('successCount').textContent = successful;
            document.getElementById('errorCount').textContent = failed;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function clearResults() {
            testResults = [];
            document.getElementById('results').innerHTML = '<p>Results cleared. Ready for new tests.</p>';
            updateStatistics();
            
            // Reset number styling
            testTrackingNumbers.forEach(number => {
                const numberDiv = document.getElementById(`number-${number}`);
                if (numberDiv) {
                    numberDiv.className = 'test-number';
                }
            });
        }

        function exportResults() {
            if (testResults.length === 0) {
                alert('No results to export');
                return;
            }
            
            const exportData = {
                timestamp: new Date().toISOString(),
                total_tested: testResults.length,
                successful: testResults.filter(r => r.success).length,
                failed: testResults.filter(r => !r.success).length,
                success_rate: Math.round((testResults.filter(r => r.success).length / testResults.length) * 100),
                results: testResults
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tcs_browser_test_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
