"""
Quick test to verify the CORS fix
"""

import requests
import json

def test_cors_fix():
    """Test the CORS-enabled server"""
    
    print("🧪 QUICK CORS FIX TEST")
    print("=" * 40)
    
    # Test 1: Server health
    print("1. Testing server health...")
    try:
        response = requests.get('http://localhost:5001/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Server healthy")
            print(f"   CORS: {data.get('cors_enabled')}")
            print(f"   Scraper: {data.get('scraper_available')}")
        else:
            print(f"   ❌ Server unhealthy: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Server unreachable: {e}")
        return False
    
    # Test 2: CORS headers
    print("\n2. Testing CORS headers...")
    try:
        response = requests.options('http://localhost:5001/api/track-tcs-public', timeout=5)
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        if cors_headers:
            print("   ✅ CORS headers present:")
            for k, v in cors_headers.items():
                print(f"      {k}: {v}")
        else:
            print("   ❌ No CORS headers found")
            return False
    except Exception as e:
        print(f"   ❌ CORS test failed: {e}")
        return False
    
    # Test 3: Quick API test with invalid number
    print("\n3. Testing API with invalid number...")
    try:
        response = requests.post(
            'http://localhost:5001/api/track-tcs-public',
            json={'tracking_number': '123'},
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if not data.get('success') and 'Incorrect Number' in data.get('error', ''):
                print("   ✅ Error handling working")
            else:
                print(f"   ⚠️  Unexpected response: {data}")
        else:
            print(f"   ❌ API error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API test failed: {e}")
    
    print("\n🎯 CORS FIX STATUS: ✅ READY FOR BROWSER TESTING")
    print("\n📋 NEXT STEPS:")
    print("1. Open test_browser_terminal_consistency.html in browser")
    print("2. Check server status (should show green)")
    print("3. Test individual tracking numbers")
    print("4. Compare with terminal results")
    
    return True

if __name__ == "__main__":
    test_cors_fix()
