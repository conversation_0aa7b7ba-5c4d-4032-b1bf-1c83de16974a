{% extends 'base.html' %}

{% block title %}Rider Performance Analytics - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line text-primary"></i> Rider Performance Analytics
        </h1>
        <div>
            <a href="{{ url_for('riders') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Riders
            </a>
            <button onclick="exportPerformanceReport()" class="btn btn-success shadow-sm">
                <i class="fas fa-download fa-sm text-white-50"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Performance Overview Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Deliveries
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ riders_performance|sum(attribute='total_deliveries') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Success Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set total_deliveries = riders_performance|sum(attribute='total_deliveries') %}
                                {% set successful_deliveries = riders_performance|sum(attribute='successful_deliveries') %}
                                {% if total_deliveries > 0 %}
                                    {{ "%.1f"|format((successful_deliveries / total_deliveries * 100)) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Average Rating
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set avg_rating = (riders_performance|sum(attribute='rating') / riders_performance|length) if riders_performance|length > 0 else 0 %}
                                {{ "%.1f"|format(avg_rating) }}/5.0
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Active Riders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ riders_performance|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-motorcycle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Charts -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Performance Trends</h6>
                </div>
                <div class="card-body">
                    <canvas id="performanceTrendChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performers</h6>
                </div>
                <div class="card-body">
                    {% for rider in riders_performance[:5] %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-circle bg-primary text-white mr-3">
                            {{ rider.name[0] }}
                        </div>
                        <div class="flex-grow-1">
                            <div class="font-weight-bold">{{ rider.name }}</div>
                            <div class="text-muted small">{{ rider.total_deliveries }} deliveries</div>
                        </div>
                        <div class="text-right">
                            <div class="font-weight-bold text-success">{{ "%.1f"|format(rider.rating) }}</div>
                            <div class="text-muted small">{{ "%.1f"|format(rider.success_rate) }}%</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Performance Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Detailed Rider Performance</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="performanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Rider ID</th>
                            <th>Name</th>
                            <th>Rating</th>
                            <th>Total Deliveries</th>
                            <th>Successful</th>
                            <th>Success Rate</th>
                            <th>Current Orders</th>
                            <th>Efficiency</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rider in riders_performance %}
                        <tr>
                            <td><strong>{{ rider.rider_id }}</strong></td>
                            <td>{{ rider.name }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="mr-2">{{ "%.1f"|format(rider.rating) }}</span>
                                    <div class="star-rating">
                                        {% for i in range(5) %}
                                            {% if i < rider.rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ rider.total_deliveries }}</td>
                            <td>{{ rider.successful_deliveries }}</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{% if rider.success_rate >= 90 %}success{% elif rider.success_rate >= 75 %}warning{% else %}danger{% endif %}" 
                                         role="progressbar" style="width: {{ rider.success_rate }}%">
                                        {{ "%.1f"|format(rider.success_rate) }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-{% if rider.current_orders > 0 %}primary{% else %}secondary{% endif %}">
                                    {{ rider.current_orders }}
                                </span>
                            </td>
                            <td>
                                {% set efficiency = (rider.delivery_efficiency * 100) if rider.delivery_efficiency else 0 %}
                                <span class="badge badge-{% if efficiency >= 90 %}success{% elif efficiency >= 75 %}warning{% else %}danger{% endif %}">
                                    {{ "%.0f"|format(efficiency) }}%
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="viewRiderDetails('{{ rider.rider_id }}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewRiderHistory('{{ rider.rider_id }}')">
                                    <i class="fas fa-history"></i> History
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.star-rating {
    font-size: 0.8rem;
}
</style>

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#performanceTable').DataTable({
        "pageLength": 25,
        "order": [[ 2, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 8 }
        ]
    });

    // Performance Trend Chart
    const ctx = document.getElementById('performanceTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: [{% for stat in monthly_stats %}'{{ stat.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Total Orders',
                data: [{% for stat in monthly_stats %}{{ stat.total_orders }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                fill: true
            }, {
                label: 'Delivered Orders',
                data: [{% for stat in monthly_stats %}{{ stat.delivered_orders }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.3,
                fill: true
            }, {
                label: 'Active Riders',
                data: [{% for stat in monthly_stats %}{{ stat.active_riders }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#36b9cc',
                backgroundColor: 'rgba(54, 185, 204, 0.1)',
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});

function viewRiderDetails(riderId) {
    // Open rider details modal
    alert('Viewing details for rider: ' + riderId);
}

function viewRiderHistory(riderId) {
    // Open rider delivery history
    alert('Viewing history for rider: ' + riderId);
}

function exportPerformanceReport() {
    // Export performance report
    alert('Exporting performance report...');
}
</script>
{% endblock %}
{% endblock %}
