{% extends 'base.html' %}

{% block title %}Customer Ledger{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Customer Ledger: {{ customer.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Customer Information</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Customer Code:</strong> {{ customer.code }}</p>
                                    <p><strong>Address:</strong> {{ customer.address }}</p>
                                    <p><strong>Phone:</strong> {{ customer.phone }}</p>
                                    <p><strong>Email:</strong> {{ customer.email }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Account Summary</h6>
                                </div>
                                <div class="card-body">
                                    {% set total_debit = ledger|sum(attribute='debit_amount') %}
                                    {% set total_credit = ledger|sum(attribute='credit_amount') %}
                                    {% set balance = total_debit - total_credit %}

                                    <p><strong>Total Debit:</strong> {{ total_debit|round(2)|format_currency }}</p>
                                    <p><strong>Total Credit:</strong> {{ total_credit|round(2)|format_currency }}</p>
                                    <p><strong>Current Balance:</strong>
                                        <span class="{{ 'text-danger' if balance > 0 else 'text-success' }}">
                                            {{ balance|abs|round(2)|format_currency }} {{ 'DR' if balance > 0 else 'CR' }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-secondary" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </button>
                            <button type="button" class="btn btn-primary float-right" onclick="goBack()">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="ledgerTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Transaction Type</th>
                                    <th>Reference</th>
                                    <th>Debit</th>
                                    <th>Credit</th>
                                    <th>Balance</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if ledger %}
                                    {% set running_balance = 0 %}
                                    {% for entry in ledger %}
                                        {% set running_balance = running_balance + entry.debit_amount - entry.credit_amount %}
                                        <tr>
                                            <td>{{ entry.transaction_date }}</td>
                                            <td>{{ entry.transaction_type }}</td>
                                            <td>{{ entry.reference_id }}</td>
                                            <td>{{ entry.debit_amount|round(2)|format_currency if entry.debit_amount > 0 else '-' }}</td>
                                            <td>{{ entry.credit_amount|round(2)|format_currency if entry.credit_amount > 0 else '-' }}</td>
                                            <td class="{{ 'text-danger' if running_balance > 0 else 'text-success' }}">
                                                {{ running_balance|abs|round(2)|format_currency }} {{ 'DR' if running_balance > 0 else 'CR' }}
                                            </td>
                                            <td>{{ entry.notes }}</td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center">No ledger entries found for this customer</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    function exportToExcel() {
        try {
            // Check if XLSX is loaded
            if (typeof XLSX === 'undefined') {
                alert('Excel export library not loaded. Please refresh the page and try again.');
                return;
            }

            // Create a workbook
            var wb = XLSX.utils.book_new();

            // Get the table data
            var table = document.getElementById('ledgerTable');
            if (!table) {
                alert('Table not found');
                return;
            }

            var ws = XLSX.utils.table_to_sheet(table);

            // Add the worksheet to the workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Customer Ledger');

            // Generate Excel file
            var filename = 'customer_ledger_{{ customer.code }}_{{ now.strftime("%Y%m%d") }}.xlsx';
            XLSX.writeFile(wb, filename);
        } catch (error) {
            console.error('Export error:', error);
            alert('Error exporting to Excel: ' + error.message);
        }
    }
</script>
{% endblock %}
