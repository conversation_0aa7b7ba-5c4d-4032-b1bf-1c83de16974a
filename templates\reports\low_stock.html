{% extends 'base.html' %}

{% block title %}Low Stock Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Low Stock Report</h4>
                    <div>
                        <button class="btn btn-light" id="printLowStockBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('low_stock_report') }}" method="get" class="form-inline">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Threshold</span>
                                    </div>
                                    <input type="number" name="threshold" class="form-control" value="{{ threshold }}" min="1">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Apply</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-exclamation-triangle"></i> Showing items with stock quantity below or equal to {{ threshold }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product ID</th>
                                    <th>Product Name</th>
                                    <th>Strength</th>
                                    <th>Batch #</th>
                                    <th>Warehouse</th>
                                    <th>Current Stock</th>
                                    <th>Min Stock Level</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if low_stock_items %}
                                    {% for item in low_stock_items %}
                                    <tr>
                                        <td>{{ item.product_id }}</td>
                                        <td>{{ item.product_name }}</td>
                                        <td>{{ item.strength }}</td>
                                        <td>{{ item.batch_number }}</td>
                                        <td>{{ item.warehouse_name }}</td>
                                        <td>{{ item.stock_quantity }} {{ item.unit_of_measure }}</td>
                                        <td>{{ item.min_stock_level }}</td>
                                        <td>
                                            {% if item.stock_quantity == 0 %}
                                            <span class="badge badge-danger">Out of Stock</span>
                                            {% elif item.stock_quantity <= item.min_stock_level / 2 %}
                                            <span class="badge badge-danger">Critical</span>
                                            {% elif item.stock_quantity <= item.min_stock_level %}
                                            <span class="badge badge-warning">Low</span>
                                            {% else %}
                                            <span class="badge badge-success">OK</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('new_stock') }}" class="btn btn-sm btn-success">Add Stock</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center">No items found with stock quantity below or equal to {{ threshold }}</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Summary</h5>
                                    <p><strong>Total Low Stock Items:</strong> {{ low_stock_items|length }}</p>
                                    <p><strong>Out of Stock Items:</strong> {{ low_stock_items|selectattr('stock_quantity', 'equalto', 0)|list|length }}</p>
                                    <p><strong>Critical Items:</strong> {{ low_stock_items|selectattr('stock_quantity', 'le', low_stock_items[0].min_stock_level / 2 if low_stock_items else 0)|selectattr('stock_quantity', 'gt', 0)|list|length }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('new_stock') }}" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> Add New Stock
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printLowStockBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}