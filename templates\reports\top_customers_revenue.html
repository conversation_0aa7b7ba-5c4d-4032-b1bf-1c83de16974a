{% extends 'base.html' %}

{% block title %}Top Customers by Revenue{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Top Customers by Revenue</h1>
        <a href="{{ url_for('reports') }}" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Reports
        </a>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ customers|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_revenue|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Average Order Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ average_order_value|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Top Customer Share</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ top_customer_percentage }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Top Customers by Revenue</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Customer Name</th>
                            <th>Total Orders</th>
                            <th>Total Revenue</th>
                            <th>Average Order Value</th>
                            <th>Last Order Date</th>
                            <th>Revenue Share</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>
                                {% if loop.index <= 3 %}
                                <span class="badge badge-{% if loop.index == 1 %}warning{% elif loop.index == 2 %}secondary{% else %}dark{% endif %}">
                                    #{{ loop.index }}
                                </span>
                                {% else %}
                                {{ loop.index }}
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ customer.customer_name }}</strong>
                                {% if loop.index == 1 %}
                                <i class="fas fa-crown text-warning ml-1" title="Top Customer"></i>
                                {% endif %}
                            </td>
                            <td>{{ customer.total_orders }}</td>
                            <td>{{ customer.total_revenue|format_currency }}</td>
                            <td>{{ customer.average_order_value|format_currency }}</td>
                            <td>{{ customer.last_order_date|format_datetime }}</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{% if loop.index <= 5 %}success{% elif loop.index <= 10 %}info{% else %}secondary{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ customer.revenue_percentage }}%"
                                         aria-valuenow="{{ customer.revenue_percentage }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ customer.revenue_percentage }}%
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 3, "desc" ]], // Sort by revenue descending
        "pageLength": 25
    });
});
</script>
{% endblock %}
