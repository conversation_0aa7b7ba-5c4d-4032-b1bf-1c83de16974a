/**
 * python_charts_init.js
 * This file initializes the Python-powered charts on the product performance and sales team pages
 */

// Function to load Python-generated sales team charts
function loadPythonSalesTeamCharts(startDate = null, endDate = null) {
    console.log(`Loading sales team charts for date range: ${startDate} to ${endDate}`);

    // Construct URL with date parameters if provided
    let url = '/python-charts/generate?type=sales_team';
    if (startDate && endDate) {
        url += `&start_date=${startDate}&end_date=${endDate}`;
    }

    // Show spinners while loading
    const spinners = [
        'teamPerformanceSpinner',
        'agentSalesSpinner',
        'agentDivisionHierarchySpinner',
        'salesByAgentSpinner',
        'topAgentDivisionsSpinner',
        'orgChartSpinner'
    ];

    spinners.forEach(spinnerId => {
        const spinner = document.getElementById(spinnerId);
        if (spinner) spinner.style.display = 'inline-block';
    });

    // Fetch chart data
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received sales team chart data:', data);

            // Display team performance chart
            if (data.team_performance && document.getElementById('teamPerformanceContainer')) {
                document.getElementById('teamPerformanceSpinner').style.display = 'none';
                const imgElement = document.getElementById('teamPerformanceImage');
                imgElement.src = data.team_performance;
                imgElement.style.display = 'block';
            }

            // Display agent sales chart
            if (data.agent_sales_pie && document.getElementById('agentSalesContainer')) {
                document.getElementById('agentSalesSpinner').style.display = 'none';
                const imgElement = document.getElementById('agentSalesImage');
                imgElement.src = data.agent_sales_pie;
                imgElement.style.display = 'block';
            }

            // Display agent-division hierarchy chart
            if (data.agent_division_hierarchy && document.getElementById('agentDivisionHierarchyContainer')) {
                document.getElementById('agentDivisionHierarchySpinner').style.display = 'none';
                const imgElement = document.getElementById('agentDivisionHierarchyImage');
                imgElement.src = data.agent_division_hierarchy;
                imgElement.style.display = 'block';
            }

            // Display sales by agent chart
            if (data.agent_sales_pie && document.getElementById('salesByAgentContainer')) {
                document.getElementById('salesByAgentSpinner').style.display = 'none';
                const imgElement = document.getElementById('salesByAgentImage');
                imgElement.src = data.agent_sales_pie;
                imgElement.style.display = 'block';
            }

            // Display top agent's divisions chart
            if (data.top_agent_divisions && document.getElementById('topAgentDivisionsContainer')) {
                document.getElementById('topAgentDivisionsSpinner').style.display = 'none';
                const imgElement = document.getElementById('topAgentDivisionsImage');
                imgElement.src = data.top_agent_divisions;
                imgElement.style.display = 'block';
            }

            // Display organizational structure chart
            if (data.org_structure && document.getElementById('organizationalStructureChart')) {
                document.getElementById('orgChartSpinner').style.display = 'none';
                const imgElement = document.getElementById('orgChartImage');
                imgElement.src = data.org_structure;
                imgElement.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading sales team charts:', error);

            // Hide spinners and show error messages
            spinners.forEach(spinnerId => {
                const spinner = document.getElementById(spinnerId);
                if (spinner) {
                    spinner.style.display = 'none';
                    const container = spinner.closest('div[id$="Container"]');
                    if (container) {
                        container.innerHTML += '<div class="alert alert-danger mt-3">Error loading chart. Please try again later.</div>';
                    }
                }
            });
        });
}

// Function to load Python-generated product charts
function loadPythonProductCharts(startDate = null, endDate = null) {
    console.log(`Loading product charts for date range: ${startDate} to ${endDate}`);

    // Construct URL with date parameters if provided
    let url = '/python-charts/generate?type=product_performance';
    if (startDate && endDate) {
        url += `&start_date=${startDate}&end_date=${endDate}`;
    }

    // Show spinners while loading
    const spinners = [
        'topProductsSpinner',
        'divisionSalesSpinner',
        'hierarchySpinner'
    ];

    spinners.forEach(spinnerId => {
        const spinner = document.getElementById(spinnerId);
        if (spinner) spinner.style.display = 'inline-block';
    });

    // Fetch chart data
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received product chart data:', data);

            // Display top products chart
            if (data.top_products_pie && document.getElementById('topProductsContainer')) {
                document.getElementById('topProductsSpinner').style.display = 'none';
                const imgElement = document.getElementById('topProductsImage');
                imgElement.src = data.top_products_pie;
                imgElement.style.display = 'block';
            }

            // Display division sales chart
            if (data.division_sales_pie && document.getElementById('divisionSalesContainer')) {
                document.getElementById('divisionSalesSpinner').style.display = 'none';
                const imgElement = document.getElementById('divisionSalesImage');
                imgElement.src = data.division_sales_pie;
                imgElement.style.display = 'block';
            }

            // Display division-product hierarchy chart
            if (data.division_product_hierarchy && document.getElementById('hierarchyContainer')) {
                document.getElementById('hierarchySpinner').style.display = 'none';
                const imgElement = document.getElementById('hierarchyImage');
                imgElement.src = data.division_product_hierarchy;
                imgElement.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading product charts:', error);

            // Hide spinners and show error messages
            spinners.forEach(spinnerId => {
                const spinner = document.getElementById(spinnerId);
                if (spinner) {
                    spinner.style.display = 'none';
                    const container = spinner.closest('div[id$="Container"]');
                    if (container) {
                        container.innerHTML += '<div class="alert alert-danger mt-3">Error loading chart. Please try again later.</div>';
                    }
                }
            });
        });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Python-powered charts...');

    // Check if we're on the product performance page with Python charts
    if (document.getElementById('topProductsContainer') || document.getElementById('hierarchyContainer')) {
        console.log('Found product performance Python chart elements, initializing...');
        // Get date range from the form
        const startDateEl = document.getElementById('start_date');
        const endDateEl = document.getElementById('end_date');

        if (startDateEl && endDateEl) {
            const startDate = startDateEl.value;
            const endDate = endDateEl.value;
            console.log(`Loading product charts for date range: ${startDate} to ${endDate}`);

            // Load Python-generated charts with a delay to ensure DOM is ready
            setTimeout(function() {
                loadPythonProductCharts(startDate, endDate);
            }, 500);
        } else {
            console.log('Date range elements not found, using default date range');
            setTimeout(function() {
                loadPythonProductCharts();
            }, 500);
        }
    }

    // Check if we're on the sales team page with Python charts
    if (document.getElementById('teamPerformanceContainer') || document.getElementById('agentSalesContainer') ||
        document.getElementById('agentDivisionHierarchyContainer')) {
        console.log('Found sales team Python chart elements, initializing...');
        // Get date range from the form
        const startDateEl = document.getElementById('start_date');
        const endDateEl = document.getElementById('end_date');

        if (startDateEl && endDateEl) {
            const startDate = startDateEl.value;
            const endDate = endDateEl.value;
            console.log(`Loading sales team charts for date range: ${startDate} to ${endDate}`);

            // Load Python-generated charts with a delay to ensure DOM is ready
            setTimeout(function() {
                loadPythonSalesTeamCharts(startDate, endDate);
            }, 500);
        } else {
            console.log('Date range elements not found, using default date range');
            setTimeout(function() {
                loadPythonSalesTeamCharts();
            }, 500);
        }
    }

    // Check if we're on the product performance page with Chart.js charts
    if (document.getElementById('productSalesChart')) {
        console.log('Found product sales Chart.js element, initializing...');
        setTimeout(initializeProductCharts, 1000); // Increased delay to ensure DOM is fully ready
    }

    // Check if we're on the sales team page with Chart.js charts
    if (document.getElementById('teamPerformanceChart')) {
        console.log('Found team performance Chart.js element, initializing...');
        setTimeout(initializeSalesTeamCharts, 1000); // Increased delay to ensure DOM is fully ready
    }

    // Check if we're on the sales team page with agent sales Chart.js chart
    if (document.getElementById('agentSalesChart')) {
        console.log('Found agent sales Chart.js element, initializing...');
        setTimeout(initializeSalesTeamCharts, 1000); // Increased delay to ensure DOM is fully ready
    }
});

function initializeProductCharts() {
    console.log('Initializing product charts...');

    try {
        // Get date range from the form
        const startDateEl = document.getElementById('start_date');
        const endDateEl = document.getElementById('end_date');

        if (!startDateEl || !endDateEl) {
            console.error('Date range elements not found');
            return;
        }

        const startDate = startDateEl.value;
        const endDate = endDateEl.value;

        console.log(`Date range: ${startDate} to ${endDate}`);

        // Fetch data from the API
        fetch(`/api/data/products?start_date=${startDate}&end_date=${endDate}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Product data received:', data);

                if (!data) {
                    console.error('No data received from API');
                    return;
                }

                // Render the charts
                if (data.top_products_by_sales) {
                    renderProductSalesChart(data.top_products_by_sales);
                }

                if (data.top_products_by_quantity) {
                    renderProductQuantityChart(data.top_products_by_quantity);
                }

                if (data.division_sales) {
                    renderDivisionSalesChart(data.division_sales);
                }

                if (data.division_quantity) {
                    renderDivisionQuantityChart(data.division_quantity);
                }
            })
            .catch(error => {
                console.error('Error fetching product data:', error);
            });
    } catch (error) {
        console.error('Error in initializeProductCharts:', error);
    }
}

function initializeSalesTeamCharts() {
    console.log('Initializing sales team charts...');

    try {
        // Get date range from the form
        const startDateEl = document.getElementById('start_date');
        const endDateEl = document.getElementById('end_date');

        if (!startDateEl || !endDateEl) {
            console.error('Date range elements not found');
            return;
        }

        const startDate = startDateEl.value;
        const endDate = endDateEl.value;

        console.log(`Date range: ${startDate} to ${endDate}`);

        // Fetch data from the API
        fetch(`/api/data/sales-team?start_date=${startDate}&end_date=${endDate}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Sales team data received:', data);

                if (!data) {
                    console.error('No data received from API');
                    return;
                }

                // Render the charts
                if (data.team_performance) {
                    renderTeamPerformanceChart(data.team_performance);
                    renderAgentSalesChart(data.team_performance);
                }

                if (data.division_by_agent) {
                    renderDivisionByAgentChart(data.division_by_agent);
                }
            })
            .catch(error => {
                console.error('Error fetching sales team data:', error);
            });
    } catch (error) {
        console.error('Error in initializeSalesTeamCharts:', error);
    }
}

// Chart rendering functions
function renderProductSalesChart(products) {
    try {
        if (!products || products.length === 0 || !document.getElementById('productSalesChart')) {
            console.log('Cannot render product sales chart - missing data or element');
            return;
        }

        console.log('Rendering product sales chart...');
        const canvas = document.getElementById('productSalesChart');
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Get top 10 products
        const topProducts = products.slice(0, 10);

        // Create labels with product name and strength
        const labels = topProducts.map(p => {
            const strength = p.strength ? ` (${p.strength})` : '';
            return p.product_name + strength;
        });

        // Create the chart
        canvas.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Sales (PKR)',
                    data: topProducts.map(p => p.total_sales),
                    backgroundColor: 'rgba(54, 162, 235, 0.7)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Top Products by Sales'
                    },
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Sales (PKR)'
                        }
                    }
                }
            }
        });

        console.log('Product sales chart rendered successfully');
    } catch (error) {
        console.error('Error rendering product sales chart:', error);
    }
}

function renderProductQuantityChart(products) {
    if (!products || products.length === 0 || !document.getElementById('productQuantityChart')) {
        console.log('Cannot render product quantity chart - missing data or element');
        return;
    }

    console.log('Rendering product quantity chart...');
    const ctx = document.getElementById('productQuantityChart').getContext('2d');

    // Get top 10 products
    const topProducts = products.slice(0, 10);

    // Create labels with product name and strength
    const labels = topProducts.map(p => {
        const strength = p.strength ? ` (${p.strength})` : '';
        return p.product_name + strength;
    });

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Quantity Sold',
                data: topProducts.map(p => p.total_quantity),
                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Top Products by Quantity'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity Sold'
                    }
                }
            }
        }
    });
}

function renderDivisionSalesChart(divisions) {
    if (!divisions || divisions.length === 0 || !document.getElementById('divisionSalesChart')) {
        console.log('Cannot render division sales chart - missing data or element');
        return;
    }

    console.log('Rendering division sales chart...');
    const ctx = document.getElementById('divisionSalesChart').getContext('2d');

    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: divisions.map(d => d.division_name),
            datasets: [{
                data: divisions.map(d => d.total_sales),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(199, 199, 199, 0.7)',
                    'rgba(83, 102, 255, 0.7)',
                    'rgba(40, 159, 64, 0.7)',
                    'rgba(210, 199, 199, 0.7)'
                ],
                borderColor: 'white',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Sales by Division'
                },
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

function renderDivisionQuantityChart(divisions) {
    if (!divisions || divisions.length === 0 || !document.getElementById('divisionQuantityChart')) {
        console.log('Cannot render division quantity chart - missing data or element');
        return;
    }

    console.log('Rendering division quantity chart...');
    const ctx = document.getElementById('divisionQuantityChart').getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: divisions.map(d => d.division_name),
            datasets: [{
                data: divisions.map(d => d.total_quantity),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(199, 199, 199, 0.7)',
                    'rgba(83, 102, 255, 0.7)',
                    'rgba(40, 159, 64, 0.7)',
                    'rgba(210, 199, 199, 0.7)'
                ],
                borderColor: 'white',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Quantity by Division'
                },
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

// Sales Team Chart Functions
function renderTeamPerformanceChart(teamData) {
    try {
        if (!teamData || teamData.length === 0 || !document.getElementById('teamPerformanceChart')) {
            console.log('Cannot render team performance chart - missing data or element');
            return;
        }

        console.log('Rendering team performance chart with data:', teamData);
        const canvas = document.getElementById('teamPerformanceChart');
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Get top 10 agents by performance score or total sales if performance score is not available
        const topAgents = teamData.slice(0, 10);

        // Determine which field to use for agent name (sales_agent or agent_name)
        const nameField = topAgents[0].agent_name !== undefined ? 'agent_name' : 'sales_agent';
        console.log(`Using ${nameField} for agent names`);

        // Determine which metric to display
        const hasPerformanceScore = topAgents[0].performance_score !== undefined;
        const metricField = hasPerformanceScore ? 'performance_score' : 'total_sales';
        const metricLabel = hasPerformanceScore ? 'Performance Score' : 'Total Sales (PKR)';

        console.log(`Using ${metricField} for team performance metric`);

        // Create the chart
        canvas.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: topAgents.map(a => a[nameField]),
                datasets: [{
                    label: metricLabel,
                    data: topAgents.map(a => a[metricField] || 0),
                    backgroundColor: 'rgba(75, 192, 192, 0.7)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Team Performance Metrics'
                    },
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (metricField === 'total_sales') {
                                    label += new Intl.NumberFormat('en-PK', {
                                        style: 'currency',
                                        currency: 'PKR',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(context.parsed.y);
                                } else {
                                    label += context.parsed.y.toFixed(2);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: metricLabel
                        },
                        max: hasPerformanceScore ? 100 : undefined
                    }
                }
            }
        });

        console.log('Team performance chart rendered successfully');
    } catch (error) {
        console.error('Error rendering team performance chart:', error);
    }
}

function renderAgentSalesChart(teamData) {
    try {
        if (!teamData || teamData.length === 0 || !document.getElementById('agentSalesChart')) {
            console.log('Cannot render agent sales chart - missing data or element');
            return;
        }

        console.log('Rendering agent sales chart with data:', teamData);
        const canvas = document.getElementById('agentSalesChart');
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Get top 10 agents by sales
        const topAgents = teamData.slice(0, 10);

        // Determine which field to use for agent name (sales_agent or agent_name)
        const nameField = topAgents[0].agent_name !== undefined ? 'agent_name' : 'sales_agent';
        console.log(`Using ${nameField} for agent names in sales chart`);

        // Create the chart
        canvas.chart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: topAgents.map(a => a[nameField]),
                datasets: [{
                    data: topAgents.map(a => a.total_sales),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)',
                        'rgba(83, 102, 255, 0.7)',
                        'rgba(40, 159, 64, 0.7)',
                        'rgba(210, 199, 199, 0.7)'
                    ],
                    borderColor: 'white',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Sales Distribution by Agent'
                    },
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const percentage = ((value / topAgents.reduce((sum, agent) => sum + agent.total_sales, 0)) * 100).toFixed(1);
                                return `${label}: ${new Intl.NumberFormat('en-PK', {
                                    style: 'currency',
                                    currency: 'PKR',
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                }).format(value)} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        console.log('Agent sales chart rendered successfully');
    } catch (error) {
        console.error('Error rendering agent sales chart:', error);
    }
}

function renderDivisionByAgentChart(divisionData) {
    try {
        if (!divisionData || divisionData.length === 0 || !document.getElementById('divisionByAgentChart')) {
            console.log('Cannot render division by agent chart - missing data or element');
            return;
        }

        console.log('Rendering division by agent chart with data:', divisionData);
        const canvas = document.getElementById('divisionByAgentChart');
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Determine which field to use for agent name (sales_agent or agent_name)
        const nameField = divisionData[0].agent_name !== undefined ? 'agent_name' : 'sales_agent';

        // Group data by agent
        const agentGroups = {};
        divisionData.forEach(item => {
            const agent = item[nameField];
            if (!agentGroups[agent]) {
                agentGroups[agent] = [];
            }
            agentGroups[agent].push(item);
        });

        // Get the agent with the most sales
        let topAgent = '';
        let maxSales = 0;
        Object.keys(agentGroups).forEach(agent => {
            const totalSales = agentGroups[agent].reduce((sum, item) => sum + item.total_sales, 0);
            if (totalSales > maxSales) {
                maxSales = totalSales;
                topAgent = agent;
            }
        });

        // Create datasets for the top agent's division sales
        if (topAgent && document.getElementById('topAgentDivisionsChart')) {
            const topAgentCanvas = document.getElementById('topAgentDivisionsChart');
            const topAgentCtx = topAgentCanvas.getContext('2d');

            // Clear any existing chart
            if (topAgentCanvas.chart) {
                topAgentCanvas.chart.destroy();
            }

            const topAgentData = agentGroups[topAgent];

            topAgentCanvas.chart = new Chart(topAgentCtx, {
                type: 'doughnut',
                data: {
                    labels: topAgentData.map(d => d.division_name),
                    datasets: [{
                        data: topAgentData.map(d => d.total_sales),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderColor: 'white',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${topAgent}'s Division Sales`
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = ((value / topAgentData.reduce((sum, item) => sum + item.total_sales, 0)) * 100).toFixed(1);
                                    return `${label}: ${new Intl.NumberFormat('en-PK', {
                                        style: 'currency',
                                        currency: 'PKR',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create a nested chart showing agent-division hierarchy
        if (document.getElementById('agentDivisionHierarchyChart')) {
            const hierarchyCanvas = document.getElementById('agentDivisionHierarchyChart');
            const hierarchyCtx = hierarchyCanvas.getContext('2d');

            // Clear any existing chart
            if (hierarchyCanvas.chart) {
                hierarchyCanvas.chart.destroy();
            }

            // Create a treemap-like visualization using a bar chart
            const agents = Object.keys(agentGroups);
            const datasets = [];

            // Create a dataset for each division
            const allDivisions = [...new Set(divisionData.map(item => item.division_name))];

            allDivisions.forEach((division, index) => {
                const data = [];
                agents.forEach(agent => {
                    const divisionItems = agentGroups[agent].filter(item => item.division_name === division);
                    data.push(divisionItems.length > 0 ? divisionItems[0].total_sales : 0);
                });

                datasets.push({
                    label: division,
                    data: data,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ][index % 6],
                    borderColor: 'white',
                    borderWidth: 1
                });
            });

            hierarchyCanvas.chart = new Chart(hierarchyCtx, {
                type: 'bar',
                data: {
                    labels: agents,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Agent-Division Hierarchy (Nested Analysis)'
                        },
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw;
                                    return `${label}: ${new Intl.NumberFormat('en-PK', {
                                        style: 'currency',
                                        currency: 'PKR',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Sales Agents'
                            }
                        },
                        y: {
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Sales (PKR)'
                            },
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Create a nested donut chart for sales by agent
        if (document.getElementById('salesByAgentNestedDonut')) {
            const nestedDonutCanvas = document.getElementById('salesByAgentNestedDonut');
            const nestedDonutCtx = nestedDonutCanvas.getContext('2d');

            // Clear any existing chart
            if (nestedDonutCanvas.chart) {
                nestedDonutCanvas.chart.destroy();
            }

            // Create data for the nested donut chart
            const agents = Object.keys(agentGroups);
            const agentTotals = agents.map(agent => {
                return {
                    agent: agent,
                    total: agentGroups[agent].reduce((sum, item) => sum + item.total_sales, 0)
                };
            });

            // Sort by total sales
            agentTotals.sort((a, b) => b.total - a.total);

            nestedDonutCanvas.chart = new Chart(nestedDonutCtx, {
                type: 'doughnut',
                data: {
                    labels: agentTotals.map(item => item.agent),
                    datasets: [{
                        data: agentTotals.map(item => item.total),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)'
                        ],
                        borderColor: 'white',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sales by Agent (Nested Donut)'
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = ((value / agentTotals.reduce((sum, item) => sum + item.total, 0)) * 100).toFixed(1);
                                    return `${label}: ${new Intl.NumberFormat('en-PK', {
                                        style: 'currency',
                                        currency: 'PKR',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create a nested donut chart for top agent's divisions
        if (document.getElementById('topAgentDivisionsNestedDonut') && topAgent) {
            const topAgentNestedCanvas = document.getElementById('topAgentDivisionsNestedDonut');
            const topAgentNestedCtx = topAgentNestedCanvas.getContext('2d');

            // Clear any existing chart
            if (topAgentNestedCanvas.chart) {
                topAgentNestedCanvas.chart.destroy();
            }

            const topAgentData = agentGroups[topAgent];

            topAgentNestedCanvas.chart = new Chart(topAgentNestedCtx, {
                type: 'doughnut',
                data: {
                    labels: topAgentData.map(d => d.division_name),
                    datasets: [{
                        data: topAgentData.map(d => d.total_sales),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderColor: 'white',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${topAgent}'s Division Sales (Nested Donut)`
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = ((value / topAgentData.reduce((sum, item) => sum + item.total_sales, 0)) * 100).toFixed(1);
                                    return `${label}: ${new Intl.NumberFormat('en-PK', {
                                        style: 'currency',
                                        currency: 'PKR',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create a summary chart for sales team performance
        if (document.getElementById('salesTeamPerformanceMetrics')) {
            const summaryCanvas = document.getElementById('salesTeamPerformanceMetrics');
            const summaryCtx = summaryCanvas.getContext('2d');

            // Clear any existing chart
            if (summaryCanvas.chart) {
                summaryCanvas.chart.destroy();
            }

            // Calculate total sales by division
            const divisionTotals = {};
            divisionData.forEach(item => {
                const division = item.division_name;
                if (!divisionTotals[division]) {
                    divisionTotals[division] = 0;
                }
                divisionTotals[division] += item.total_sales;
            });

            const divisions = Object.keys(divisionTotals);

            summaryCanvas.chart = new Chart(summaryCtx, {
                type: 'bar',
                data: {
                    labels: divisions,
                    datasets: [{
                        label: 'Sales by Division',
                        data: divisions.map(div => divisionTotals[div]),
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sales Team Performance Metrics'
                        },
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw;
                                    const percentage = ((value / Object.values(divisionTotals).reduce((sum, val) => sum + val, 0)) * 100).toFixed(1);
                                    return `${label}: ${new Intl.NumberFormat('en-PK', {
                                        style: 'currency',
                                        currency: 'PKR',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value)} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Sales (PKR)'
                            }
                        }
                    }
                }
            });
        }

        console.log('Division by agent charts rendered successfully');
    } catch (error) {
        console.error('Error rendering division by agent charts:', error);
    }
}
