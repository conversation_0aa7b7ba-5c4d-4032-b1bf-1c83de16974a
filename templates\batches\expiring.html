{% extends 'base.html' %}

{% block title %}Expiring Batches{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Expiring Batches (Next 90 Days)</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> View All Batches
                            </a>
                            <a href="{{ url_for('batches', view='by_product') }}" class="btn btn-secondary">
                                <i class="fas fa-pills"></i> View Batches by Product
                            </a>
                            <a href="{{ url_for('batches', view='details') }}" class="btn btn-secondary">
                                <i class="fas fa-info-circle"></i> View Batch Details
                            </a>
                            <a href="{{ url_for('batches', view='movements') }}" class="btn btn-secondary">
                                <i class="fas fa-exchange-alt"></i> View Batch Movements
                            </a>
                            <a href="{{ url_for('batches', view='deliveries') }}" class="btn btn-secondary">
                                <i class="fas fa-truck"></i> View Batch Deliveries
                            </a>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Batch ID</th>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Batch Number</th>
                                    <th>Quantity</th>
                                    <th>Expiry Date</th>
                                    <th>Days Left</th>
                                    <th>Location</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if batches %}
                                    {% for batch in batches %}
                                    {% set days_left = 30 %}
                                    <tr class="{% if days_left <= 30 %}table-danger{% elif days_left <= 60 %}table-warning{% else %}table-info{% endif %}">
                                        <td>{{ batch.batch_id }}</td>
                                        <td>{{ batch.product_name }}</td>
                                        <td>{{ batch.strength }}</td>
                                        <td>{{ batch.batch_number }}</td>
                                        <td>{{ batch.quantity }}</td>
                                        <td>{{ batch.expiry_date }}</td>
                                        <td>
                                            <span class="badge 
                                                {% if days_left <= 30 %}badge-danger
                                                {% elif days_left <= 60 %}badge-warning
                                                {% else %}badge-info{% endif %}">
                                                {{ days_left }} days
                                            </span>
                                        </td>
                                        <td>{{ batch.location }}</td>
                                        <td>
                                            <a href="{{ url_for('batches', view='details', batch_id=batch.batch_id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-info-circle"></i> Details
                                            </a>
                                            <a href="{{ url_for('batches', view='movements', batch_id=batch.batch_id) }}" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-exchange-alt"></i> Movements
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center">No expiring batches found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
