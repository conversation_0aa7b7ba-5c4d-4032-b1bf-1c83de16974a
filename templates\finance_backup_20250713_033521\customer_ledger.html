{% extends 'base.html' %}

{% block title %}Finance - Customer Ledger{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Customer Ledger</h1>
        <a href="{{ url_for('finance') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Finance
        </a>
    </div>

    <!-- Customer Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ customers|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₨{{ "{:,.2f}".format(customers|sum(attribute='total_amount')|default(0)) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Search -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Select Customer for Detailed Ledger</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <select class="form-control" id="customerSelect" onchange="loadCustomerLedger()">
                        <option value="">Select a customer to view detailed ledger...</option>
                        {% for customer in customers %}
                        <option value="{{ customer.customer_name }}">
                            {{ customer.customer_name }} - ₨{{ "{:,.2f}".format(customer.total_amount) }} ({{ customer.order_count }} orders)
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary" onclick="loadCustomerLedger()">
                        <i class="fas fa-search"></i> View Ledger
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Account Summary -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Customer Account Summary</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Customer Name</th>
                            <th>Total Orders</th>
                            <th>Debit (Sales)</th>
                            <th>Credit (Payments)</th>
                            <th>Balance</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>{{ customer.customer_name }}</td>
                            <td>{{ customer.order_count }}</td>
                            <td class="text-danger">₨{{ "{:,.2f}".format(customer.total_amount) }}</td>
                            <td class="text-success">₨{{ "{:,.2f}".format(customer.total_amount * 0.7) }}</td>
                            <td class="{% if customer.total_amount * 0.3 > 0 %}text-warning{% else %}text-success{% endif %}">
                                ₨{{ "{:,.2f}".format(customer.total_amount * 0.3) }}
                            </td>
                            <td>
                                {% if customer.total_amount * 0.3 > 0 %}
                                <span class="badge badge-warning">Outstanding</span>
                                {% else %}
                                <span class="badge badge-success">Paid</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="viewCustomerLedger('{{ customer.customer_name }}')">
                                    <i class="fas fa-book"></i> Ledger
                                </button>
                                <a href="{{ url_for('customer_purchase_history') }}?customer={{ customer.customer_name }}"
                                   class="btn btn-sm btn-secondary">
                                    <i class="fas fa-history"></i> History
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">No customer data found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Detailed Customer Ledger (Hidden by default) -->
    <div class="card shadow mb-4" id="detailedLedger" style="display: none;">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">Customer Ledger - <span id="selectedCustomerName"></span></h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="ledgerTable">
                    <thead class="thead-dark">
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Invoice #</th>
                            <th>Debit</th>
                            <th>Credit</th>
                            <th>Balance</th>
                        </tr>
                    </thead>
                    <tbody id="ledgerTableBody">
                        <!-- Ledger entries will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 4, "desc" ]], // Sort by balance descending
        "pageLength": 25
    });
});

function viewCustomerLedger(customerName) {
    // Set customer name in the header
    document.getElementById('selectedCustomerName').textContent = customerName;

    // Show the detailed ledger section
    document.getElementById('detailedLedger').style.display = 'block';

    // Scroll to the ledger section
    document.getElementById('detailedLedger').scrollIntoView({ behavior: 'smooth' });

    // Load customer ledger data
    loadCustomerLedgerData(customerName);
}

function loadCustomerLedger() {
    const customerSelect = document.getElementById('customerSelect');
    const selectedCustomer = customerSelect.value;

    if (selectedCustomer) {
        viewCustomerLedger(selectedCustomer);
    } else {
        alert('Please select a customer first');
    }
}

function loadCustomerLedgerData(customerName) {
    // Show loading message
    const tableBody = document.getElementById('ledgerTableBody');
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading ledger data...</td></tr>';

    // Fetch customer ledger data
    fetch(`/finance/customer_ledger_data?customer=${encodeURIComponent(customerName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayLedgerData(data.ledger_entries);
            } else {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading ledger data</td></tr>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Display empty state when no data available
            const tableBody = document.getElementById('ledgerTableBody');
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No ledger data available</td></tr>';
        });
}

function displayLedgerData(ledgerEntries) {
    const tableBody = document.getElementById('ledgerTableBody');

    if (ledgerEntries.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No ledger entries found</td></tr>';
        return;
    }

    let html = '';
    ledgerEntries.forEach(entry => {
        const debitAmount = entry.debit > 0 ? `₨${entry.debit.toLocaleString('en-US', {minimumFractionDigits: 2})}` : '-';
        const creditAmount = entry.credit > 0 ? `₨${entry.credit.toLocaleString('en-US', {minimumFractionDigits: 2})}` : '-';
        const balanceAmount = `₨${entry.balance.toLocaleString('en-US', {minimumFractionDigits: 2})}`;

        html += `
            <tr>
                <td>${entry.date}</td>
                <td>${entry.description}</td>
                <td>${entry.invoice_number}</td>
                <td class="text-danger">${debitAmount}</td>
                <td class="text-success">${creditAmount}</td>
                <td class="font-weight-bold ${entry.balance > 0 ? 'text-warning' : 'text-success'}">${balanceAmount}</td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
}
</script>
{% endblock %}
