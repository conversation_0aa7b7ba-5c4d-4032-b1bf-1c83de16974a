#!/usr/bin/env python3
"""
Division Validation Utilities
Comprehensive validation system for division management integration
"""

import sqlite3
from typing import List, Dict, Optional, Tuple
from datetime import datetime


class DivisionValidator:
    """Comprehensive division validation and management utilities"""
    
    def __init__(self, db_connection):
        self.db = db_connection
        
    def get_active_divisions(self) -> List[Dict]:
        """Get all active divisions from the database"""
        try:
            cursor = self.db.execute('''
                SELECT division_id, name, category, description, manager, 
                       status, budget, contact_email, contact_phone, location,
                       created_at, updated_at
                FROM divisions 
                WHERE status = 'Active' 
                ORDER BY name
            ''')
            
            divisions = []
            for row in cursor.fetchall():
                divisions.append({
                    'division_id': row[0],
                    'name': row[1],
                    'category': row[2] or 'General',
                    'description': row[3] or '',
                    'manager': row[4] or '',
                    'status': row[5],
                    'budget': row[6],
                    'contact_email': row[7] or '',
                    'contact_phone': row[8] or '',
                    'location': row[9] or '',
                    'created_at': row[10],
                    'updated_at': row[11]
                })
            
            return divisions
            
        except Exception as e:
            print(f"Error fetching active divisions: {e}")
            return []
    
    def validate_division_exists(self, division_id: int) -> Tuple[bool, Optional[Dict]]:
        """Validate that a division exists and is active"""
        try:
            cursor = self.db.execute('''
                SELECT division_id, name, status, manager
                FROM divisions 
                WHERE division_id = ? AND status = 'Active'
            ''', (division_id,))
            
            row = cursor.fetchone()
            if row:
                return True, {
                    'division_id': row[0],
                    'name': row[1],
                    'status': row[2],
                    'manager': row[3]
                }
            else:
                return False, None
                
        except Exception as e:
            print(f"Error validating division {division_id}: {e}")
            return False, None
    
    def validate_division_by_name(self, division_name: str) -> Tuple[bool, Optional[Dict]]:
        """Validate division by name"""
        try:
            cursor = self.db.execute('''
                SELECT division_id, name, status, manager
                FROM divisions 
                WHERE LOWER(name) = LOWER(?) AND status = 'Active'
            ''', (division_name,))
            
            row = cursor.fetchone()
            if row:
                return True, {
                    'division_id': row[0],
                    'name': row[1],
                    'status': row[2],
                    'manager': row[3]
                }
            else:
                return False, None
                
        except Exception as e:
            print(f"Error validating division by name '{division_name}': {e}")
            return False, None
    
    def get_division_products_count(self, division_id: int) -> int:
        """Get count of products associated with a division"""
        try:
            cursor = self.db.execute('''
                SELECT COUNT(*) FROM products 
                WHERE division_id = ?
            ''', (division_id,))
            
            return cursor.fetchone()[0]
            
        except Exception as e:
            print(f"Error counting products for division {division_id}: {e}")
            return 0
    
    def get_orphaned_products(self) -> List[Dict]:
        """Find products with invalid or missing division references"""
        try:
            cursor = self.db.execute('''
                SELECT p.product_id, p.name, p.division_id
                FROM products p
                LEFT JOIN divisions d ON p.division_id = d.division_id
                WHERE p.division_id IS NULL 
                   OR d.division_id IS NULL 
                   OR d.status != 'Active'
                ORDER BY p.name
            ''')
            
            orphaned = []
            for row in cursor.fetchall():
                orphaned.append({
                    'product_id': row[0],
                    'product_name': row[1],
                    'division_id': row[2],
                    'issue': 'Missing division' if row[2] is None else 'Invalid division reference'
                })
            
            return orphaned
            
        except Exception as e:
            print(f"Error finding orphaned products: {e}")
            return []
    
    def fix_orphaned_products(self, default_division_id: int) -> Tuple[bool, int]:
        """Fix orphaned products by assigning them to a default division"""
        try:
            # First validate the default division exists
            is_valid, division_info = self.validate_division_exists(default_division_id)
            if not is_valid:
                return False, 0
            
            # Update orphaned products
            cursor = self.db.execute('''
                UPDATE products 
                SET division_id = ?, updated_at = ?, updated_by = 'system'
                WHERE division_id IS NULL 
                   OR division_id NOT IN (
                       SELECT division_id FROM divisions WHERE status = 'Active'
                   )
            ''', (default_division_id, datetime.now()))
            
            updated_count = cursor.rowcount
            self.db.commit()
            
            return True, updated_count
            
        except Exception as e:
            print(f"Error fixing orphaned products: {e}")
            self.db.rollback()
            return False, 0
    
    def create_default_division_if_missing(self) -> Tuple[bool, Optional[int]]:
        """Create a default 'General' division if no divisions exist"""
        try:
            # Check if any divisions exist
            cursor = self.db.execute('SELECT COUNT(*) FROM divisions')
            division_count = cursor.fetchone()[0]
            
            if division_count == 0:
                # Create default division
                cursor = self.db.execute('''
                    INSERT INTO divisions (
                        name, category, description, manager, status, 
                        created_at, updated_at, created_by, updated_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    'General Division',
                    'General',
                    'Default division for products without specific division assignment',
                    'System Administrator',
                    'Active',
                    datetime.now(),
                    datetime.now(),
                    'system',
                    'system'
                ))
                
                division_id = cursor.lastrowid
                self.db.commit()
                
                return True, division_id
            else:
                # Return the first active division
                cursor = self.db.execute('''
                    SELECT division_id FROM divisions 
                    WHERE status = 'Active' 
                    ORDER BY division_id 
                    LIMIT 1
                ''')
                row = cursor.fetchone()
                return True, row[0] if row else None
                
        except Exception as e:
            print(f"Error creating default division: {e}")
            self.db.rollback()
            return False, None
    
    def validate_division_for_product_creation(self, division_id: int) -> Tuple[bool, str]:
        """Validate division for product creation with detailed error message"""
        if not division_id:
            return False, "Division ID is required for product creation"
        
        is_valid, division_info = self.validate_division_exists(division_id)
        
        if not is_valid:
            return False, f"Division ID {division_id} does not exist or is not active"
        
        return True, f"Division '{division_info['name']}' is valid for product creation"
    
    def get_division_summary(self) -> Dict:
        """Get comprehensive division system summary"""
        try:
            # Count active divisions
            cursor = self.db.execute('SELECT COUNT(*) FROM divisions WHERE status = "Active"')
            active_divisions = cursor.fetchone()[0]
            
            # Count total divisions
            cursor = self.db.execute('SELECT COUNT(*) FROM divisions')
            total_divisions = cursor.fetchone()[0]
            
            # Count products with valid divisions
            cursor = self.db.execute('''
                SELECT COUNT(*) FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE d.status = 'Active'
            ''')
            products_with_valid_divisions = cursor.fetchone()[0]
            
            # Count orphaned products
            orphaned_products = len(self.get_orphaned_products())
            
            return {
                'active_divisions': active_divisions,
                'total_divisions': total_divisions,
                'products_with_valid_divisions': products_with_valid_divisions,
                'orphaned_products': orphaned_products,
                'system_health': 'Good' if orphaned_products == 0 else 'Needs Attention'
            }
            
        except Exception as e:
            print(f"Error generating division summary: {e}")
            return {
                'active_divisions': 0,
                'total_divisions': 0,
                'products_with_valid_divisions': 0,
                'orphaned_products': 0,
                'system_health': 'Error'
            }


def get_division_validator(db_connection):
    """Factory function to create division validator instance"""
    return DivisionValidator(db_connection)


# Utility functions for Flask routes
def validate_division_for_route(db, division_id):
    """Quick validation function for Flask routes"""
    validator = DivisionValidator(db)
    return validator.validate_division_exists(division_id)


def get_active_divisions_for_forms(db):
    """Get active divisions formatted for form dropdowns"""
    validator = DivisionValidator(db)
    divisions = validator.get_active_divisions()
    
    return [
        {
            'id': div['division_id'],
            'name': div['name'],
            'display_name': f"{div['name']} ({div['category']})"
        }
        for div in divisions
    ]
