"""
Permission API Routes for Medivent Pharmaceuticals Web Portal
This module provides API endpoints for permission management
"""

from flask import Blueprint, request, jsonify, g, current_app
from flask_login import login_required, current_user
from utils.db import get_db
from utils.permissions import has_permission
from utils.permission_audit import log_permission_change
from utils.permission_groups import get_permission_groups, get_role_templates, get_template_permissions

# Create blueprint
permission_api = Blueprint('permission_api', __name__)

@permission_api.route('/users/roles/<role>/permissions', methods=['GET'])
@login_required
def get_role_permissions(role):
    """Get permissions for a specific role"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    db = get_db()
    
    # Get all permissions for the role
    permissions = []
    cursor = db.execute('''
        SELECT p.permission_code
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE rp.role = ?
    ''', (role,))
    
    for row in cursor:
        permissions.append(row['permission_code'])
    
    return jsonify({
        'success': True,
        'role': role,
        'permissions': permissions
    })

@permission_api.route('/users/roles/<role>/permissions', methods=['POST'])
@login_required
def update_role_permissions(role):
    """Update permissions for a specific role"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    if role == 'admin':
        return jsonify({'success': False, 'message': 'Cannot modify admin permissions'}), 400
    
    db = get_db()
    
    # Get the permissions from the request
    permissions = request.form.getlist('permissions')
    
    # Get current permissions for audit logging
    current_permissions = {}
    cursor = db.execute('''
        SELECT p.permission_id, p.permission_code, p.permission_name
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE rp.role = ?
    ''', (role,))
    
    for row in cursor:
        current_permissions[row['permission_code']] = {
            'id': row['permission_id'],
            'name': row['permission_name']
        }
    
    # Get all permissions for reference
    all_permissions = {}
    cursor = db.execute('SELECT permission_id, permission_code, permission_name FROM permissions')
    for row in cursor:
        all_permissions[row['permission_code']] = {
            'id': row['permission_id'],
            'name': row['permission_name']
        }
    
    try:
        # Start a transaction
        db.execute('BEGIN TRANSACTION')
        
        # Remove all current permissions for the role
        db.execute('DELETE FROM role_permissions WHERE role = ?', (role,))
        
        # Add the new permissions
        for permission_id in permissions:
            db.execute('''
                INSERT INTO role_permissions (role, permission_id)
                VALUES (?, ?)
            ''', (role, permission_id))
        
        # Log permission changes
        for permission_code, permission_data in current_permissions.items():
            if permission_data['id'] not in permissions:
                # Permission was removed
                log_permission_change(
                    role=role,
                    permission_code=permission_code,
                    permission_name=permission_data['name'],
                    action='removed',
                    previous_state='granted',
                    new_state='revoked'
                )
        
        for permission_id in permissions:
            for permission_code, permission_data in all_permissions.items():
                if permission_data['id'] == int(permission_id) and permission_code not in current_permissions:
                    # Permission was added
                    log_permission_change(
                        role=role,
                        permission_code=permission_code,
                        permission_name=permission_data['name'],
                        action='added',
                        previous_state='revoked',
                        new_state='granted'
                    )
        
        # Commit the transaction
        db.execute('COMMIT')
        
        return jsonify({'success': True})
    
    except Exception as e:
        # Rollback the transaction on error
        db.execute('ROLLBACK')
        current_app.logger.error(f"Error updating permissions: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@permission_api.route('/users/roles/<role>/apply-template', methods=['POST'])
@login_required
def apply_role_template(role):
    """Apply a template to a role"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    if role == 'admin':
        return jsonify({'success': False, 'message': 'Cannot modify admin permissions'}), 400
    
    template = request.form.get('template')
    if not template:
        return jsonify({'success': False, 'message': 'Template not specified'}), 400
    
    # Get template permissions
    template_permissions = get_template_permissions(template)
    if not template_permissions:
        return jsonify({'success': False, 'message': 'Invalid template'}), 400
    
    db = get_db()
    
    # Get current permissions for audit logging
    current_permissions = {}
    cursor = db.execute('''
        SELECT p.permission_id, p.permission_code, p.permission_name
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE rp.role = ?
    ''', (role,))
    
    for row in cursor:
        current_permissions[row['permission_code']] = {
            'id': row['permission_id'],
            'name': row['permission_name']
        }
    
    # Get all permissions for reference
    all_permissions = {}
    cursor = db.execute('SELECT permission_id, permission_code, permission_name FROM permissions')
    for row in cursor:
        all_permissions[row['permission_code']] = {
            'id': row['permission_id'],
            'name': row['permission_name']
        }
    
    try:
        # Start a transaction
        db.execute('BEGIN TRANSACTION')
        
        # Remove all current permissions for the role
        db.execute('DELETE FROM role_permissions WHERE role = ?', (role,))
        
        # Add the template permissions
        for permission_code in template_permissions:
            if permission_code in all_permissions:
                db.execute('''
                    INSERT INTO role_permissions (role, permission_id)
                    VALUES (?, ?)
                ''', (role, all_permissions[permission_code]['id']))
        
        # Log permission changes
        log_permission_change(
            role=role,
            permission_code='template',
            permission_name=f"{template} template",
            action='applied',
            previous_state=f"{len(current_permissions)} permissions",
            new_state=f"{len(template_permissions)} permissions"
        )
        
        # Commit the transaction
        db.execute('COMMIT')
        
        return jsonify({'success': True})
    
    except Exception as e:
        # Rollback the transaction on error
        db.execute('ROLLBACK')
        current_app.logger.error(f"Error applying template: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@permission_api.route('/users/templates/<template>/permissions', methods=['GET'])
@login_required
def get_template_permissions_api(template):
    """Get permissions for a specific template"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    # Get template permissions
    template_permissions = get_template_permissions(template)
    if not template_permissions:
        return jsonify({'success': False, 'message': 'Invalid template'}), 400
    
    # Get permission groups
    permission_groups = get_permission_groups()
    
    # Organize permissions by group
    grouped_permissions = {}
    for group_code, group in permission_groups.items():
        grouped_permissions[group['name']] = []
        for permission in group['permissions']:
            if permission['code'] in template_permissions:
                grouped_permissions[group['name']].append({
                    'code': permission['code'],
                    'name': permission['name'],
                    'description': permission['description']
                })
    
    return jsonify({
        'success': True,
        'template': template,
        'permissions': grouped_permissions
    })

@permission_api.route('/users/permissions/matrix', methods=['GET'])
@login_required
def get_permission_matrix():
    """Get the permission matrix for all roles"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    db = get_db()
    
    # Get all permissions
    permissions = []
    cursor = db.execute('SELECT permission_id, permission_code, permission_name FROM permissions ORDER BY permission_name')
    for row in cursor:
        permissions.append({
            'id': row['permission_id'],
            'code': row['permission_code'],
            'name': row['permission_name']
        })
    
    # Get permissions for each role
    roles = ['admin', 'manager', 'sales', 'warehouse', 'rider', 'user']
    matrix = {}
    
    for role in roles:
        matrix[role] = []
        cursor = db.execute('''
            SELECT p.permission_code
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.permission_id
            WHERE rp.role = ?
        ''', (role,))
        
        for row in cursor:
            matrix[role].append(row['permission_code'])
    
    return jsonify({
        'success': True,
        'permissions': permissions,
        'matrix': matrix
    })

@permission_api.route('/users/roles/<role>/widget-permissions', methods=['POST'])
@login_required
def update_widget_permissions(role):
    """Update dashboard widget permissions for a specific role"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    if role == 'admin':
        return jsonify({'success': False, 'message': 'Cannot modify admin permissions'}), 400
    
    db = get_db()
    
    # Get the widget permissions from the request
    widget_permissions = request.form.getlist('permissions')
    
    # Get current widget permissions for audit logging
    current_widget_permissions = {}
    widget_permission_codes = ['dashboard_orders_widget', 'dashboard_inventory_widget', 'dashboard_workflow_widget']
    
    cursor = db.execute('''
        SELECT p.permission_id, p.permission_code, p.permission_name
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE rp.role = ? AND p.permission_code IN (?, ?, ?)
    ''', (role, *widget_permission_codes))
    
    for row in cursor:
        current_widget_permissions[row['permission_code']] = {
            'id': row['permission_id'],
            'name': row['permission_name']
        }
    
    # Get all permissions for reference
    all_permissions = {}
    cursor = db.execute('SELECT permission_id, permission_code, permission_name FROM permissions')
    for row in cursor:
        all_permissions[row['permission_code']] = {
            'id': row['permission_id'],
            'name': row['permission_name']
        }
    
    try:
        # Start a transaction
        db.execute('BEGIN TRANSACTION')
        
        # Remove all current widget permissions for the role
        for widget_code in widget_permission_codes:
            if widget_code in all_permissions:
                db.execute('''
                    DELETE FROM role_permissions 
                    WHERE role = ? AND permission_id = ?
                ''', (role, all_permissions[widget_code]['id']))
        
        # Add the new widget permissions
        for widget_code in widget_permissions:
            if widget_code in all_permissions:
                db.execute('''
                    INSERT INTO role_permissions (role, permission_id)
                    VALUES (?, ?)
                ''', (role, all_permissions[widget_code]['id']))
        
        # Log permission changes
        for widget_code in widget_permission_codes:
            if widget_code in current_widget_permissions and widget_code not in widget_permissions:
                # Widget permission was removed
                log_permission_change(
                    role=role,
                    permission_code=widget_code,
                    permission_name=all_permissions[widget_code]['name'],
                    action='removed',
                    previous_state='granted',
                    new_state='revoked'
                )
            elif widget_code not in current_widget_permissions and widget_code in widget_permissions:
                # Widget permission was added
                log_permission_change(
                    role=role,
                    permission_code=widget_code,
                    permission_name=all_permissions[widget_code]['name'],
                    action='added',
                    previous_state='revoked',
                    new_state='granted'
                )
        
        # Commit the transaction
        db.execute('COMMIT')
        
        return jsonify({'success': True})
    
    except Exception as e:
        # Rollback the transaction on error
        db.execute('ROLLBACK')
        current_app.logger.error(f"Error updating widget permissions: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@permission_api.route('/users/permissions/audit-logs/<role>', methods=['GET'])
@login_required
def get_role_audit_logs(role):
    """Get audit logs for a specific role"""
    if not has_permission('permission_manage'):
        return jsonify({'success': False, 'message': 'Permission denied'}), 403
    
    db = get_db()
    
    # Get audit logs for the role
    logs = []
    cursor = db.execute('''
        SELECT * FROM permission_audit_logs
        WHERE role = ?
        ORDER BY timestamp DESC
        LIMIT 50
    ''', (role,))
    
    for row in cursor:
        logs.append({
            'id': row['id'],
            'timestamp': row['timestamp'],
            'username': row['username'],
            'action': row['action'],
            'permission_code': row['permission_code'],
            'permission_name': row['permission_name'],
            'previous_state': row['previous_state'],
            'new_state': row['new_state']
        })
    
    return jsonify({
        'success': True,
        'role': role,
        'logs': logs
    })
