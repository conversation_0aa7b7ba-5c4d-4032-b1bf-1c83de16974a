"""
Test multiple tracking numbers to understand TCS website behavior
"""

import asyncio
import json
from tcs_scraper_simple import TCSScraperSimple

async def test_multiple_numbers():
    """Test multiple tracking numbers from the sample file"""
    
    # Read tracking numbers from sample file
    with open('tcs/sample.txt', 'r') as f:
        lines = f.readlines()
    
    tracking_numbers = []
    for line in lines:
        line = line.strip()
        if line.isdigit() and len(line) >= 8:
            tracking_numbers.append(line)
    
    print(f"Found {len(tracking_numbers)} tracking numbers to test")
    
    # Test with browser visible for debugging
    scraper = TCSScraperSimple(headless=False)
    
    results = {}
    
    for i, number in enumerate(tracking_numbers[:3]):  # Test first 3 numbers
        print(f"\n=== Testing {i+1}/{len(tracking_numbers[:3])}: {number} ===")
        
        try:
            result = await scraper.track_shipment(number)
            results[number] = result
            
            # Print summary
            if result.get('success'):
                print(f"✅ Success: {result.get('current_status', 'Unknown')}")
                print(f"   Origin: {result.get('origin', 'N/A')}")
                print(f"   Destination: {result.get('destination', 'N/A')}")
                print(f"   History entries: {len(result.get('track_history', []))}")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results[number] = {'error': str(e)}
        
        # Wait between requests
        await asyncio.sleep(3)
    
    # Save results
    with open('tracking_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n=== Test Complete ===")
    print(f"Results saved to tracking_test_results.json")
    
    # Show summary
    successful = sum(1 for r in results.values() if r.get('success'))
    print(f"Successful: {successful}/{len(results)}")

if __name__ == "__main__":
    asyncio.run(test_multiple_numbers())
