{% extends 'base.html' %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">User Management</h5>
                    <div>
                        <button type="button" class="btn btn-light btn-sm" id="exportCSV">
                            <i class="fas fa-file-csv"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-light btn-sm" id="exportPDF">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Dashboard Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total Users</h5>
                                    <h2 class="card-text">{{ users|length }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Active Users</h5>
                                    <h2 class="card-text">{{ users|selectattr('status', 'equalto', 'active')|list|length }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Inactive Users</h5>
                                    <h2 class="card-text">{{ users|selectattr('status', 'equalto', 'inactive')|list|length }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Admin Users</h5>
                                    <h2 class="card-text">{{ users|selectattr('role', 'equalto', 'admin')|list|length }}</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('users.add') }}" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> Add New User
                            </a>
                            <a href="{{ url_for('users.roles') }}" class="btn btn-info">
                                <i class="fas fa-user-tag"></i> Manage Roles
                            </a>
                            <a href="{{ url_for('users.logs') }}" class="btn btn-secondary">
                                <i class="fas fa-clipboard-list"></i> View Activity Logs
                            </a>
                        </div>
                    </div>

                    <!-- Users List -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="usersTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Username</th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.full_name }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge {% if user.role == 'admin' %}badge-danger{% elif user.role == 'manager' %}badge-warning{% elif user.role == 'sales' %}badge-success{% elif user.role == 'warehouse' %}badge-info{% elif user.role == 'rider' %}badge-primary{% else %}badge-secondary{% endif %}">
                                            {{ user.role|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {% if user.status == 'active' %}badge-success{% else %}badge-danger{% endif %}">
                                            {{ user.status|title }}
                                        </span>
                                    </td>
                                    <td>{{ user.last_login|default('Never', true) }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('users.edit', user_id=user.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if user.username != 'admin' and user.username != current_user.username %}
                                                {% if user.status == 'active' %}
                                                <a href="{{ url_for('users.toggle_status', user_id=user.id) }}" class="btn btn-sm btn-warning"
                                                   onclick="return confirm('Are you sure you want to deactivate this user?')">
                                                    <i class="fas fa-user-slash"></i>
                                                </a>
                                                {% else %}
                                                <a href="{{ url_for('users.toggle_status', user_id=user.id) }}" class="btn btn-sm btn-success"
                                                   onclick="return confirm('Are you sure you want to activate this user?')">
                                                    <i class="fas fa-user-check"></i>
                                                </a>
                                                {% endif %}
                                                <a href="{{ url_for('users.delete', user_id=user.id) }}" class="btn btn-sm btn-danger"
                                                   onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            {% endif %}
                                            <a href="{{ url_for('users.reset_password', user_id=user.id) }}" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-key"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#usersTable').DataTable({
            "order": [[0, "asc"]],
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });

        // Export to CSV
        $('#exportCSV').click(function() {
            exportTableToCSV('users_list.csv');
        });

        // Export to PDF
        $('#exportPDF').click(function() {
            // Create a new jsPDF instance
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Add title
            doc.setFontSize(18);
            doc.text('Medivent Pharmaceuticals Pvt. Ltd.', 105, 15, { align: 'center' });
            doc.setFontSize(14);
            doc.text('User Management', 105, 25, { align: 'center' });

            // Add date
            doc.setFontSize(10);
            doc.text('Generated on: ' + new Date().toLocaleString(), 105, 35, { align: 'center' });

            // Get table data
            const rows = [];
            const headers = [];

            // Get headers
            $('#usersTable thead th').each(function() {
                headers.push($(this).text().trim());
            });

            rows.push(headers);

            // Get visible rows data
            $('#usersTable tbody tr').each(function() {
                const rowData = [];
                $(this).find('td').each(function(index) {
                    // For action buttons, just use "Actions" text
                    if (index === headers.length - 1) {
                        rowData.push("Actions");
                    } else {
                        rowData.push($(this).text().trim());
                    }
                });
                rows.push(rowData);
            });

            // Add table to PDF
            doc.autoTable({
                head: [headers],
                body: rows.slice(1),
                startY: 40,
                theme: 'grid',
                headStyles: {
                    fillColor: [0, 123, 255],
                    textColor: 255,
                    fontStyle: 'bold'
                },
                alternateRowStyles: {
                    fillColor: [240, 240, 240]
                },
                margin: { top: 40 }
            });

            // Save the PDF
            doc.save('users_list.pdf');
        });

        // Export table to CSV
        function exportTableToCSV(filename) {
            var csv = [];

            // Get headers
            var headerRow = [];
            $('#usersTable thead th').each(function() {
                headerRow.push('"' + $(this).text().trim() + '"');
            });
            csv.push(headerRow.join(','));

            // Get rows data
            $('#usersTable tbody tr').each(function() {
                var row = [];
                $(this).find('td').each(function(index) {
                    // For action buttons, just use "Actions" text
                    if (index === headerRow.length - 1) {
                        row.push('"Actions"');
                    } else {
                        var text = $(this).text().replace(/[\n\r]+/g, ' ').trim();
                        row.push('"' + text + '"');
                    }
                });
                csv.push(row.join(','));
            });

            // Download CSV file
            downloadCSV(csv.join('\n'), filename);
        }

        function downloadCSV(csv, filename) {
            try {
                var csvFile = new Blob([csv], {type: 'text/csv'});
                var downloadLink = document.createElement('a');

                downloadLink.download = filename;
                downloadLink.href = window.URL.createObjectURL(csvFile);
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);

                downloadLink.click();
                document.body.removeChild(downloadLink);
            } catch (e) {
                console.error("Error downloading CSV: ", e);
                alert("Error exporting CSV: " + e.message);
            }
        }
    });
</script>
{% endblock %}
