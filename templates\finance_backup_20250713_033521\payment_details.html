{% extends 'base.html' %}

{% block title %}Invoice Payment Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Invoice Details - {{ invoice.invoice_number }}</h5>
                    <div>
                        <a href="{{ url_for('finance') }}?view=payments" class="btn btn-sm btn-light">
                            <i class="fas fa-arrow-left"></i> Back to Payments
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Invoice Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">Invoice Number:</th>
                                    <td>{{ invoice.invoice_number }}</td>
                                </tr>
                                <tr>
                                    <th>Date Generated:</th>
                                    <td>{{ invoice.date_generated|format_datetime }}</td>
                                </tr>
                                <tr>
                                    <th>Total Amount:</th>
                                    <td>{{ invoice.total_amount|format_currency }}</td>
                                </tr>
                                <tr>
                                    <th>Paid Amount:</th>
                                    <td>{{ invoice.paid_amount|format_currency }}</td>
                                </tr>
                                <tr>
                                    <th>Outstanding:</th>
                                    <td>
                                        <span class="font-weight-bold {% if invoice.total_amount > invoice.paid_amount %}text-danger{% else %}text-success{% endif %}">
                                            {{ (invoice.total_amount - invoice.paid_amount)|format_currency }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Payment Status:</th>
                                    <td>
                                        <span class="badge
                                            {% if invoice.payment_status == 'Paid' %}badge-success
                                            {% elif invoice.payment_status == 'Partial' %}badge-warning
                                            {% else %}badge-danger{% endif %}">
                                            {{ invoice.payment_status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Order ID:</th>
                                    <td>
                                        <a href="{{ url_for('view_order', order_id=invoice.order_id) }}"
                                           class="text-primary font-weight-bold">
                                            {{ invoice.order_id }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Order Status:</th>
                                    <td>
                                        <span class="badge
                                            {% if invoice.order_status == 'Placed' %}badge-info
                                            {% elif invoice.order_status == 'Approved' %}badge-primary
                                            {% elif invoice.order_status == 'Processing' %}badge-warning
                                            {% elif invoice.order_status == 'Ready for Pickup' %}badge-secondary
                                            {% elif invoice.order_status == 'Dispatched' %}badge-dark
                                            {% elif invoice.order_status == 'Delivered' %}badge-success
                                            {% elif invoice.order_status == 'Cancelled' %}badge-danger
                                            {% else %}badge-light{% endif %}">
                                            {{ invoice.order_status }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Customer Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">Customer Name:</th>
                                    <td>{{ invoice.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Customer Code:</th>
                                    <td>{{ invoice.customer_code }}</td>
                                </tr>
                                <tr>
                                    <th>Actions:</th>
                                    <td>
                                        <a href="{{ url_for('finance') }}?view=ledger&customer_id={{ invoice.customer_id }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-book"></i> View Ledger
                                        </a>
                                        {% if invoice.pdf_path %}
                                        <a href="{{ url_for('static', filename='documents/invoices/' + invoice.pdf_path.split('/')[-1]) }}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-file-pdf"></i> View PDF
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>

                            {% if invoice.total_amount > invoice.paid_amount %}
                            <div class="card mt-3">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Record Payment</h6>
                                </div>
                                <div class="card-body">
                                    <form action="{{ url_for('record_payment') }}" method="POST">
                                        <input type="hidden" name="invoice_id" value="{{ invoice.invoice_number }}">
                                        <div class="form-group">
                                            <label for="payment_date">Payment Date</label>
                                            <input type="date" class="form-control" id="payment_date" name="payment_date" value="{{ now.strftime('%Y-%m-%d') }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="payment_method">Payment Method</label>
                                            <select class="form-control" id="payment_method" name="payment_method" required>
                                                <option value="Cash">Cash</option>
                                                <option value="Cheque">Cheque</option>
                                                <option value="Bank Transfer">Bank Transfer</option>
                                                <option value="Online">Online</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="amount">Amount</label>
                                            <input type="text" pattern="[0-9]+(\.[0-9]{1,2})?" class="form-control" id="amount" name="amount" value="{{ invoice.total_amount - invoice.paid_amount }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="reference_number">Reference Number</label>
                                            <input type="text" class="form-control" id="reference_number" name="reference_number" placeholder="Cheque/Transaction Number">
                                        </div>
                                        <div class="form-group">
                                            <label for="notes">Notes</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-success" onclick="return validatePayment()">
                                            <i class="fas fa-save"></i> Record Payment
                                        </button>
                                    </form>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="text-muted">Order Items</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Line Total</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in order_items %}
                                        <tr>
                                            <td>{{ item.product_name }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.unit_price|format_currency }}</td>
                                            <td>{{ (item.quantity * item.unit_price)|format_currency }}</td>
                                            <td>
                                                <span class="badge
                                                    {% if item.status == 'Placed' %}badge-info
                                                    {% elif item.status == 'Approved' %}badge-primary
                                                    {% elif item.status == 'Processing' %}badge-warning
                                                    {% elif item.status == 'Ready for Pickup' %}badge-secondary
                                                    {% elif item.status == 'Dispatched' %}badge-dark
                                                    {% elif item.status == 'Delivered' %}badge-success
                                                    {% elif item.status == 'Cancelled' %}badge-danger
                                                    {% elif item.status == 'Pending' %}badge-warning
                                                    {% elif item.status == 'Partially Approved' %}badge-info
                                                    {% else %}badge-light{% endif %}">
                                                    {{ item.status }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="text-muted">Payment History</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Payment ID</th>
                                            <th>Date</th>
                                            <th>Method</th>
                                            <th>Amount</th>
                                            <th>Reference</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in payments %}
                                        {% if payment.invoice_id == invoice.invoice_number %}
                                        <tr>
                                            <td><strong>{{ payment.payment_id }}</strong></td>
                                            <td>{{ payment.payment_date|format_datetime }}</td>
                                            <td>{{ payment.payment_method }}</td>
                                            <td>{{ payment.amount|format_currency }}</td>
                                            <td>{{ payment.reference_number or 'N/A' }}</td>
                                            <td>{{ payment.notes or 'N/A' }}</td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="text-muted">Customer Payment History</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Invoice</th>
                                            <th>Payment Date</th>
                                            <th>Method</th>
                                            <th>Amount</th>
                                            <th>Reference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in customer_payments %}
                                        <tr>
                                            <td>
                                                <a href="{{ url_for('finance_invoice_details', invoice_number=payment.invoice_id) }}"
                                                   class="text-primary font-weight-bold">
                                                    {{ payment.invoice_number }}
                                                </a>
                                            </td>
                                            <td>{{ payment.payment_date|format_datetime }}</td>
                                            <td>{{ payment.payment_method }}</td>
                                            <td>{{ payment.amount|format_currency }}</td>
                                            <td>{{ payment.reference_number or 'N/A' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function validatePayment() {
        var amount = parseFloat(document.getElementById('amount').value);
        var outstanding = {{ (invoice.total_amount|default(0)) - (invoice.paid_amount|default(0)) }};

        if (isNaN(amount) || amount <= 0) {
            alert('Please enter a valid payment amount greater than 0.');
            return false;
        }

        if (amount > outstanding) {
            var proceed = confirm('Payment amount (' + amount.toFixed(2) + ') is greater than outstanding amount (' + outstanding.toFixed(2) + '). Do you want to proceed?');
            return proceed;
        }

        return true;
    }
</script>
{% endblock %}