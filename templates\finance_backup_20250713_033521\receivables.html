{% extends 'base.html' %}

{% block title %}Accounts Receivable{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Sales</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalSales">₨865,942.08</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Receivables</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalReceivables">₨277,933.38</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Collection Ratio</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="collectionRatio">67.9%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Overdue Amount</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="overdueAmount">₨125,450.00</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Accounts Receivable</h5>
                        <button type="button" class="btn btn-light btn-sm" onclick="goBack()">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customerSearch">Search Customer</label>
                                <input type="text" class="form-control" id="customerSearch" placeholder="Search by customer name, code, or invoice number...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="dateRange">Date Range</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="startDate" placeholder="Start Date">
                                    <div class="input-group-append input-group-prepend">
                                        <span class="input-group-text">to</span>
                                    </div>
                                    <input type="date" class="form-control" id="endDate" placeholder="End Date">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="button" class="btn btn-primary mr-2" onclick="applyDateFilter()">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Age Filter Buttons -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-primary filter-btn" data-filter="all">All</button>
                                <button type="button" class="btn btn-outline-success filter-btn" data-filter="0-30">0-30 Days</button>
                                <button type="button" class="btn btn-outline-warning filter-btn" data-filter="31-60">31-60 Days</button>
                                <button type="button" class="btn btn-outline-danger filter-btn" data-filter="61-90">61-90 Days</button>
                                <button type="button" class="btn btn-outline-dark filter-btn" data-filter="90+">90+ Days</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="float-right">
                                <a href="{{ url_for('export_receivables', format='excel') }}" class="btn btn-success ml-2">
                                    <i class="fas fa-file-excel"></i> Export to Excel
                                </a>
                                <a href="{{ url_for('export_receivables', format='pdf') }}" class="btn btn-danger">
                                    <i class="fas fa-file-pdf"></i> Export to PDF
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="receivablesTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Customer Code</th>
                                    <th>Amount</th>
                                    <th>Outstanding</th>
                                    <th>Days</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if receivables %}
                                    {% for invoice in receivables %}
                                    <tr class="aging-row" data-days="{{ invoice.days_outstanding|int }}">
                                        <td>{{ invoice.invoice_number }}</td>
                                        <td>{{ invoice.date_generated }}</td>
                                        <td>{{ invoice.customer_name }}</td>
                                        <td>{{ invoice.customer_code }}</td>
                                        <td>{{ invoice.total_amount|default(invoice.order_amount)|round(2)|format_currency }}</td>
                                        <td>{{ (invoice.total_amount|default(invoice.order_amount) - invoice.paid_amount|default(0))|round(2)|format_currency }}</td>
                                        <td>{{ invoice.days_outstanding|int }}</td>
                                        <td>
                                            {% if invoice.days_outstanding <= 30 %}
                                            <span class="badge badge-success">Current</span>
                                            {% elif invoice.days_outstanding <= 60 %}
                                            <span class="badge badge-warning">Overdue</span>
                                            {% elif invoice.days_outstanding <= 90 %}
                                            <span class="badge badge-danger">Critical</span>
                                            {% else %}
                                            <span class="badge badge-dark">Delinquent</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('finance', view='payments') }}?invoice_id={{ invoice.invoice_id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-money-bill-wave"></i> Record Payment
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center">No receivables found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Calculate and update summary cards
        updateSummaryCards();

        // Search functionality
        $('#customerSearch').on('keyup', function() {
            applyAllFilters();
        });

        // Filter functionality
        $('.filter-btn').click(function() {
            $('.filter-btn').removeClass('active');
            $(this).addClass('active');
            applyAllFilters();
        });

        // Set "All" as default active filter
        $('.filter-btn[data-filter="all"]').addClass('active');
    });

    function applyAllFilters() {
        var searchTerm = $('#customerSearch').val().toLowerCase();
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        var ageFilter = $('.filter-btn.active').data('filter');

        $('.aging-row').each(function() {
            var row = $(this);
            var showRow = true;

            // Age filter
            if (ageFilter !== 'all') {
                var days = parseInt(row.data('days'));
                var showByAge = false;

                if (ageFilter === '0-30') {
                    showByAge = (days >= 0 && days <= 30);
                } else if (ageFilter === '31-60') {
                    showByAge = (days >= 31 && days <= 60);
                } else if (ageFilter === '61-90') {
                    showByAge = (days >= 61 && days <= 90);
                } else if (ageFilter === '90+') {
                    showByAge = (days > 90);
                }

                if (!showByAge) showRow = false;
            }

            // Search filter
            if (searchTerm && showRow) {
                var invoiceNumber = row.find('td:nth-child(1)').text().toLowerCase();
                var customerName = row.find('td:nth-child(3)').text().toLowerCase();
                var customerCode = row.find('td:nth-child(4)').text().toLowerCase();

                var showBySearch = (invoiceNumber.includes(searchTerm) ||
                                   customerName.includes(searchTerm) ||
                                   customerCode.includes(searchTerm));

                if (!showBySearch) showRow = false;
            }

            // Date filter
            if ((startDate || endDate) && showRow) {
                var rowDate = row.find('td:nth-child(2)').text(); // Date column
                var showByDate = true;

                if (startDate && rowDate < startDate) showByDate = false;
                if (endDate && rowDate > endDate) showByDate = false;

                if (!showByDate) showRow = false;
            }

            // Show/hide row
            if (showRow) {
                row.show();
            } else {
                row.hide();
            }
        });

        // Update summary cards based on visible rows
        updateSummaryCards();
    }

    function applyDateFilter() {
        applyAllFilters();
    }

    function clearFilters() {
        $('#customerSearch').val('');
        $('#startDate').val('');
        $('#endDate').val('');
        $('.filter-btn').removeClass('active');
        $('.filter-btn[data-filter="all"]').addClass('active');
        applyAllFilters();
    }

    function updateSummaryCards() {
        var totalSales = 0;
        var totalReceivables = 0;
        var overdueAmount = 0;
        var visibleRows = 0;

        $('.aging-row:visible').each(function() {
            var row = $(this);
            var amount = parseFloat(row.find('td:nth-child(5)').text().replace(/[₨,]/g, '')) || 0;
            var outstanding = parseFloat(row.find('td:nth-child(6)').text().replace(/[₨,]/g, '')) || 0;
            var days = parseInt(row.data('days')) || 0;

            totalSales += amount;
            totalReceivables += outstanding;

            if (days > 30) {
                overdueAmount += outstanding;
            }

            visibleRows++;
        });

        var collectionRatio = totalSales > 0 ? ((totalSales - totalReceivables) / totalSales * 100) : 0;

        // Update the summary cards
        $('#totalSales').text('₨' + totalSales.toLocaleString('en-US', {minimumFractionDigits: 2}));
        $('#totalReceivables').text('₨' + totalReceivables.toLocaleString('en-US', {minimumFractionDigits: 2}));
        $('#collectionRatio').text(collectionRatio.toFixed(1) + '%');
        $('#overdueAmount').text('₨' + overdueAmount.toLocaleString('en-US', {minimumFractionDigits: 2}));
    }

    function goBack() {
        window.history.back();
    }
</script>
{% endblock %}
