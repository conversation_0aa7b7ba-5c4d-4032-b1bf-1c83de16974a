#!/usr/bin/env python3
"""
Create comprehensive divisions database schema with sample data
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

def create_divisions_schema():
    """Create divisions table with proper schema and sample data"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    # Create backup
    backup_path = f'instance/medivent_backup_divisions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    import shutil
    shutil.copy2(db_path, backup_path)
    print(f"✅ Backup created: {backup_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🏗️  CREATING DIVISIONS DATABASE SCHEMA")
        print("=" * 60)
        
        # Drop existing divisions table if it exists
        cursor.execute('DROP TABLE IF EXISTS divisions')
        print("✅ Cleaned existing divisions table")
        
        # Create comprehensive divisions table
        cursor.execute('''
            CREATE TABLE divisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                code VARCHAR(20) NOT NULL UNIQUE,
                description TEXT,
                target_revenue DECIMAL(15,2) DEFAULT 0,
                achieved_revenue DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'active',
                manager_name VARCHAR(100),
                manager_email VARCHAR(100),
                employee_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ Created divisions table with comprehensive schema")
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX idx_divisions_status ON divisions(status)",
            "CREATE INDEX idx_divisions_code ON divisions(code)",
            "CREATE INDEX idx_divisions_name ON divisions(name)",
            "CREATE INDEX idx_divisions_created_at ON divisions(created_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        print("✅ Created performance indexes")
        
        # Create trigger to update updated_at timestamp
        cursor.execute('''
            CREATE TRIGGER update_divisions_timestamp 
            AFTER UPDATE ON divisions
            BEGIN
                UPDATE divisions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        ''')
        print("✅ Created timestamp update trigger")
        
        # Insert sample data for testing
        sample_divisions = [
            {
                'name': 'AQVIDA Division',
                'code': 'AQVIDA',
                'description': 'Advanced pharmaceutical solutions and medical devices',
                'target_revenue': 500000,
                'achieved_revenue': 450000,
                'status': 'active',
                'manager_name': 'Dr. Sarah Johnson',
                'manager_email': '<EMAIL>',
                'employee_count': 25
            },
            {
                'name': 'BEACON-R Division',
                'code': 'BEACON-R',
                'description': 'Radiology and imaging solutions',
                'target_revenue': 400000,
                'achieved_revenue': 380000,
                'status': 'active',
                'manager_name': 'Dr. Michael Chen',
                'manager_email': '<EMAIL>',
                'employee_count': 18
            },
            {
                'name': 'ONKO-KOSEL Division',
                'code': 'ONKO-KOSEL',
                'description': 'Oncology and specialized medical treatments',
                'target_revenue': 350000,
                'achieved_revenue': 320000,
                'status': 'active',
                'manager_name': 'Dr. Emily Rodriguez',
                'manager_email': '<EMAIL>',
                'employee_count': 22
            },
            {
                'name': 'Radiology Division',
                'code': 'RADIOLOGY',
                'description': 'Diagnostic imaging and radiology services',
                'target_revenue': 200000,
                'achieved_revenue': 180000,
                'status': 'active',
                'manager_name': 'Dr. James Wilson',
                'manager_email': '<EMAIL>',
                'employee_count': 15
            },
            {
                'name': 'CA BO Division',
                'code': 'CA-BO',
                'description': 'Cancer biology and oncology research',
                'target_revenue': 150000,
                'achieved_revenue': 140000,
                'status': 'active',
                'manager_name': 'Dr. Lisa Thompson',
                'manager_email': '<EMAIL>',
                'employee_count': 12
            },
            {
                'name': 'Cardiology Division',
                'code': 'CARDIO',
                'description': 'Cardiovascular medical solutions and devices',
                'target_revenue': 100000,
                'achieved_revenue': 95000,
                'status': 'active',
                'manager_name': 'Dr. Robert Davis',
                'manager_email': '<EMAIL>',
                'employee_count': 10
            },
            {
                'name': 'Research & Development',
                'code': 'R&D',
                'description': 'Innovation and product development division',
                'target_revenue': 75000,
                'achieved_revenue': 60000,
                'status': 'active',
                'manager_name': 'Dr. Anna Martinez',
                'manager_email': '<EMAIL>',
                'employee_count': 8
            },
            {
                'name': 'Quality Assurance',
                'code': 'QA',
                'description': 'Quality control and regulatory compliance',
                'target_revenue': 50000,
                'achieved_revenue': 45000,
                'status': 'active',
                'manager_name': 'John Anderson',
                'manager_email': '<EMAIL>',
                'employee_count': 6
            }
        ]
        
        # Insert sample data with varied creation dates
        base_date = datetime.now() - timedelta(days=180)
        for i, division in enumerate(sample_divisions):
            # Vary creation dates over the last 6 months
            created_date = base_date + timedelta(days=random.randint(0, 150))
            
            cursor.execute('''
                INSERT INTO divisions 
                (name, code, description, target_revenue, achieved_revenue, status, 
                 manager_name, manager_email, employee_count, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                division['name'], division['code'], division['description'],
                division['target_revenue'], division['achieved_revenue'], division['status'],
                division['manager_name'], division['manager_email'], division['employee_count'],
                created_date.strftime('%Y-%m-%d %H:%M:%S')
            ))
        
        print(f"✅ Inserted {len(sample_divisions)} sample divisions")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 DIVISIONS DATABASE SCHEMA CREATED!")
        print("=" * 60)
        print("✅ Table: divisions with comprehensive schema")
        print("✅ Indexes: status, code, name, created_at")
        print("✅ Trigger: automatic updated_at timestamp")
        print(f"✅ Sample Data: {len(sample_divisions)} divisions")
        print("🎯 Ready for Division Management System!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating divisions schema: {e}")
        return False

if __name__ == "__main__":
    success = create_divisions_schema()
    if success:
        print("\n🎉 SUCCESS! Divisions database schema created!")
    else:
        print("\n❌ FAILED to create divisions schema")
