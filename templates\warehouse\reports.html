{% extends 'base.html' %}

{% block title %}Warehouse Reports Dashboard - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    
    .metric-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .metric-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .metric-card.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .metric-card.danger {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .warehouse-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 5px solid #007bff;
    }
    
    .warehouse-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .performance-indicator {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 18px;
        color: white;
    }
    
    .performance-excellent { background: linear-gradient(135deg, #11998e, #38ef7d); }
    .performance-good { background: linear-gradient(135deg, #667eea, #764ba2); }
    .performance-average { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .performance-poor { background: linear-gradient(135deg, #fa709a, #fee140); }
    
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-warehouse text-primary"></i> Warehouse Reports Dashboard
        </h1>
        <div>
            <a href="{{ url_for('warehouses') }}" class="btn btn-outline-primary btn-sm mr-2">
                <i class="fas fa-arrow-left"></i> Back to Warehouses
            </a>
            <button class="btn btn-primary btn-sm" onclick="exportReports()">
                <i class="fas fa-download"></i> Export Reports
            </button>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card metric-card h-100 py-3">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total Warehouses</div>
                            <div class="h4 mb-0 font-weight-bold">{{ warehouse_stats.total_warehouses }}</div>
                            <small><i class="fas fa-arrow-up trend-up"></i> +2 this month</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card metric-card success h-100 py-3">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total Inventory Value</div>
                            <div class="h4 mb-0 font-weight-bold">₨{{ "{:,.0f}".format(warehouse_stats.total_value) }}</div>
                            <small><i class="fas fa-arrow-up trend-up"></i> +15% vs last month</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card metric-card info h-100 py-3">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Active Products</div>
                            <div class="h4 mb-0 font-weight-bold">{{ warehouse_stats.total_products }}</div>
                            <small><i class="fas fa-arrow-up trend-up"></i> +8 new products</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card metric-card warning h-100 py-3">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Low Stock Alerts</div>
                            <div class="h4 mb-0 font-weight-bold">{{ warehouse_stats.low_stock_count }}</div>
                            <small><i class="fas fa-exclamation-triangle"></i> Needs attention</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line text-primary"></i> Warehouse Performance Trends
                </h5>
                <canvas id="performanceChart" height="100"></canvas>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie text-success"></i> Inventory Distribution
                </h5>
                <canvas id="distributionChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Warehouse Performance Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 bg-gradient-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-trophy"></i> Warehouse Performance Analysis
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for warehouse in warehouse_performance %}
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="warehouse-card p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="performance-indicator performance-{{ warehouse.performance_class }} mr-3">
                                        {{ warehouse.performance_score }}%
                                    </div>
                                    <div>
                                        <h6 class="mb-1 font-weight-bold">{{ warehouse.name }}</h6>
                                        <small class="text-muted">{{ warehouse.location }}</small>
                                    </div>
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="text-xs text-muted">Products</div>
                                        <div class="font-weight-bold">{{ warehouse.total_products }}</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-xs text-muted">Value</div>
                                        <div class="font-weight-bold">₨{{ "{:,.0f}".format(warehouse.total_value) }}</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-xs text-muted">Utilization</div>
                                        <div class="font-weight-bold">{{ warehouse.utilization }}%</div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-{{ warehouse.performance_color }}" 
                                             style="width: {{ warehouse.utilization }}%"></div>
                                    </div>
                                </div>
                                
                                <div class="mt-3 d-flex justify-content-between">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> Last updated: {{ warehouse.last_updated }}
                                    </small>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Details
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Reports Section -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-gradient-success text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-file-alt"></i> Quick Reports
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ url_for('warehouse_utilization_report') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-chart-bar text-primary mr-2"></i>
                                Warehouse Utilization Report
                            </div>
                            <span class="badge badge-primary badge-pill">Generate</span>
                        </a>
                        <a href="{{ url_for('low_stock_alert_report') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-exclamation-triangle text-warning mr-2"></i>
                                Low Stock Alert Report
                            </div>
                            <span class="badge badge-warning badge-pill">{{ warehouse_stats.low_stock_count }}</span>
                        </a>
                        <a href="{{ url_for('warehouse_performance_report') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-truck text-info mr-2"></i>
                                Delivery Performance Report
                            </div>
                            <span class="badge badge-info badge-pill">Generate</span>
                        </a>
                        <a href="{{ url_for('monthly_summary_report') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-calendar text-success mr-2"></i>
                                Monthly Summary Report
                            </div>
                            <span class="badge badge-success badge-pill">Generate</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-gradient-info text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-bell"></i> Recent Alerts & Notifications
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for alert in recent_alerts %}
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="bg-{{ alert.type }} rounded-circle p-2">
                                        <i class="fas {{ alert.icon }} text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ml-3">
                                    <h6 class="mb-1">{{ alert.title }}</h6>
                                    <p class="text-muted mb-1">{{ alert.message }}</p>
                                    <small class="text-muted">{{ alert.time_ago }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Performance Trends Chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Warehouse Efficiency',
                data: [],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                fill: true
            }, {
                label: 'Inventory Turnover',
                data: [],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Inventory Distribution Chart
    const distributionCtx = document.getElementById('distributionChart').getContext('2d');
    new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function exportReports() {
    // Export functionality
    alert('Export functionality will be implemented here');
}
</script>
{% endblock %}
