{% extends 'base.html' %}

{% block title %}Order Search Results{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-search"></i> Order Search Results
                        </h4>
                        <div>
                            <a href="{{ url_for('orders') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left"></i> Back to Orders
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if search_query %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Search results for: <strong>"{{ search_query }}"</strong>
                        {% if orders %}
                            - Found {{ orders|length }} result(s)
                        {% endif %}
                    </div>
                    {% endif %}

                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <!-- Search Form -->
                    <form method="GET" action="{{ url_for('orders') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="q" 
                                           placeholder="Search by Order ID, Customer Name, Phone, Email, Invoice Number..." 
                                           value="{{ search_query or '' }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                {% if search_query %}
                                <a href="{{ url_for('orders') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear Search
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </form>

                    {% if orders %}
                    <!-- Search Results Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Invoice</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ order.order_id }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ order.customer_name }}</strong><br>
                                            <small class="text-muted">
                                                {% if order.customer_phone %}
                                                    <i class="fas fa-phone"></i> {{ order.customer_phone }}<br>
                                                {% endif %}
                                                {% if order.customer_email %}
                                                    <i class="fas fa-envelope"></i> {{ order.customer_email }}
                                                {% endif %}
                                            </small>
                                        </div>
                                    </td>
                                    <td>{{ order.order_date.strftime('%Y-%m-%d') if order.order_date else 'N/A' }}</td>
                                    <td>{{ "₨{:,.2f}".format(order.order_amount) if order.order_amount else '₨0.00' }}</td>
                                    <td>
                                        <span class="badge badge-{% if order.status == 'Delivered' %}success{% elif order.status == 'Cancelled' %}danger{% elif order.status == 'Approved' %}info{% elif order.status == 'Processing' %}warning{% else %}secondary{% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if order.payment_method == 'cash' %}success{% elif order.payment_method == 'cheque' %}warning{% else %}info{% endif %}">
                                            {{ order.payment_method|title }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if order.invoice_number %}
                                            <span class="text-success">{{ order.invoice_number }}</span>
                                        {% else %}
                                            <span class="text-muted">Not Generated</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('view_order', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-info" title="View Order">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if order.status not in ['Delivered', 'Cancelled'] %}
                                            <a href="{{ url_for('update_order', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-warning" title="Edit Order">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            {% if order.invoice_number %}
                                            <a href="{{ url_for('view_invoice', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-success" title="View Invoice" target="_blank">
                                                <i class="fas fa-file-invoice"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination would go here if needed -->
                    
                    {% else %}
                    <!-- No Results -->
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Orders Found</h4>
                        {% if search_query %}
                        <p class="text-muted">
                            No orders match your search criteria: <strong>"{{ search_query }}"</strong><br>
                            Try searching with different keywords or check the spelling.
                        </p>
                        <a href="{{ url_for('orders') }}" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All Orders
                        </a>
                        {% else %}
                        <p class="text-muted">Enter search criteria to find specific orders.</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Auto-focus search input
        $('input[name="q"]').focus();
        
        // Highlight search terms in results
        {% if search_query %}
        var searchTerm = "{{ search_query }}";
        if (searchTerm.length > 2) {
            $('tbody').highlight(searchTerm, {
                className: 'bg-warning'
            });
        }
        {% endif %}
    });
    
    // Simple highlight function
    $.fn.highlight = function(text, options) {
        var settings = $.extend({
            className: 'highlight'
        }, options);
        
        return this.each(function() {
            var $this = $(this);
            var html = $this.html();
            var regex = new RegExp('(' + text + ')', 'gi');
            html = html.replace(regex, '<span class="' + settings.className + '">$1</span>');
            $this.html(html);
        });
    };
</script>
{% endblock %}
