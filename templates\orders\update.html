{% extends 'base.html' %}

{% block title %}Update Order - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Update Order #{{ order.order_id }}</h4>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('update_order', order_id=order.order_id) }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" value="{{ order.customer_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_phone">Customer Phone</label>
                                    <input type="text" class="form-control" id="customer_phone" name="customer_phone" value="{{ order.customer_phone }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="customer_address">Customer Address</label>
                            <input type="text" class="form-control" id="customer_address" name="customer_address" value="{{ order.customer_address }}" required>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="payment_mode">Payment Mode</label>
                                    <select class="form-control" id="payment_mode" name="payment_mode" required>
                                        <option value="cash" {% if order.payment_method == 'cash' %}selected{% endif %}>Cash</option>
                                        <option value="cheque" {% if order.payment_method == 'cheque' %}selected{% endif %}>Cheque</option>
                                        <option value="online" {% if order.payment_method == 'online' %}selected{% endif %}>Online</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="sales_agent">Sales Agent</label>
                                    <input type="text" class="form-control" id="sales_agent" name="sales_agent" value="{{ order.sales_agent }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status" required>
                                        {% for status in statuses %}
                                        <option value="{{ status }}" {% if order.status == status %}selected{% endif %}>{{ status }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">Order Items</h5>

                        <div class="table-responsive">
                            <table class="table table-striped" id="order-items-table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Strength</th>
                                        <th>Quantity</th>
                                        <th>FOC Qty</th>
                                        <th>Total Qty</th>
                                        <th>Unit Price</th>
                                        <th>Line Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="order-items-body">
                                    {% for item in order_items %}
                                    <tr class="order-item-row" data-item-id="{{ item.order_item_id }}">
                                        <td>
                                            <input type="hidden" name="order_item_id[]" value="{{ item.order_item_id }}">
                                            <input type="hidden" name="product_id[]" value="{{ item.product_id }}" class="product-id">
                                            <input type="hidden" name="product_name[]" value="{{ item.product_name }}" class="product-name">
                                            <span class="product-name-display">{{ item.product_name }}</span>
                                        </td>
                                        <td>
                                            <input type="hidden" name="product_strength[]" value="{{ item.strength }}" class="product-strength">
                                            <span class="product-strength-display">{{ item.strength }}</span>
                                        </td>
                                        <td>
                                            <input type="number" name="quantity[]" value="{{ item.quantity }}" class="form-control quantity" min="1" required>
                                        </td>
                                        <td>
                                            <input type="number" name="foc_quantity[]" value="{{ item.foc_quantity or 0 }}" class="form-control foc-quantity" min="0">
                                        </td>
                                        <td>
                                            <span class="total-quantity-display badge badge-info">{{ item.quantity + (item.foc_quantity or 0) }}</span>
                                        </td>
                                        <td>
                                            <input type="number" name="unit_price[]" value="{{ item.unit_price }}" class="form-control unit-price" step="0.01" min="0" required>
                                        </td>
                                        <td>
                                            <input type="hidden" name="line_total[]" value="{{ item.line_total }}" class="line-total">
                                            <span class="line-total-display">Rs. {{ item.line_total }}</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-danger remove-item">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="6" class="text-right">Total:</th>
                                        <th id="total-amount">Rs. {{ order.order_amount }}</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div class="row mt-3 mb-4">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-success" id="add-product-btn">
                                    <i class="fas fa-plus"></i> Add Product
                                </button>
                            </div>
                        </div>

                        <!-- Product Selection Modal -->
                        <div class="modal fade" id="productModal" tabindex="-1" role="dialog" aria-labelledby="productModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="productModalLabel">Add Product</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="form-group">
                                            <label for="product-select">Select Product</label>
                                            <select class="form-control" id="product-select">
                                                <option value="">Select a product</option>
                                                {% for product in products %}
                                                <option value="{{ product.product_id }}"
                                                        data-name="{{ product.name }}"
                                                        data-strength="{{ product.strength }}"
                                                        data-price="{{ product.unit_price }}">
                                                    {{ product.name }} ({{ product.strength }})
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="modal-quantity">Quantity</label>
                                            <input type="number" class="form-control" id="modal-quantity" min="1" value="1">
                                        </div>
                                        <div class="form-group">
                                            <label for="modal-foc-quantity">FOC Quantity</label>
                                            <input type="number" class="form-control" id="modal-foc-quantity" min="0" value="0">
                                        </div>
                                        <div class="form-group">
                                            <label for="modal-price">Unit Price (Rs.)</label>
                                            <input type="number" class="form-control" id="modal-price" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary" id="add-product-confirm">Add to Order</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12 text-right">
                                <a href="{{ url_for('orders') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update Order</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize variables
        let itemCounter = {{ order_items|length }};

        // Calculate line totals and update total amount
        function calculateTotals() {
            let totalAmount = 0;

            $('.order-item-row').each(function() {
                const quantity = parseFloat($(this).find('.quantity').val()) || 0;
                const focQuantity = parseFloat($(this).find('.foc-quantity').val()) || 0;
                const unitPrice = parseFloat($(this).find('.unit-price').val()) || 0;
                const lineTotal = quantity * unitPrice; // FOC is free, so not included in total

                // Update total quantity display
                const totalQty = quantity + focQuantity;
                $(this).find('.total-quantity-display').text(totalQty);

                // Update line total
                $(this).find('.line-total').val(lineTotal.toFixed(2));
                $(this).find('.line-total-display').text('Rs. ' + lineTotal.toFixed(2));

                // Add to total
                totalAmount += lineTotal;
            });

            // Update total amount
            $('#total-amount').text('Rs. ' + totalAmount.toFixed(2));
        }

        // Recalculate when quantity, FOC quantity, or price changes
        $(document).on('change', '.quantity, .foc-quantity, .unit-price', function() {
            calculateTotals();
        });

        // Remove item button
        $(document).on('click', '.remove-item', function() {
            if (confirm('Are you sure you want to remove this item?')) {
                $(this).closest('tr').remove();
                calculateTotals();
            }
        });

        // Add product button
        $('#add-product-btn').click(function() {
            $('#productModal').modal('show');
        });

        // Product selection change
        $('#product-select').change(function() {
            const selectedOption = $(this).find('option:selected');
            const price = selectedOption.data('price');
            $('#modal-price').val(price);
        });

        // Add product confirm
        $('#add-product-confirm').click(function() {
            const productId = $('#product-select').val();

            if (!productId) {
                alert('Please select a product');
                return;
            }

            const selectedOption = $('#product-select').find('option:selected');
            const productName = selectedOption.data('name');
            const productStrength = selectedOption.data('strength');
            const quantity = parseInt($('#modal-quantity').val()) || 1;
            const focQuantity = parseInt($('#modal-foc-quantity').val()) || 0;
            const unitPrice = parseFloat($('#modal-price').val()) || 0;
            const lineTotal = quantity * unitPrice; // FOC is free
            const totalQty = quantity + focQuantity;

            // Create new row
            const newRow = `
                <tr class="order-item-row" data-item-id="new-${itemCounter}">
                    <td>
                        <input type="hidden" name="order_item_id[]" value="new-${itemCounter}">
                        <input type="hidden" name="product_id[]" value="${productId}" class="product-id">
                        <input type="hidden" name="product_name[]" value="${productName}" class="product-name">
                        <span class="product-name-display">${productName}</span>
                    </td>
                    <td>
                        <input type="hidden" name="product_strength[]" value="${productStrength}" class="product-strength">
                        <span class="product-strength-display">${productStrength}</span>
                    </td>
                    <td>
                        <input type="number" name="quantity[]" value="${quantity}" class="form-control quantity" min="1" required>
                    </td>
                    <td>
                        <input type="number" name="foc_quantity[]" value="${focQuantity}" class="form-control foc-quantity" min="0">
                    </td>
                    <td>
                        <span class="total-quantity-display badge badge-info">${totalQty}</span>
                    </td>
                    <td>
                        <input type="number" name="unit_price[]" value="${unitPrice.toFixed(2)}" class="form-control unit-price" step="0.01" min="0" required>
                    </td>
                    <td>
                        <input type="hidden" name="line_total[]" value="${lineTotal.toFixed(2)}" class="line-total">
                        <span class="line-total-display">Rs. ${lineTotal.toFixed(2)}</span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-item">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    </td>
                </tr>
            `;

            // Add to table
            $('#order-items-body').append(newRow);
            itemCounter++;

            // Reset modal and hide
            $('#product-select').val('');
            $('#modal-quantity').val(1);
            $('#modal-foc-quantity').val(0);
            $('#modal-price').val('');
            $('#productModal').modal('hide');

            // Recalculate totals
            calculateTotals();
        });

        // Initial calculation
        calculateTotals();
    });
</script>
{% endblock %}