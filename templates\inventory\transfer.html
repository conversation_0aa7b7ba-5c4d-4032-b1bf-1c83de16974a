{% extends 'base.html' %}

{% block title %}Stock Transfer - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Stock Transfer</h4>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('transfer_stock') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="inventory_id">Select Stock to Transfer</label>
                                    <select class="form-control" id="inventory_id" name="inventory_id" required>
                                        <option value="">Select Stock</option>
                                        {% for item in inventory %}
                                        <option value="{{ item.inventory_id }}" 
                                                data-product="{{ item.product_name }}" 
                                                data-strength="{{ item.strength }}"
                                                data-batch="{{ item.batch_number }}"
                                                data-warehouse="{{ item.warehouse_name }}"
                                                data-quantity="{{ item.stock_quantity }}">
                                            {{ item.product_name }} ({{ item.strength }}) - Batch: {{ item.batch_number }} - {{ item.warehouse_name }} - {{ item.stock_quantity }} units
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="to_warehouse_id">Destination Warehouse</label>
                                    <select class="form-control" id="to_warehouse_id" name="to_warehouse_id" required>
                                        <option value="">Select Destination Warehouse</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quantity">Quantity to Transfer</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                                    <small class="form-text text-muted">Available: <span id="available-quantity">0</span> units</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light mt-4">
                                    <div class="card-body">
                                        <h5 class="card-title">Transfer Details</h5>
                                        <p><strong>Product:</strong> <span id="product-name">-</span></p>
                                        <p><strong>Batch:</strong> <span id="batch-number">-</span></p>
                                        <p><strong>From Warehouse:</strong> <span id="from-warehouse">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12 text-right">
                                <a href="{{ url_for('inventory') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Transfer Stock</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Update transfer details when inventory is selected
        $('#inventory_id').change(function() {
            var selectedOption = $(this).find('option:selected');
            
            if (selectedOption.val()) {
                var product = selectedOption.data('product');
                var strength = selectedOption.data('strength');
                var batch = selectedOption.data('batch');
                var warehouse = selectedOption.data('warehouse');
                var quantity = selectedOption.data('quantity');
                
                $('#product-name').text(product + ' (' + strength + ')');
                $('#batch-number').text(batch);
                $('#from-warehouse').text(warehouse);
                $('#available-quantity').text(quantity);
                
                // Set max quantity
                $('#quantity').attr('max', quantity);
            } else {
                $('#product-name').text('-');
                $('#batch-number').text('-');
                $('#from-warehouse').text('-');
                $('#available-quantity').text('0');
                $('#quantity').attr('max', '');
            }
        });
        
        // Validate quantity on input
        $('#quantity').on('input', function() {
            var max = parseInt($('#available-quantity').text());
            var val = parseInt($(this).val());
            
            if (val > max) {
                $(this).val(max);
            }
        });
    });
</script>
{% endblock %}
