{% extends 'base.html' %}

{% block title %}Customer Feedback{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Customer Feedback</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addFeedbackModal">
                                <i class="fas fa-plus-circle"></i> Add New Feedback
                            </button>
                            
                            <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#searchFeedbackModal">
                                <i class="fas fa-search"></i> Search Feedback
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Mobile</th>
                                    <th>Feedback</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if feedback_data %}
                                    {% for feedback in feedback_data %}
                                    <tr>
                                        <td>{{ feedback.order_id }}</td>
                                        <td>{{ feedback.customer }}</td>
                                        <td>{{ feedback.mobile }}</td>
                                        <td>{{ feedback.feedback }}</td>
                                        <td>{{ feedback.date }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">No feedback found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Feedback Modal -->
<div class="modal fade" id="addFeedbackModal" tabindex="-1" role="dialog" aria-labelledby="addFeedbackModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addFeedbackModalLabel">Add New Feedback</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{{ url_for('feedback') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="order_number">Order Number</label>
                        <input type="text" class="form-control" id="order_number" name="order_number" required>
                    </div>
                    <div class="form-group">
                        <label for="mobile_number">Customer Mobile Number</label>
                        <input type="text" class="form-control" id="mobile_number" name="mobile_number" required>
                    </div>
                    <div class="form-group">
                        <label for="testimonial">Customer Testimonial/Feedback</label>
                        <textarea class="form-control" id="testimonial" name="testimonial" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Submit Feedback</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Search Feedback Modal -->
<div class="modal fade" id="searchFeedbackModal" tabindex="-1" role="dialog" aria-labelledby="searchFeedbackModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="searchFeedbackModalLabel">Search Feedback</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="get" action="{{ url_for('feedback') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="order_number">Order Number (leave blank for all)</label>
                        <input type="text" class="form-control" id="order_number" name="order_number">
                    </div>
                    <div class="form-group">
                        <label for="mobile_number">Customer Mobile Number (leave blank for all)</label>
                        <input type="text" class="form-control" id="mobile_number" name="mobile_number">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
