{% extends "base.html" %}

{% block title %}View All Products - Medivent ERP{% endblock %}

{% block extra_css %}
<style>
    .filter-card {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
    }
    .search-box {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    .search-box:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    .table thead th {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }
    .table tbody tr {
        transition: all 0.3s ease;
    }
    .table tbody tr:hover {
        background-color: #f8f9ff;
        transform: scale(1.01);
    }
    .table td {
        padding: 15px;
        vertical-align: middle;
        border-color: #e9ecef;
    }
    .product-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 10px;
        border: 3px solid #e9ecef;
        transition: all 0.3s ease;
    }
    .product-image:hover {
        transform: scale(1.1);
        border-color: #007bff;
    }
    .badge-custom {
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    .pricing-info {
        font-size: 0.85rem;
        line-height: 1.4;
    }
    .stock-info {
        font-size: 0.85rem;
        line-height: 1.4;
    }
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }
    .action-btn {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        border: none;
        margin: 0 2px;
        transition: all 0.3s ease;
    }
    .action-btn:hover {
        transform: scale(1.1);
    }
    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
        color: #007bff;
    }
    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-list-alt text-primary"></i> View All Products</h2>
            <p class="text-muted">Browse and view all products in your inventory catalog</p>
        </div>
        <div>
            <button class="btn btn-outline-success me-2" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Export to Excel
            </button>
            <button class="btn btn-outline-primary me-2" onclick="window.print()">
                <i class="fas fa-print"></i> Print Catalog
            </button>
            <button class="btn btn-primary" onclick="toggleAdvancedSearch()">
                <i class="fas fa-search"></i> Advanced Search
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.total }}</h3>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div>
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.available }}</h3>
                            <p class="mb-0">In Stock</p>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.low_stock }}</h3>
                            <p class="mb-0">Low Stock</p>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.out_of_stock }}</h3>
                            <p class="mb-0">Out of Stock</p>
                        </div>
                        <div>
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search & Filter Bar -->
    <div class="filter-card">
        <form method="GET" action="{{ url_for('view_all_products') }}" id="filterForm">
            <div class="row align-items-end">
                <div class="col-md-5">
                    <label><strong>Search Product Catalog</strong></label>
                    <div class="input-group">
                        <input type="text" name="q" class="form-control search-box" 
                               placeholder="Search by product name, generic, manufacturer..." 
                               value="{{ filters.search or '' }}">
                        <div class="input-group-append">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label><strong>Filter by Generic</strong></label>
                    <select name="generic" class="form-control" onchange="document.getElementById('filterForm').submit()">
                        <option value="">All Categories</option>
                        {% for category in generic_categories %}
                        <option value="{{ category.category }}" 
                                {% if filters.generic == category.category %}selected{% endif %}>
                            {{ category.category }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label><strong>Sort by</strong></label>
                    <select name="sort" class="form-control" onchange="document.getElementById('filterForm').submit()">
                        <option value="name_asc" {% if filters.sort == 'name_asc' %}selected{% endif %}>Product Name A-Z</option>
                        <option value="name_desc" {% if filters.sort == 'name_desc' %}selected{% endif %}>Product Name Z-A</option>
                        <option value="price_low" {% if filters.sort == 'price_low' %}selected{% endif %}>Price Low to High</option>
                        <option value="price_high" {% if filters.sort == 'price_high' %}selected{% endif %}>Price High to Low</option>
                        <option value="stock" {% if filters.sort == 'stock' %}selected{% endif %}>Stock Level</option>
                        <option value="manufacturer" {% if filters.sort == 'manufacturer' %}selected{% endif %}>Manufacturer</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label><strong>View</strong></label>
                    <div class="btn-group btn-block" role="group">
                        <button type="button" class="btn btn-outline-primary active" id="listView">
                            <i class="fas fa-th-list"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="gridView">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-filter"></i> Apply Filters
                    </button>
                    <a href="{{ url_for('view_all_products') }}" class="btn btn-outline-secondary btn-block mt-2">
                        <i class="fas fa-times"></i> Clear Filters
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Product Catalog -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0"><i class="fas fa-list text-primary"></i> Product Catalog</h5>
            <small class="text-muted">Best view of all products</small>
        </div>

        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="8%">Image</th>
                    <th width="20%">Product Information</th>
                    <th width="10%">Strength</th>
                    <th width="12%">Generic Category</th>
                    <th width="12%">Manufacturer</th>
                    <th width="10%">Division</th>
                    <th width="12%">Pricing Details</th>
                    <th width="8%">Stock Info</th>
                    <th width="8%">Status</th>
                    <th width="5%">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% if products %}
                    {% for product in products %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            {% if product.image_url %}
                                <img src="{{ product.image_url }}" alt="{{ product.name }}" class="product-image">
                            {% else %}
                                <img src="https://via.placeholder.com/60x60/007bff/ffffff?text={{ product.name[:2]|upper }}"
                                     alt="{{ product.name }}" class="product-image">
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <strong>{{ product.name }}</strong><br>
                                <small class="text-muted">{{ product.product_id }}</small><br>
                                {% if product.email %}
                                <small class="text-info">{{ product.email }}</small><br>
                                {% endif %}
                                <small class="text-success">{{ product.status|title }} Product</small>
                            </div>
                        </td>
                        <td>
                            {% if product.strength %}
                                <span class="badge badge-custom" style="background-color: #007bff; color: white;">
                                    {{ product.strength }}
                                </span>
                            {% else %}
                                <span class="badge badge-custom" style="background-color: #6c757d; color: white;">
                                    N/A
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge badge-custom" style="background-color: #6c757d; color: white;">
                                {{ product.generic_category or 'Unassigned' }}
                            </span><br>
                            {% if product.category and product.category != product.generic_category %}
                            <small class="text-muted">{{ product.category }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.manufacturer or 'Unknown' }}</strong><br>
                            <small class="text-muted">{{ product.manufacturer_type or 'Pharmaceutical' }}</small>
                        </td>
                        <td>
                            <span class="badge badge-custom" style="background-color: #17a2b8; color: white;">
                                {{ product.division_name or 'Medicine' }}
                            </span>
                        </td>
                        <td>
                            <div class="pricing-info">
                                <strong>MRP: ₹{{ "%.2f"|format(product.mrp or 0) }}</strong><br>
                                <small>Ptr: ₹{{ "%.2f"|format(product.tp_rate or 0) }}</small><br>
                                <small>Avg: ₹{{ "%.2f"|format(product.asp or 0) }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="stock-info">
                                {% if product.stock_status == 'Available' %}
                                    <span class="badge badge-custom" style="background-color: #28a745; color: white;">In Stock</span>
                                {% elif product.stock_status == 'Low Stock' %}
                                    <span class="badge badge-custom" style="background-color: #ffc107; color: black;">Low Stock</span>
                                {% else %}
                                    <span class="badge badge-custom" style="background-color: #dc3545; color: white;">Out of Stock</span>
                                {% endif %}
                                <br>
                                <small>Qty: {{ product.stock_quantity or 0 }}</small>
                            </div>
                        </td>
                        <td>
                            {% if product.status == 'active' %}
                                <span class="badge badge-custom" style="background-color: #28a745; color: white;">Active</span>
                            {% else %}
                                <span class="badge badge-custom" style="background-color: #dc3545; color: white;">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <button class="action-btn btn btn-primary btn-sm"
                                    onclick="viewProduct('{{ product.product_id }}')"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="11" class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No products found</h5>
                            <p class="text-muted">Try adjusting your search criteria or add some products.</p>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Pagination & Summary -->
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
            <p class="mb-0 text-muted">
                <i class="fas fa-info-circle"></i>
                <strong>Catalog Summary:</strong> Displaying {{ products|length }} of {{ stats.total }} total products •
                {{ stats.available }} Available • {{ stats.low_stock }} Low Stock • {{ stats.out_of_stock }} Out of Stock
            </p>
        </div>
        <nav aria-label="Product pagination">
            <ul class="pagination mb-0">
                <li class="page-item">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item"><a class="page-link" href="#">...</a></li>
                <li class="page-item"><a class="page-link" href="#">{{ (stats.total / 50)|round|int }}</a></li>
                <li class="page-item">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // View toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const listViewBtn = document.getElementById('listView');
        const gridViewBtn = document.getElementById('gridView');

        listViewBtn.addEventListener('click', function() {
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
            // Add list view logic here
        });

        gridViewBtn.addEventListener('click', function() {
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            // Add grid view logic here
        });
    });

    // Export to Excel functionality
    function exportToExcel() {
        // Create a form to submit export request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("view_all_products") }}';

        const exportField = document.createElement('input');
        exportField.type = 'hidden';
        exportField.name = 'export';
        exportField.value = 'excel';
        form.appendChild(exportField);

        // Add current filters to export
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.forEach((value, key) => {
            const field = document.createElement('input');
            field.type = 'hidden';
            field.name = key;
            field.value = value;
            form.appendChild(field);
        });

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Advanced search toggle
    function toggleAdvancedSearch() {
        // Toggle advanced search panel
        const filterCard = document.querySelector('.filter-card');
        if (filterCard.style.display === 'none') {
            filterCard.style.display = 'block';
        } else {
            filterCard.style.display = 'none';
        }
    }

    // View product details
    function viewProduct(productId) {
        // Navigate to product details page
        window.location.href = `/products/${productId}`;
    }

    // Auto-submit search on Enter key
    document.querySelector('input[name="q"]').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('filterForm').submit();
        }
    });

    // Add loading state to buttons
    document.getElementById('filterForm').addEventListener('submit', function() {
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
        submitBtn.disabled = true;
    });
</script>
{% endblock %}
