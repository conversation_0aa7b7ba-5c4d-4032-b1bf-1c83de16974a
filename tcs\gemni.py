import asyncio
from playwright.async_api import async_playwright

async def get_tcs_tracking_data_playwright(tracking_number: str):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True) # Set headless=False to see the browser UI
        page = await browser.new_page()

        print(f"Navigating to https://www.tcsexpress.com/track")
        await page.goto("https://www.tcsexpress.com/track")

        # Locate the input field and fill it
        # XPath for the input field: //*[@id="main-content-app"]/div/div[2]/div[2]/div/input
        print(f"Entering tracking number: {tracking_number}")
        await page.locator('xpath=//*[@id="main-content-app"]/div/div[2]/div[2]/div/input').fill(tracking_number)

        # Locate the track button and click it
        # XPath for the button: //*[@id="main-content-app"]/div/div[2]/div[2]/div/button
        print("Clicking 'Track Shipment' button")
        await page.locator('xpath=//*[@id="main-content-app"]/div/div[2]/div[2]/div/button').click()

        # Wait for the results to load. You might need to adjust this wait condition.
        # Waiting for a specific element that appears after tracking results are loaded.
        # The provided data is within //*[@id="main-content-app"]/div/div[3]/div
        print("Waiting for tracking results to load...")
        try:
            await page.wait_for_selector('xpath=//*[@id="main-content-app"]/div/div[3]/div', timeout=10000) # 10 seconds timeout
            print("Tracking results container found.")
        except Exception as e:
            print(f"Timeout waiting for tracking results: {e}")
            await browser.close()
            return {"error": "Tracking results did not load in time or element not found."}

        # Now, extract the data from the loaded content
        results_data = {}
        
        # Locate the main data container
        main_content_locator = page.locator('xpath=//*[@id="main-content-app"]/div/div[3]/div')

        # Extract Shipment Booking Details
        try:
            booking_details_section = main_content_locator.locator('.//div[./h2[contains(text(), "Shipment Booking Details")]]')
            results_data['trackingNumber'] = await booking_details_section.locator('//p[contains(text(), "Tracking Number:")]/span').text_content()
            results_data['agentReferenceNumber'] = await booking_details_section.locator('//div[p[contains(text(), "Agent Reference Number:")]]/p[2]').text_content()
            results_data['origin'] = await booking_details_section.locator('//div[p[contains(text(), "Origin:")]]/p[2]').text_content()
            results_data['destination'] = await booking_details_section.locator('//div[p[contains(text(), "Destination:")]]/p[2]').text_content()
            results_data['bookingDate'] = await booking_details_section.locator('//div[p[contains(text(), "Booking Date:")]]/p[2]').text_content()
        except Exception as e:
            print(f"Could not extract booking details: {e}")
            results_data['bookingDetailsError'] = "Some booking details not found."

        # Extract Shipment Track Summary
        try:
            track_summary_section = main_content_locator.locator('.//div[./h2[contains(text(), "Shipment Track Summary")]]')
            results_data['currentStatus'] = await track_summary_section.locator('//p[contains(text(), "Current Status:")]/span').text_content()
            results_data['deliveredOn'] = await track_summary_section.locator('//div[p[contains(text(), "Delivered On:")]]/p[2]').text_content()
            results_data['receivedBy'] = await track_summary_section.locator('//div[p[contains(text(), "Received by:")]]/p[2]').text_content()
        except Exception as e:
            print(f"Could not extract track summary: {e}")
            results_data['trackSummaryError'] = "Some track summary details not found."

        # Extract the separate current status block
        try:
            overall_status_div = main_content_locator.locator('.//div[contains(@class, "mt-[2rem]")]')
            results_data['overallCurrentStatus'] = (await overall_status_div.locator('./div[1]').text_content()).replace('Current Status:', '').strip()
            results_data['overallDeliveredOn'] = (await overall_status_div.locator('./div[2]').text_content()).replace('Delivered On:', '').strip()
            results_data['overallSignedBy'] = (await overall_status_div.locator('./div[3]').text_content()).replace('Signed By:', '').strip()
        except Exception as e:
            print(f"Could not extract overall status block: {e}")
            results_data['overallStatusError'] = "Overall status details not found."

        # Extract Track History (table)
        track_history = []
        try:
            table_rows = await main_content_locator.locator('//table/tbody/tr').all()
            for row in table_rows:
                date_time = await row.locator('./td[1]').text_content()
                status = await row.locator('./td[2]').text_content()
                track_history.append({
                    'dateTime': date_time.replace('\n', ' ').strip(),
                    'status': status.replace('\n', ' ').strip()
                })
            results_data['trackHistory'] = track_history
        except Exception as e:
            print(f"Could not extract track history: {e}")
            results_data['trackHistoryError'] = "Track history table not found."
            
        await browser.close()
        return results_data

# Example Usage:
async def main():
    tracking_id = "31442084039" # Use the provided tracking number
    data = await get_tcs_tracking_data_playwright(tracking_id)
    import json
    print(json.dumps(data, indent=4))

if __name__ == "__main__":
    asyncio.run(main())