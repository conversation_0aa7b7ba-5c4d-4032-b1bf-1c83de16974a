"""
Data Processor Module
Data processing and analysis functionality for the ERP system
"""

import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import json

def process_sales_data(db_connection, start_date=None, end_date=None):
    """Process sales data for analysis"""
    try:
        query = """
        SELECT 
            DATE(order_date) as date,
            SUM(order_amount) as total_sales,
            COUNT(*) as order_count
        FROM orders 
        WHERE 1=1
        """
        
        params = []
        if start_date:
            query += " AND order_date >= ?"
            params.append(start_date)
        if end_date:
            query += " AND order_date <= ?"
            params.append(end_date)
            
        query += " GROUP BY DATE(order_date) ORDER BY date"
        
        df = pd.read_sql_query(query, db_connection, params=params)
        return df.to_dict('records')
    except Exception as e:
        print(f"Sales data processing error: {e}")
        return []

def process_inventory_data(db_connection):
    """Process inventory data for analysis"""
    try:
        query = """
        SELECT 
            p.name as product_name,
            SUM(i.stock_quantity) as total_stock,
            SUM(i.allocated_quantity) as allocated_stock,
            (SUM(i.stock_quantity) - SUM(i.allocated_quantity)) as available_stock
        FROM inventory i
        JOIN products p ON i.product_id = p.product_id
        GROUP BY p.product_id, p.name
        ORDER BY total_stock DESC
        """
        
        df = pd.read_sql_query(query, db_connection)
        return df.to_dict('records')
    except Exception as e:
        print(f"Inventory data processing error: {e}")
        return []

def process_customer_data(db_connection):
    """Process customer data for analysis"""
    try:
        query = """
        SELECT 
            c.name as customer_name,
            COUNT(o.order_id) as order_count,
            SUM(o.order_amount) as total_amount,
            AVG(o.order_amount) as avg_order_value
        FROM customers c
        LEFT JOIN orders o ON c.customer_id = o.customer_id
        GROUP BY c.customer_id, c.name
        ORDER BY total_amount DESC
        """
        
        df = pd.read_sql_query(query, db_connection)
        return df.to_dict('records')
    except Exception as e:
        print(f"Customer data processing error: {e}")
        return []

def calculate_kpis(db_connection):
    """Calculate key performance indicators"""
    try:
        kpis = {}
        
        # Total sales this month
        query = """
        SELECT SUM(order_amount) as total_sales
        FROM orders 
        WHERE strftime('%Y-%m', order_date) = strftime('%Y-%m', 'now')
        """
        result = db_connection.execute(query).fetchone()
        kpis['monthly_sales'] = result[0] if result[0] else 0
        
        # Total orders this month
        query = """
        SELECT COUNT(*) as order_count
        FROM orders 
        WHERE strftime('%Y-%m', order_date) = strftime('%Y-%m', 'now')
        """
        result = db_connection.execute(query).fetchone()
        kpis['monthly_orders'] = result[0] if result[0] else 0
        
        # Active customers
        query = """
        SELECT COUNT(DISTINCT customer_id) as active_customers
        FROM orders 
        WHERE order_date >= date('now', '-30 days')
        """
        result = db_connection.execute(query).fetchone()
        kpis['active_customers'] = result[0] if result[0] else 0
        
        # Low stock products
        query = """
        SELECT COUNT(*) as low_stock_count
        FROM inventory 
        WHERE stock_quantity <= 10
        """
        result = db_connection.execute(query).fetchone()
        kpis['low_stock_products'] = result[0] if result[0] else 0
        
        return kpis
    except Exception as e:
        print(f"KPI calculation error: {e}")
        return {}

def generate_report_data(db_connection, report_type='sales'):
    """Generate data for various reports"""
    try:
        if report_type == 'sales':
            return process_sales_data(db_connection)
        elif report_type == 'inventory':
            return process_inventory_data(db_connection)
        elif report_type == 'customers':
            return process_customer_data(db_connection)
        else:
            return []
    except Exception as e:
        print(f"Report data generation error: {e}")
        return []
