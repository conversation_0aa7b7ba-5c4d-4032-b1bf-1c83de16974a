"""
Final TCS Express Tracking Scraper
Robust implementation that handles real TCS website structure
"""

import asyncio
import json
import re
from typing import Dict, Any, List
from playwright.async_api import async_playwright, Page
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TCSTrackerFinal:
    """Final implementation of TCS Express tracking scraper"""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.base_url = "https://www.tcsexpress.com/track"
        
    async def track_shipment(self, tracking_number: str) -> Dict[str, Any]:
        """Track a shipment by tracking number"""
        playwright = None
        browser = None
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
            )
            
            page = await browser.new_page()
            
            # Navigate to the tracking page
            url = f"{self.base_url}?tracking_number={tracking_number}"
            logger.info(f"Navigating to: {url}")
            
            await page.goto(url, wait_until='networkidle', timeout=30000)
            
            # Wait for the main content to load
            try:
                await page.wait_for_selector('#main-content-app', timeout=10000)
            except:
                logger.warning("Main content selector not found, continuing...")
            
            # Wait for dynamic content
            await asyncio.sleep(3)
            
            # Check if we have tracking data
            page_content = await page.content()
            
            # Look for the tracking data container
            tracking_container = None
            selectors_to_try = [
                'div.w-\\[70rem\\].shadow-card.p-8',
                'div[class*="shadow-card"]',
                'div[class*="w-"][class*="rem"]',
                '.shadow-card',
                '[class*="tracking"]',
                '[class*="shipment"]'
            ]
            
            for selector in selectors_to_try:
                try:
                    tracking_container = await page.query_selector(selector)
                    if tracking_container:
                        logger.info(f"Found tracking container with selector: {selector}")
                        break
                except:
                    continue
            
            if not tracking_container:
                # Try to find any container with tracking information
                all_text = await page.text_content('body')
                if tracking_number in all_text:
                    logger.info("Tracking number found in page, extracting from full content")
                    return await self._extract_from_full_page(page, tracking_number)
                else:
                    return self._create_error_response(tracking_number, "Tracking information not found on page")
            
            # Extract data from the container
            return await self._extract_from_container(page, tracking_container, tracking_number)
            
        except Exception as e:
            logger.error(f"Error tracking shipment {tracking_number}: {e}")
            return self._create_error_response(tracking_number, f"System error: {str(e)}")
            
        finally:
            if browser:
                await browser.close()
            if playwright:
                await playwright.stop()
                
    async def _extract_from_container(self, page: Page, container, tracking_number: str) -> Dict[str, Any]:
        """Extract data from the tracking container"""
        try:
            result = {
                'success': True,
                'tracking_number': tracking_number,
                'track_history': []
            }
            
            # Get container HTML for analysis
            container_html = await container.inner_html()
            container_text = await container.text_content()
            
            # Extract tracking number (verification)
            tracking_match = re.search(r'(\d{11,})', container_text)
            if tracking_match:
                result['tracking_number'] = tracking_match.group(1)
            
            # Extract origin and destination
            origin_match = re.search(r'Origin[:\s]*([A-Z\s]+?)(?:Destination|$)', container_text, re.IGNORECASE)
            if origin_match:
                result['origin'] = origin_match.group(1).strip()
                
            dest_match = re.search(r'Destination[:\s]*([A-Z\s]+?)(?:Booking|Origin|$)', container_text, re.IGNORECASE)
            if dest_match:
                result['destination'] = dest_match.group(1).strip()
            
            # Extract booking date
            booking_match = re.search(r'Booking Date[:\s]*([A-Za-z0-9\s,]+?)(?:Origin|Destination|$)', container_text, re.IGNORECASE)
            if booking_match:
                result['booking_date'] = booking_match.group(1).strip()
            
            # Extract current status
            status_match = re.search(r'Current Status[:\s]*([A-Za-z\s]+?)(?:Delivered|$)', container_text, re.IGNORECASE)
            if status_match:
                result['current_status'] = status_match.group(1).strip()
            
            # Extract delivery information
            delivered_match = re.search(r'Delivered On[:\s]*([A-Za-z0-9\s,:]+?)(?:Received|$)', container_text, re.IGNORECASE)
            if delivered_match:
                result['delivered_on'] = delivered_match.group(1).strip()
                
            received_match = re.search(r'Received by[:\s]*([A-Za-z\s]+?)(?:$)', container_text, re.IGNORECASE)
            if received_match:
                result['received_by'] = received_match.group(1).strip()
            
            # Extract track history from table
            await self._extract_track_history(page, result)
            
            # Set defaults
            self._set_defaults(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting from container: {e}")
            return self._create_error_response(tracking_number, f"Data extraction error: {str(e)}")
            
    async def _extract_from_full_page(self, page: Page, tracking_number: str) -> Dict[str, Any]:
        """Extract data from full page when container not found"""
        try:
            result = {
                'success': True,
                'tracking_number': tracking_number,
                'track_history': []
            }
            
            # Get full page text
            full_text = await page.text_content('body')
            
            # Use regex patterns to extract information
            patterns = {
                'origin': r'Origin[:\s]*([A-Z\s]+?)(?:Destination|Booking|$)',
                'destination': r'Destination[:\s]*([A-Z\s]+?)(?:Booking|Origin|$)',
                'booking_date': r'Booking Date[:\s]*([A-Za-z0-9\s,]+?)(?:Origin|Destination|$)',
                'current_status': r'Current Status[:\s]*([A-Za-z\s]+?)(?:Delivered|$)',
                'delivered_on': r'Delivered On[:\s]*([A-Za-z0-9\s,:]+?)(?:Received|$)',
                'received_by': r'Received by[:\s]*([A-Za-z\s]+?)(?:$)'
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, full_text, re.IGNORECASE)
                if match:
                    result[key] = match.group(1).strip()
            
            # Extract track history
            await self._extract_track_history(page, result)
            
            # Set defaults
            self._set_defaults(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting from full page: {e}")
            return self._create_error_response(tracking_number, f"Full page extraction error: {str(e)}")
            
    async def _extract_track_history(self, page: Page, result: Dict[str, Any]):
        """Extract tracking history from table"""
        try:
            # Look for table
            table = await page.query_selector('table')
            if not table:
                return
                
            # Get all rows
            rows = await table.query_selector_all('tr')
            
            for row in rows:
                cells = await row.query_selector_all('td')
                if len(cells) >= 2:
                    # Get date/time
                    date_cell = cells[0]
                    date_text = await date_cell.text_content()
                    
                    # Get status
                    status_cell = cells[1]
                    status_text = await status_cell.text_content()
                    
                    if date_text and status_text:
                        # Clean up text
                        date_clean = re.sub(r'\s+', ' ', date_text.strip())
                        status_lines = status_text.strip().split('\n')
                        
                        status = status_lines[0].strip() if status_lines else ''
                        location = status_lines[1].strip() if len(status_lines) > 1 else ''
                        
                        # Remove HTML tags if any
                        status = re.sub(r'<[^>]+>', '', status)
                        location = re.sub(r'<[^>]+>', '', location)
                        
                        if status:  # Only add if we have actual status
                            result['track_history'].append({
                                'date_time': date_clean,
                                'status': status,
                                'location': location
                            })
                            
        except Exception as e:
            logger.error(f"Error extracting track history: {e}")
            
    def _set_defaults(self, result: Dict[str, Any]):
        """Set default values for missing fields"""
        defaults = {
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Unknown',
            'delivered_on': 'N/A',
            'received_by': 'N/A'
        }
        
        for key, default_value in defaults.items():
            if key not in result or not result[key] or result[key].strip() == '':
                result[key] = default_value
                
    def _create_error_response(self, tracking_number: str, error_message: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            'success': False,
            'tracking_number': tracking_number,
            'error': error_message,
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Error',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }

# Synchronous wrapper for Flask
def track_tcs_final(tracking_number: str, headless: bool = True) -> Dict[str, Any]:
    """Synchronous wrapper for Flask integration"""
    async def _track():
        tracker = TCSTrackerFinal(headless=headless)
        return await tracker.track_shipment(tracking_number)
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_track())
        loop.close()
        return result
    except Exception as e:
        logger.error(f"Error in synchronous wrapper: {e}")
        return {
            'success': False,
            'tracking_number': tracking_number,
            'error': f"System error: {str(e)}",
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Error',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }

# Test function
async def test_final():
    """Test the final implementation"""
    test_numbers = ["31442084039", "31442083394", "31442083525"]
    
    tracker = TCSTrackerFinal(headless=False)
    
    for number in test_numbers:
        print(f"\n=== Testing {number} ===")
        result = await tracker.track_shipment(number)
        print(json.dumps(result, indent=2))
        await asyncio.sleep(2)

if __name__ == "__main__":
    asyncio.run(test_final())
