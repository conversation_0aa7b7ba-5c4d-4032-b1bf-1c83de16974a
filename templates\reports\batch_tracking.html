{% extends 'base.html' %}

{% block title %}Batch Tracking Report{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Batch Tracking Report</h5>
                        <div>
                            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Reports
                            </a>
                            <a href="{{ url_for('export_batch_tracking', batch_number=batch_number, product_id=product_id, format='excel') }}" class="btn btn-success btn-sm ml-2">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a>
                            <a href="{{ url_for('export_batch_tracking', batch_number=batch_number, product_id=product_id, format='pdf') }}" class="btn btn-danger btn-sm ml-2">
                                <i class="fas fa-file-pdf"></i> Export to PDF
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form action="{{ url_for('batch_tracking_report') }}" method="GET">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <label for="batch_number">Batch Number</label>
                                            <input type="text" class="form-control" id="batch_number" name="batch_number" value="{{ batch_number }}" placeholder="Enter batch number...">
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <label for="product_id">Product</label>
                                            <select class="form-control" id="product_id" name="product_id">
                                                <option value="">All Products</option>
                                                {% for product in products %}
                                                <option value="{{ product.product_id }}" {% if product_id == product.product_id %}selected{% endif %}>{{ product.name }} ({{ product.strength }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-search"></i> Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Batch Information -->
                    {% if batches %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>Batch Information</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Batch Number</th>
                                            <th>Product</th>
                                            <th>Warehouse</th>
                                            <th>Manufacturing Date</th>
                                            <th>Expiry Date</th>
                                            <th>Current Stock</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for batch in batches %}
                                        <tr>
                                            <td>{{ batch.batch_number }}</td>
                                            <td>{{ batch.product_name }} ({{ batch.strength }})</td>
                                            <td>{{ batch.warehouse_name }}</td>
                                            <td>{{ batch.manufacturing_date }}</td>
                                            <td>{{ batch.expiry_date }}</td>
                                            <td>{{ batch.stock_quantity }}</td>
                                            <td>
                                                {% if batch.status == 'active' %}
                                                <span class="badge badge-success">Active</span>
                                                {% else %}
                                                <span class="badge badge-secondary">Inactive</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Movement History -->
                    {% if batch_movements %}
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Movement History</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Date</th>
                                            <th>Movement Type</th>
                                            <th>Product</th>
                                            <th>From</th>
                                            <th>To</th>
                                            <th>Quantity</th>
                                            <th>Reference</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for movement in batch_movements %}
                                        <tr>
                                            <td>{{ movement.movement_date }}</td>
                                            <td>{{ movement.movement_type }}</td>
                                            <td>{{ movement.product_name }}</td>
                                            <td>{{ movement.from_warehouse_name or 'N/A' }}</td>
                                            <td>{{ movement.to_warehouse_name or 'N/A' }}</td>
                                            <td>{{ movement.quantity }}</td>
                                            <td>{{ movement.reference_id or 'N/A' }}</td>
                                            <td>{{ movement.notes or 'N/A' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No movement history found for these batches.
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No batches found matching your search criteria. Please try different search parameters.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Auto-submit form when product selection changes
        $('#product_id').change(function() {
            if ($(this).val() !== '') {
                $(this).closest('form').submit();
            }
        });
    });
</script>
{% endblock %}
