{% extends 'base.html' %}

{% block title %}Expiry Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Expiry Report</h4>
                    <div>
                        <button class="btn btn-light" id="printExpiryBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('expiry_report') }}" method="get" class="form-inline">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Show items expiring within</span>
                                    </div>
                                    <input type="number" name="months" class="form-control" value="{{ months }}" min="1">
                                    <div class="input-group-append">
                                        <span class="input-group-text">months</span>
                                        <button class="btn btn-primary" type="submit">Apply</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-calendar-times"></i> Showing items expiring within {{ months }} months
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product ID</th>
                                    <th>Product Name</th>
                                    <th>Strength</th>
                                    <th>Batch #</th>
                                    <th>Warehouse</th>
                                    <th>Expiry Date</th>
                                    <th>Days Left</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if expiring_items %}
                                    {% for item in expiring_items %}
                                    <tr>
                                        <td>{{ item.product_id }}</td>
                                        <td>{{ item.product_name }}</td>
                                        <td>{{ item.strength }}</td>
                                        <td>{{ item.batch_number }}</td>
                                        <td>{{ item.warehouse_name }}</td>
                                        <td>{{ item.expiry_date }}</td>
                                        <td>{{ item.days_until_expiry|int }}</td>
                                        <td>{{ item.stock_quantity }} {{ item.unit_of_measure }}</td>
                                        <td>
                                            {% if item.days_until_expiry <= 30 %}
                                            <span class="badge badge-danger">Critical</span>
                                            {% elif item.days_until_expiry <= 90 %}
                                            <span class="badge badge-warning">Warning</span>
                                            {% else %}
                                            <span class="badge badge-info">Approaching</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center">No items found expiring within {{ months }} months</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Summary</h5>
                                    <p><strong>Total Expiring Items:</strong> {{ expiring_items|length }}</p>
                                    <p><strong>Critical (< 30 days):</strong> {{ expiring_items|selectattr('days_until_expiry', 'le', 30)|list|length }}</p>
                                    <p><strong>Warning (30-90 days):</strong> {{ expiring_items|selectattr('days_until_expiry', 'gt', 30)|selectattr('days_until_expiry', 'le', 90)|list|length }}</p>
                                    <p><strong>Approaching (> 90 days):</strong> {{ expiring_items|selectattr('days_until_expiry', 'gt', 90)|list|length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printExpiryBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}