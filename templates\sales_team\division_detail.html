{% extends "base.html" %}

{% block title %}{{ division_name }} Division - Sales Team{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('sales_team_dashboard') }}">Sales Team</a></li>
                    <li class="breadcrumb-item active">{{ division_name }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-4">🏢 {{ division_name }} Division Performance</h1>
            <p class="text-muted">Detailed analytics and performance metrics for {{ division_name }} division</p>
        </div>
    </div>

    <!-- Division Overview Cards -->
    {% if division_data %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">{{ division_data.total_orders }}</h2>
                    <p class="mb-0">Total Orders</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">Rs.{{ "{:,.0f}".format(division_data.total_revenue) }}</h2>
                    <p class="mb-0">Total Revenue</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">Rs.{{ "{:,.0f}".format(division_data.avg_order_value) }}</h2>
                    <p class="mb-0">Avg Order Value</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">{{ division_data.unique_customers }}</h2>
                    <p class="mb-0">Unique Customers</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row mb-4">
        <!-- Top Customers -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">🏆 Top Customers</h5>
                </div>
                <div class="card-body">
                    {% if top_customers %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                    <th>Last Order</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in top_customers %}
                                <tr>
                                    <td>
                                        <strong>{{ customer.customer_name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ customer.order_count }}</span>
                                    </td>
                                    <td>
                                        <strong class="text-success">Rs.{{ "{:,.0f}".format(customer.total_spent) }}</strong>
                                    </td>
                                    <td>
                                        <small>{{ customer.last_order }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h5>No customer data available</h5>
                        <p>No customers found for this division.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Division Sales Agents -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">👥 Sales Agents Performance</h5>
                </div>
                <div class="card-body">
                    {% if division_agents %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Agent Name</th>
                                    <th>Orders</th>
                                    <th>Revenue</th>
                                    <th>Avg Order</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for agent in division_agents %}
                                <tr>
                                    <td>
                                        <strong>{{ agent.sales_agent }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ agent.orders }}</span>
                                    </td>
                                    <td>
                                        <strong class="text-success">Rs.{{ "{:,.0f}".format(agent.revenue) }}</strong>
                                    </td>
                                    <td>
                                        <span class="text-info">Rs.{{ "{:,.0f}".format(agent.avg_order) }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-tie fa-3x mb-3"></i>
                        <h5>No agent data available</h5>
                        <p>No sales agents found for this division.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Charts -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">📊 Agent Performance Chart</h5>
                </div>
                <div class="card-body">
                    <canvas id="agentPerformanceChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">📈 Customer Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="customerDistributionChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Performance Chart -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">📈 {{ division_name }} Monthly Performance</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyPerformanceChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders for this Division ONLY -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">📋 Recent {{ division_name }} Orders ONLY</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a href="{{ url_for('orders') }}?division={{ division_name }}" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All {{ division_name }} Orders
                        </a>
                    </div>

                    {% if recent_division_orders %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Sales Agent</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_division_orders %}
                                <tr>
                                    <td><strong class="text-primary">{{ order.order_id }}</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                    <td><strong class="text-success">Rs.{{ "{:,.0f}".format(order.order_amount) }}</strong></td>
                                    <td>{{ order.sales_agent }}</td>
                                    <td>{{ order.order_date }}</td>
                                    <td>
                                        {% if order.status == 'Delivered' %}
                                            <span class="badge badge-success">{{ order.status }}</span>
                                        {% elif order.status == 'Dispatched' %}
                                            <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == 'Processing' %}
                                            <span class="badge badge-warning">{{ order.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>No recent orders found for {{ division_name }} division.</strong>
                        <p class="mb-0 mt-2">This division may not have any recent orders or the division name might be different in the database.</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('sales_team_dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Sales Dashboard
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('reports') }}?division={{ division_name }}" class="btn btn-success">
                                <i class="fas fa-chart-bar"></i> {{ division_name }} Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Agent Performance Chart
const agentCtx = document.getElementById('agentPerformanceChart').getContext('2d');
const agentChart = new Chart(agentCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for agent in division_agents %}
            '{{ agent.sales_agent }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Revenue (Rs.)',
            data: [
                {% for agent in division_agents %}
                {{ agent.revenue }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: '#36A2EB',
            borderColor: '#1E88E5',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'Rs.' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Revenue: Rs.' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});

// Customer Distribution Chart
const customerCtx = document.getElementById('customerDistributionChart').getContext('2d');
const customerChart = new Chart(customerCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for customer in top_customers[:5] %}
            '{{ customer.customer_name[:20] }}...'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for customer in top_customers[:5] %}
                {{ customer.total_spent }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': Rs.' + context.parsed.toLocaleString();
                    }
                }
            }
        }
    }
});

// Monthly Performance Chart
const monthlyCtx = document.getElementById('monthlyPerformanceChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month in monthly_performance %}
            '{{ month.month }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Revenue (Rs.)',
            data: [
                {% for month in monthly_performance %}
                {{ month.revenue }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 3,
            fill: true
        }, {
            label: 'Orders',
            data: [
                {% for month in monthly_performance %}
                {{ month.orders * 1000 }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                ticks: {
                    callback: function(value) {
                        return 'Rs.' + value.toLocaleString();
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    callback: function(value) {
                        return (value / 1000) + ' orders';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        if (context.datasetIndex === 0) {
                            return 'Revenue: Rs.' + context.parsed.y.toLocaleString();
                        } else {
                            return 'Orders: ' + (context.parsed.y / 1000);
                        }
                    }
                }
            }
        }
    }
});
</script>

<style>
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.breadcrumb {
    background-color: #f8f9fa;
    border-radius: 10px;
}
</style>
{% endblock %}
