{% extends 'base.html' %}

{% block title %}Batches by Product{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Batches by Product</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> View All Batches
                            </a>
                            <a href="{{ url_for('batches', view='details') }}" class="btn btn-secondary">
                                <i class="fas fa-info-circle"></i> View Batch Details
                            </a>
                            <a href="{{ url_for('batches', view='movements') }}" class="btn btn-secondary">
                                <i class="fas fa-exchange-alt"></i> View Batch Movements
                            </a>
                            <a href="{{ url_for('batches', view='deliveries') }}" class="btn btn-secondary">
                                <i class="fas fa-truck"></i> View Batch Deliveries
                            </a>
                            <a href="{{ url_for('batches', view='expiring') }}" class="btn btn-secondary">
                                <i class="fas fa-exclamation-triangle"></i> View Expiring Batches
                            </a>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addBatchModal">
                                <i class="fas fa-plus-circle"></i> Add New Batch
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="get" action="{{ url_for('batches', view='by_product') }}">
                                <div class="form-group">
                                    <label for="product_id">Select Product</label>
                                    <select class="form-control" id="product_id" name="product_id" onchange="this.form.submit()">
                                        <option value="">-- Select Product --</option>
                                        {% for product in products %}
                                        <option value="{{ product.product_id }}" {% if selected_product == product.product_id %}selected{% endif %}>{{ product.name }} ({{ product.strength }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    {% if selected_product %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">Batches for {{ products|selectattr('product_id', 'equalto', selected_product)|map(attribute='name')|first }} ({{ products|selectattr('product_id', 'equalto', selected_product)|map(attribute='strength')|first }})</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="thead-dark">
                                                    <tr>
                                                        <th>Batch ID</th>
                                                        <th>Batch Number</th>
                                                        <th>Quantity</th>
                                                        <th>Expiry Date</th>
                                                        <th>Location</th>
                                                        <th>Status</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% if batches %}
                                                        {% for batch in batches %}
                                                        <tr>
                                                            <td>{{ batch.batch_id }}</td>
                                                            <td>{{ batch.batch_number }}</td>
                                                            <td>{{ batch.quantity }}</td>
                                                            <td>{{ batch.expiry_date }}</td>
                                                            <td>{{ batch.location }}</td>
                                                            <td>
                                                                <span class="badge 
                                                                    {% if batch.status == 'active' %}badge-success
                                                                    {% elif batch.status == 'expired' %}badge-danger
                                                                    {% else %}badge-secondary{% endif %}">
                                                                    {{ batch.status }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <a href="{{ url_for('batches', view='details', batch_id=batch.batch_id) }}" class="btn btn-sm btn-info">
                                                                    <i class="fas fa-info-circle"></i> Details
                                                                </a>
                                                                <a href="{{ url_for('batches', view='movements', batch_id=batch.batch_id) }}" class="btn btn-sm btn-secondary">
                                                                    <i class="fas fa-exchange-alt"></i> Movements
                                                                </a>
                                                                <a href="{{ url_for('batches', view='deliveries', batch_id=batch.batch_id) }}" class="btn btn-sm btn-secondary">
                                                                    <i class="fas fa-truck"></i> Deliveries
                                                                </a>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    {% else %}
                                                        <tr>
                                                            <td colspan="7" class="text-center">No batches found for this product</td>
                                                        </tr>
                                                    {% endif %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> Please select a product to view its batches.
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Batch Modal -->
<div class="modal fade" id="addBatchModal" tabindex="-1" role="dialog" aria-labelledby="addBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addBatchModalLabel">Add New Batch</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{{ url_for('batches', action='add') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="product_id">Product</label>
                        <select class="form-control" id="product_id" name="product_id" required>
                            <option value="">-- Select Product --</option>
                            {% for product in products %}
                            <option value="{{ product.product_id }}" {% if selected_product == product.product_id %}selected{% endif %}>{{ product.name }} ({{ product.strength }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="batch_number">Batch Number</label>
                        <input type="text" class="form-control" id="batch_number" name="batch_number" required>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" required>
                    </div>
                    <div class="form-group">
                        <label for="expiry_date">Expiry Date</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location</label>
                        <select class="form-control" id="location" name="location" required>
                            <option value="Karachi">Karachi</option>
                            <option value="Lahore">Lahore</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Add Batch</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
