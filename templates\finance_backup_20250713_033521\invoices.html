{% extends 'base.html' %}

{% block title %}Finance - Invoices{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Invoice Management</h1>
        <a href="{{ url_for('finance') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Finance
        </a>
    </div>

    <!-- Invoice Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Invoices</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ invoices|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₨{{ "{:,.2f}".format(invoices|sum(attribute='order_amount')|default(0)) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Invoice List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Generated By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>{{ invoice.invoice_number }}</td>
                            <td>{{ invoice.order_id }}</td>
                            <td>{{ invoice.customer_name }}</td>
                            <td>{{ invoice.date_generated|format_datetime }}</td>
                            <td>{{ invoice.order_amount|format_currency }}</td>
                            <td>{{ invoice.generated_by or 'System' }}</td>
                            <td>
                                <a href="{{ url_for('finance_invoice_details', invoice_number=invoice.invoice_number) }}"
                                   class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                <a href="{{ url_for('view_invoice', order_id=invoice.order_id) }}"
                                   class="btn btn-sm btn-primary" target="_blank">
                                    <i class="fas fa-file-invoice"></i> Invoice
                                </a>
                                {% if invoice.pdf_path %}
                                <a href="{{ url_for('download_invoice', invoice_id=invoice.invoice_number) }}"
                                   class="btn btn-sm btn-success">
                                    <i class="fas fa-download"></i> PDF
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">No invoices found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 3, "desc" ]], // Sort by date descending
        "pageLength": 25
    });
});
</script>
{% endblock %}
