{% extends 'base.html' %}

{% block title %}Finance - Accounts Receivable{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Accounts Receivable</h1>
        <a href="{{ url_for('finance') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Finance
        </a>
    </div>

    <!-- Receivables Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Outstanding Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ receivables|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Outstanding Amount</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₨{{ "{:,.2f}".format(receivables|sum(attribute='order_amount')|default(0)) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Accounts Receivable Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Outstanding Receivables</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Order Date</th>
                            <th>Amount</th>
                            <th>Payment Method</th>
                            <th>Days Outstanding</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for receivable in receivables %}
                        {% set days_outstanding = 30 %}
                        <tr class="{% if days_outstanding > 30 %}table-warning{% elif days_outstanding > 60 %}table-danger{% endif %}">
                            <td>{{ receivable.order_id }}</td>
                            <td>{{ receivable.customer_name }}</td>
                            <td>{{ receivable.order_date if receivable.order_date else 'N/A' }}</td>
                            <td>₨{{ "{:,.2f}".format(receivable.order_amount) }}</td>
                            <td>
                                <span class="badge badge-{% if receivable.payment_method == 'cheque' %}warning{% else %}info{% endif %}">
                                    {{ receivable.payment_method|title }}
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-warning">
                                    {{ days_outstanding }} days
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-{% if receivable.status == 'Delivered' %}success{% elif receivable.status == 'Dispatched' %}info{% else %}primary{% endif %}">
                                    {{ receivable.status }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('generate_invoice', order_id=receivable.order_id) }}"
                                   class="btn btn-sm btn-primary" target="_blank">
                                    <i class="fas fa-file-invoice"></i> Invoice
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center">No outstanding receivables found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="card shadow">
        <div class="card-body">
            <h6 class="font-weight-bold">Legend:</h6>
            <span class="badge badge-secondary">0-30 days</span>
            <span class="badge badge-warning">31-60 days</span>
            <span class="badge badge-danger">60+ days</span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 5, "desc" ]], // Sort by days outstanding descending
        "pageLength": 25
    });
});
</script>
{% endblock %}
