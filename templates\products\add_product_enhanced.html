{% extends "base.html" %}

{% block title %}Add New Product - Medivent ERP{% endblock %}

{% block extra_css %}
<style>
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px 15px 0 0 !important;
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .required {
        color: #dc3545;
    }
    .upload-area {
        border: 2px dashed #007bff;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background-color: #f8f9ff;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .upload-area:hover {
        background-color: #e3f2fd;
        border-color: #0056b3;
    }
    .price-input {
        position: relative;
    }
    .price-input .currency {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-weight: 600;
    }
    .price-input input {
        padding-left: 35px;
    }
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1"><i class="fas fa-plus-circle text-primary"></i> Add New Product</h2>
                <p class="text-muted">Create a new product entry in the system</p>
            </div>
            <div>
                <a href="{{ url_for('view_all_products') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left"></i> Back to Products
                </a>
                <a href="{{ url_for('products') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog"></i> Product Management
                </a>
            </div>
        </div>

        <!-- Add Product Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Product Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Product Image Upload -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Product Image</label>
                                <div class="upload-area" onclick="document.getElementById('product_image').click()">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <p class="mb-2"><strong>Click to upload</strong></p>
                                    <p class="text-muted small">PNG, JPG up to 5MB</p>
                                    <input type="file" id="product_image" name="product_image" class="d-none" accept="image/*">
                                </div>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Product Name <span class="required">*</span></label>
                                        <input type="text" name="name" class="form-control" 
                                               placeholder="Enter product name" 
                                               value="{{ form_data.name if form_data else '' }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Strength <span class="required">*</span></label>
                                        <input type="text" name="strength" class="form-control" 
                                               placeholder="e.g., 500mg, 10ml, 100IU" 
                                               value="{{ form_data.strength if form_data else '' }}" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Generic Category <span class="required">*</span></label>
                                        <select name="generic_category" class="form-control" required>
                                            <option value="">Select Generic Category</option>
                                            {% for category in generic_categories %}
                                            <option value="{{ category.category }}" 
                                                    {% if form_data and form_data.generic_category == category.category %}selected{% endif %}>
                                                {{ category.category }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Manufacturer <span class="required">*</span></label>
                                        <select name="manufacturer" class="form-control" required>
                                            <option value="">Select Manufacturer</option>
                                            {% for manufacturer in manufacturers %}
                                            <option value="{{ manufacturer.manufacturer }}" 
                                                    {% if form_data and form_data.manufacturer == manufacturer.manufacturer %}selected{% endif %}>
                                                {{ manufacturer.manufacturer }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Division <span class="required">*</span></label>
                                        <select name="division" class="form-control" required>
                                            <option value="">Select Division</option>
                                            {% for division in divisions %}
                                            <option value="{{ division.division }}" 
                                                    {% if form_data and form_data.division == division.division %}selected{% endif %}>
                                                {{ division.division }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-rupee-sign"></i> Pricing Information</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>MRP (Maximum Retail Price) <span class="required">*</span></label>
                                <div class="price-input">
                                    <span class="currency">₹</span>
                                    <input type="number" name="mrp" class="form-control" 
                                           placeholder="0.00" step="0.01" 
                                           value="{{ form_data.mrp if form_data else '' }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>TP Rate (Trade Price) <span class="required">*</span></label>
                                <div class="price-input">
                                    <span class="currency">₹</span>
                                    <input type="number" name="tp_rate" class="form-control" 
                                           placeholder="0.00" step="0.01" 
                                           value="{{ form_data.tp_rate if form_data else '' }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>ASP (Average Selling Price) <span class="required">*</span></label>
                                <div class="price-input">
                                    <span class="currency">₹</span>
                                    <input type="number" name="asp" class="form-control" 
                                           placeholder="0.00" step="0.01" 
                                           value="{{ form_data.asp if form_data else '' }}" required>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Additional Information -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-info"></i> Additional Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Product Description</label>
                                <textarea name="description" class="form-control" rows="4" 
                                          placeholder="Enter product description, usage instructions, etc.">{{ form_data.description if form_data else '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Unit of Measure</label>
                                        <select name="unit_of_measure" class="form-control">
                                            <option value="Tablet" {% if form_data and form_data.unit_of_measure == 'Tablet' %}selected{% endif %}>Tablet</option>
                                            <option value="Capsule" {% if form_data and form_data.unit_of_measure == 'Capsule' %}selected{% endif %}>Capsule</option>
                                            <option value="Syrup" {% if form_data and form_data.unit_of_measure == 'Syrup' %}selected{% endif %}>Syrup</option>
                                            <option value="Injection" {% if form_data and form_data.unit_of_measure == 'Injection' %}selected{% endif %}>Injection</option>
                                            <option value="Cream" {% if form_data and form_data.unit_of_measure == 'Cream' %}selected{% endif %}>Cream</option>
                                            <option value="Drops" {% if form_data and form_data.unit_of_measure == 'Drops' %}selected{% endif %}>Drops</option>
                                            <option value="Piece" {% if form_data and form_data.unit_of_measure == 'Piece' %}selected{% endif %}>Piece</option>
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" class="form-control">
                                    <option value="active" {% if not form_data or form_data.status == 'active' %}selected{% endif %}>Active</option>
                                    <option value="inactive" {% if form_data and form_data.status == 'inactive' %}selected{% endif %}>Inactive</option>
                                    <option value="discontinued" {% if form_data and form_data.status == 'discontinued' %}selected{% endif %}>Discontinued</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <hr class="my-4">
                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('view_all_products') }}" class="btn btn-outline-secondary me-3">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// File upload preview
document.getElementById('product_image').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        const file = e.target.files[0];
        const reader = new FileReader();
        reader.onload = function(e) {
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = `
                <img src="${e.target.result}" style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                <p class="mt-2 mb-0 text-success"><i class="fas fa-check"></i> Image uploaded</p>
            `;
        };
        reader.readAsDataURL(file);
    }
});

// Auto-calculate TP Rate from MRP (MRP * 0.85 = TP Rate)
document.querySelector('input[name="mrp"]').addEventListener('input', function() {
    const mrp = parseFloat(this.value) || 0;
    if (mrp > 0) {
        // Calculate TP Rate: MRP * 0.85 (MRP - MRP * 0.15) (unchangeable until product batch ends)
        const tpRate = mrp * 0.85; // 85% of MRP
        document.querySelector('input[name="tp_rate"]').value = tpRate.toFixed(2);
        document.querySelector('input[name="tp_rate"]').readOnly = true;
        document.querySelector('input[name="tp_rate"]').style.backgroundColor = '#f8f9fa';
        document.querySelector('input[name="tp_rate"]').title = 'Auto-calculated: MRP × 0.85 (unchangeable until batch ends)';

        // Calculate ASP after TP Rate is set
        calculateASP();
    }
});

// Manual TP Rate input (disabled by default)
document.querySelector('input[name="tp_rate"]').addEventListener('input', calculateASP);

function calculateASP() {
    const mrp = parseFloat(document.querySelector('input[name="mrp"]').value) || 0;
    const tpRate = parseFloat(document.querySelector('input[name="tp_rate"]').value) || 0;

    if (mrp > 0 && tpRate > 0) {
        // Calculate ASP as average of MRP and TP Rate
        const asp = (mrp + tpRate) / 2;
        document.querySelector('input[name="asp"]').value = asp.toFixed(2);
    }
}

// Validation
document.querySelector('form').addEventListener('submit', function(e) {
    const mrp = parseFloat(document.querySelector('input[name="mrp"]').value) || 0;
    const tpRate = parseFloat(document.querySelector('input[name="tp_rate"]').value) || 0;
    const asp = parseFloat(document.querySelector('input[name="asp"]').value) || 0;
    
    if (tpRate > mrp) {
        e.preventDefault();
        alert('Trade Price cannot be higher than MRP');
        return false;
    }
    
    if (asp > mrp) {
        e.preventDefault();
        alert('Average Selling Price cannot be higher than MRP');
        return false;
    }
});
</script>
{% endblock %}
