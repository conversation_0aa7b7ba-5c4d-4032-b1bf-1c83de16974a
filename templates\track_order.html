{% extends "base.html" %}

{% block title %}Order Tracker - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-search"></i> Universal Order Tracker
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="orderIdInput" class="form-control"
                                       placeholder="Enter Order ID, Invoice Number (INV12345), or DC Number (DC1234)"
                                       value="{{ order_id }}">
                                <div class="input-group-append">
                                    <button class="btn btn-primary" onclick="trackOrder()">
                                        <i class="fas fa-search"></i> Track
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                <strong>Search Examples:</strong><br>
                                • Order ID: ORD005915, ORD123, 123<br>
                                • Invoice Number: INV00123, INV123, 123<br>
                                • DC Number: DC0045, DC45, 45<br>
                                <em>You can enter with or without prefixes (INV/DC/ORD)</em>
                            </small>
                        </div>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="text-center" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Tracking order...</p>
                    </div>

                    <!-- Error Message -->
                    <div id="errorMessage" class="alert alert-danger" style="display: none;"></div>

                    <!-- Order Details -->
                    <div id="orderDetails" style="display: none;">
                        <!-- Order Summary -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Order Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr><td><strong>Order ID:</strong></td><td id="orderId"></td></tr>
                                            <tr><td><strong>Customer:</strong></td><td id="customerName"></td></tr>
                                            <tr><td><strong>Order Date:</strong></td><td id="orderDate"></td></tr>
                                            <tr><td><strong>Status:</strong></td><td id="orderStatus"></td></tr>
                                            <tr><td><strong>Amount:</strong></td><td id="orderAmount"></td></tr>
                                            <tr><td><strong>Payment Method:</strong></td><td id="paymentMethod"></td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Document Numbers</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr><td><strong>Invoice Number:</strong></td><td id="invoiceNumber"></td></tr>
                                            <tr><td><strong>DC Number:</strong></td><td id="dcNumber"></td></tr>
                                            <tr><td><strong>PO Number:</strong></td><td id="poNumber"></td></tr>
                                            <tr><td><strong>Sales Agent:</strong></td><td id="salesAgent"></td></tr>
                                            <tr><td><strong>Division:</strong></td><td id="division"></td></tr>
                                            <tr><td><strong>Rider:</strong></td><td id="rider"></td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Timeline -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Order Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="orderTimeline"></div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">Order Items</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="orderItemsTable">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Quantity</th>
                                                <th>FOC</th>
                                                <th>Unit Price</th>
                                                <th>Line Total</th>
                                            </tr>
                                        </thead>
                                        <tbody id="orderItemsBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="card" id="paymentInfo" style="display: none;">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">Payment Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="paymentsTable">
                                        <thead>
                                            <tr>
                                                <th>Payment ID</th>
                                                <th>Date</th>
                                                <th>Method</th>
                                                <th>Amount</th>
                                                <th>Reference</th>
                                            </tr>
                                        </thead>
                                        <tbody id="paymentsBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function trackOrder() {
    let orderId = document.getElementById('orderIdInput').value.trim();

    if (!orderId) {
        alert('Please enter an Order ID, Invoice Number, or DC Number');
        return;
    }

    // Auto-format input for better search
    orderId = formatSearchInput(orderId);

    // Show loading
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('orderDetails').style.display = 'none';

    // Fetch order data
    fetch(`/api/track-order/${orderId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingIndicator').style.display = 'none';

            if (data.error) {
                document.getElementById('errorMessage').innerHTML = `
                    <strong>Search Failed:</strong> ${data.error}<br>
                    <small class="text-muted">
                        <i class="fas fa-lightbulb"></i> <strong>Tips:</strong><br>
                        • For Invoice: Try INV00123, INV123, or just 123<br>
                        • For Order: Try ORD005915, ORD123, or just 123<br>
                        • For DC: Try DC0045, DC45, or just 45<br>
                        • Make sure the number exists in the system
                    </small>
                `;
                document.getElementById('errorMessage').style.display = 'block';
                return;
            }

            // Populate order details
            populateOrderDetails(data);
            document.getElementById('orderDetails').style.display = 'block';
        })
        .catch(error => {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('errorMessage').innerHTML = `
                <strong>Connection Error:</strong> ${error.message}<br>
                <small class="text-muted">Please check your internet connection and try again.</small>
            `;
            document.getElementById('errorMessage').style.display = 'block';
        });
}

function formatSearchInput(input) {
    // Remove extra spaces and convert to uppercase
    input = input.trim().toUpperCase();

    // If it's just numbers, try to determine what type it might be
    if (/^\d+$/.test(input)) {
        const num = parseInt(input);
        // If it's a reasonable order number range, format as order
        if (num > 0 && num < 100000) {
            return input; // Keep as number for flexible search
        }
    }

    // If it already has a prefix, keep it
    if (input.startsWith('INV') || input.startsWith('DC') || input.startsWith('ORD')) {
        return input;
    }

    return input;
}

function populateOrderDetails(data) {
    const order = data.order;
    
    // Basic order info
    document.getElementById('orderId').textContent = order.order_id;
    document.getElementById('customerName').textContent = order.customer_name;
    document.getElementById('orderDate').textContent = new Date(order.order_date).toLocaleDateString();
    document.getElementById('orderStatus').innerHTML = `<span class="badge badge-${getStatusColor(order.status)}">${order.status}</span>`;
    document.getElementById('orderAmount').textContent = '₨' + (order.gross_amount || 0).toLocaleString();
    document.getElementById('paymentMethod').textContent = order.payment_method;
    
    // Document numbers
    document.getElementById('invoiceNumber').textContent = order.invoice_number || 'Not Generated';
    document.getElementById('dcNumber').textContent = order.dc_number || 'Not Generated';
    document.getElementById('poNumber').textContent = order.po_number || 'N/A';
    document.getElementById('salesAgent').textContent = order.sales_agent || 'N/A';
    document.getElementById('division').textContent = order.division || 'N/A';
    document.getElementById('rider').textContent = order.rider || 'Not Assigned';
    
    // Timeline
    populateTimeline(data.timeline);
    
    // Order items
    populateOrderItems(data.items);
    
    // Payments
    if (data.payments && data.payments.length > 0) {
        populatePayments(data.payments);
        document.getElementById('paymentInfo').style.display = 'block';
    }
}

function populateTimeline(timeline) {
    const timelineDiv = document.getElementById('orderTimeline');
    timelineDiv.innerHTML = '';
    
    timeline.forEach((stage, index) => {
        const stageDiv = document.createElement('div');
        stageDiv.className = 'timeline-item mb-3';
        stageDiv.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <div class="timeline-badge ${stage.status === 'completed' ? 'bg-success' : 'bg-secondary'} text-white p-2 rounded">
                        <i class="fas fa-${getStageIcon(stage.stage)}"></i> ${stage.stage}
                    </div>
                </div>
                <div class="col-md-6">
                    <p class="mb-1">${stage.description}</p>
                </div>
                <div class="col-md-3">
                    <small class="text-muted">${new Date(stage.date).toLocaleString()}</small>
                </div>
            </div>
        `;
        timelineDiv.appendChild(stageDiv);
    });
}

function populateOrderItems(items) {
    const tbody = document.getElementById('orderItemsBody');
    tbody.innerHTML = '';
    
    items.forEach(item => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${item.product_name}</td>
            <td>${item.quantity}</td>
            <td>${item.foc_quantity || 0}</td>
            <td>₨${(item.unit_price || 0).toLocaleString()}</td>
            <td>₨${(item.line_total || 0).toLocaleString()}</td>
        `;
    });
}

function populatePayments(payments) {
    const tbody = document.getElementById('paymentsBody');
    tbody.innerHTML = '';
    
    payments.forEach(payment => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${payment.payment_id}</td>
            <td>${new Date(payment.payment_date).toLocaleDateString()}</td>
            <td>${payment.payment_method}</td>
            <td>₨${payment.amount.toLocaleString()}</td>
            <td>${payment.reference_number || 'N/A'}</td>
        `;
    });
}

function getStatusColor(status) {
    const colors = {
        'Pending': 'warning',
        'Approved': 'info',
        'Processing': 'primary',
        'Dispatched': 'success',
        'Delivered': 'success',
        'Cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStageIcon(stage) {
    const icons = {
        'Order Placed': 'plus-circle',
        'Order Approved': 'check-circle',
        'DC Generated': 'file-alt',
        'Invoice Generated': 'file-invoice',
        'Order Dispatched': 'truck',
        'Order Delivered': 'check-double'
    };
    return icons[stage] || 'circle';
}

// Auto-track if order ID is provided
document.addEventListener('DOMContentLoaded', function() {
    const orderId = document.getElementById('orderIdInput').value;
    if (orderId) {
        trackOrder();
    }
});
</script>
{% endblock %}
