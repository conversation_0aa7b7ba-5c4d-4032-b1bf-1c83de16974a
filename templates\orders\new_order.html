{% extends "base.html" %}

{% block title %}Place New Order{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle"></i> Place New Order
                    </h4>
                    <small>Create a new order with automatic workflow processing</small>
                </div>
            </div>

            <!-- Order Form -->
            <form method="POST" action="{{ url_for('create_order_with_workflow') }}" id="orderForm" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                
                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_id">Customer ID *</label>
                                    <select name="customer_id" id="customer_id" class="form-control" required>
                                        <option value="">Select Customer</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_id }}" data-name="{{ customer.name }}">
                                            {{ customer.customer_id }} - {{ customer.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name</label>
                                    <input type="text" name="customer_name" id="customer_name" class="form-control" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="po_number">PO Number *</label>
                                    <input type="text" name="po_number" id="po_number" class="form-control" required placeholder="Purchase Order Number">
                                    <small class="form-text text-muted">Enter doctor/institute/customer PO number</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="priority">Priority</label>
                                    <select name="priority" id="priority" class="form-control">
                                        <option value="normal">Normal</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="delivery_date">Delivery Date</label>
                                    <input type="date" name="delivery_date" id="delivery_date" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="special_instructions">Special Instructions</label>
                                    <textarea name="special_instructions" id="special_instructions" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-paperclip"></i> Order Documents</h5>
                        <small>Upload prescriptions, purchase orders, or other relevant documents</small>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="order_files">Upload Files</label>
                            <input type="file" class="form-control" id="order_files" name="order_files" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt">
                            <small class="form-text text-muted">You can upload multiple files. Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG, TXT</small>
                        </div>

                        <!-- File Preview Area -->
                        <div id="order-file-preview-area" class="mt-3" style="display: none;">
                            <h6>Selected Files:</h6>
                            <div id="order-file-list" class="row"></div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> Order Items</h5>
                            </div>
                            <div class="col-auto">
                                <button type="button" class="btn btn-light btn-sm" onclick="addOrderItem()">
                                    <i class="fas fa-plus"></i> Add Item
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="orderItemsTable">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="30%">Product</th>
                                        <th width="15%">Quantity</th>
                                        <th width="15%">Unit Price</th>
                                        <th width="15%">Total</th>
                                        <th width="15%">Available Stock</th>
                                        <th width="10%">Action</th>
                                    </tr>
                                </thead>
                                <tbody id="orderItemsBody">
                                    <tr class="order-item-row">
                                        <td>
                                            <select name="product_id[]" class="form-control product-select" required onchange="updateProductInfo(this)">
                                                <option value="">Select Product</option>
                                                {% for product in products %}
                                                <option value="{{ product.product_id }}"
                                                        data-name="{{ product.name }}"
                                                        data-price="{{ product.unit_price }}"
                                                        data-stock="{{ product.available_stock or 0 }}">
                                                    {{ product.product_id }} - {{ product.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <input type="hidden" name="product_name[]" class="product-name">
                                        </td>
                                        <td>
                                            <input type="number" name="quantity[]" class="form-control quantity-input" 
                                                   min="1" step="1" required onchange="calculateRowTotal(this)">
                                        </td>
                                        <td>
                                            <input type="number" name="unit_price[]" class="form-control unit-price" 
                                                   min="0" step="0.01" required onchange="calculateRowTotal(this)">
                                        </td>
                                        <td>
                                            <input type="number" class="form-control row-total" readonly>
                                        </td>
                                        <td>
                                            <span class="available-stock badge badge-info">0</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="removeOrderItem(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator"></i> Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="discount_amount">Discount Amount</label>
                                            <input type="number" name="discount_amount" id="discount_amount" 
                                                   class="form-control" min="0" step="0.01" value="0" onchange="calculateTotal()">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="tax_amount">Tax Amount</label>
                                            <input type="number" name="tax_amount" id="tax_amount" 
                                                   class="form-control" min="0" step="0.01" value="0" onchange="calculateTotal()">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="shipping_cost">Shipping Cost</label>
                                            <input type="number" name="shipping_cost" id="shipping_cost" 
                                                   class="form-control" min="0" step="0.01" value="0" onchange="calculateTotal()">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="order-totals">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Subtotal:</strong></td>
                                            <td class="text-right">₹<span id="subtotal">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Discount:</strong></td>
                                            <td class="text-right">₹<span id="discount_display">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Tax:</strong></td>
                                            <td class="text-right">₹<span id="tax_display">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Shipping:</strong></td>
                                            <td class="text-right">₹<span id="shipping_display">0.00</span></td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td><strong>Total:</strong></td>
                                            <td class="text-right"><strong>₹<span id="total_amount">0.00</span></strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body text-center">
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('orders') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="button" class="btn btn-info" onclick="validateOrder()">
                                <i class="fas fa-check"></i> Validate Order
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-paper-plane"></i> Place Order with Workflow
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Customer selection handler
document.getElementById('customer_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const customerName = selectedOption.getAttribute('data-name') || '';
    document.getElementById('customer_name').value = customerName;
});

// Product selection handler
function updateProductInfo(selectElement) {
    const row = selectElement.closest('tr');
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    
    if (selectedOption.value) {
        const productName = selectedOption.getAttribute('data-name') || '';
        const price = selectedOption.getAttribute('data-price') || '0';
        const stock = selectedOption.getAttribute('data-stock') || '0';
        
        row.querySelector('.product-name').value = productName;
        row.querySelector('.unit-price').value = price;
        row.querySelector('.available-stock').textContent = stock;
        
        // Update badge color based on stock
        const stockBadge = row.querySelector('.available-stock');
        stockBadge.className = 'available-stock badge ' + 
            (parseInt(stock) > 10 ? 'badge-success' : 
             parseInt(stock) > 0 ? 'badge-warning' : 'badge-danger');
    }
    
    calculateRowTotal(selectElement);
}

// Calculate row total
function calculateRowTotal(element) {
    const row = element.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
    const total = quantity * unitPrice;
    
    row.querySelector('.row-total').value = total.toFixed(2);
    calculateTotal();
}

// Calculate order total
function calculateTotal() {
    let subtotal = 0;
    document.querySelectorAll('.row-total').forEach(function(input) {
        subtotal += parseFloat(input.value) || 0;
    });
    
    const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
    const shipping = parseFloat(document.getElementById('shipping_cost').value) || 0;
    
    const total = subtotal - discount + tax + shipping;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('discount_display').textContent = discount.toFixed(2);
    document.getElementById('tax_display').textContent = tax.toFixed(2);
    document.getElementById('shipping_display').textContent = shipping.toFixed(2);
    document.getElementById('total_amount').textContent = total.toFixed(2);
}

// Add new order item row
function addOrderItem() {
    const tbody = document.getElementById('orderItemsBody');
    const firstRow = tbody.querySelector('.order-item-row');
    const newRow = firstRow.cloneNode(true);
    
    // Clear values in new row
    newRow.querySelectorAll('input, select').forEach(function(input) {
        if (input.type === 'hidden') return;
        input.value = '';
    });
    newRow.querySelector('.available-stock').textContent = '0';
    newRow.querySelector('.available-stock').className = 'available-stock badge badge-info';
    
    tbody.appendChild(newRow);
}

// Remove order item row
function removeOrderItem(button) {
    const tbody = document.getElementById('orderItemsBody');
    if (tbody.children.length > 1) {
        button.closest('tr').remove();
        calculateTotal();
    } else {
        alert('At least one item is required');
    }
}

// Validate order before submission
function validateOrder() {
    const customerId = document.getElementById('customer_id').value;
    if (!customerId) {
        alert('Please select a customer');
        return false;
    }
    
    const productSelects = document.querySelectorAll('.product-select');
    let hasValidItems = false;
    
    productSelects.forEach(function(select) {
        if (select.value) {
            const row = select.closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
            
            if (quantity > 0 && unitPrice > 0) {
                hasValidItems = true;
            }
        }
    });
    
    if (!hasValidItems) {
        alert('Please add at least one valid item with quantity and price');
        return false;
    }
    
    alert('Order validation successful! You can now place the order.');
    return true;
}

// Form submission handler
document.getElementById('orderForm').addEventListener('submit', function(e) {
    if (!validateOrder()) {
        e.preventDefault();
        return false;
    }
    
    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    submitBtn.disabled = true;
});

// File upload preview functionality for orders
document.addEventListener('DOMContentLoaded', function() {
    // Set default delivery date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    document.getElementById('delivery_date').value = tomorrow.toISOString().split('T')[0];

    // File upload handling
    const orderFileInput = document.getElementById('order_files');
    const orderPreviewArea = document.getElementById('order-file-preview-area');
    const orderFileList = document.getElementById('order-file-list');

    orderFileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);

        if (files.length > 0) {
            orderPreviewArea.style.display = 'block';
            orderFileList.innerHTML = '';

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'col-md-4 mb-3';

                const fileCard = document.createElement('div');
                fileCard.className = 'card border-warning';

                const fileIcon = getOrderFileIcon(file.type);
                const fileSize = formatOrderFileSize(file.size);

                fileCard.innerHTML = `
                    <div class="card-body text-center">
                        <i class="${fileIcon} fa-2x mb-2 text-warning"></i>
                        <h6 class="card-title">${file.name}</h6>
                        <p class="card-text small text-muted">${fileSize}</p>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOrderFile(${index})">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                        ${file.type.startsWith('image/') ? `<button type="button" class="btn btn-sm btn-outline-warning ml-1" onclick="previewOrderImage(${index})"><i class="fas fa-eye"></i> Preview</button>` : ''}
                    </div>
                `;

                fileItem.appendChild(fileCard);
                orderFileList.appendChild(fileItem);
            });
        } else {
            orderPreviewArea.style.display = 'none';
        }
    });
});

function getOrderFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) return 'fas fa-image';
    if (mimeType.includes('pdf')) return 'fas fa-file-pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
    if (mimeType.includes('text')) return 'fas fa-file-alt';
    return 'fas fa-file';
}

function formatOrderFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeOrderFile(index) {
    const fileInput = document.getElementById('order_files');
    const dt = new DataTransfer();
    const files = Array.from(fileInput.files);

    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    fileInput.files = dt.files;
    fileInput.dispatchEvent(new Event('change'));
}

function previewOrderImage(index) {
    const fileInput = document.getElementById('order_files');
    const file = fileInput.files[index];

    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Create modal for image preview
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${file.name}</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="${e.target.result}" class="img-fluid" alt="${file.name}">
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            $(modal).modal('show');

            // Remove modal after hiding
            $(modal).on('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        };
        reader.readAsDataURL(file);
    }
}
</script>
{% endblock %}