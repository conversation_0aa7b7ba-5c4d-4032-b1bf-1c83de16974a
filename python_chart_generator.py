"""
Python Chart Generator Module
Advanced chart generation using matplotlib and plotly
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import io
import base64
from datetime import datetime, timedelta
import numpy as np

class PythonChartGenerator:
    """Python-based chart generator for ERP system"""
    
    def __init__(self):
        plt.style.use('default')
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    
    def generate_sales_trend_chart(self, data, title="Sales Trend"):
        """Generate sales trend line chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            dates = [datetime.strptime(d['date'], '%Y-%m-%d') for d in data]
            sales = [d['total_sales'] for d in data]
            
            ax.plot(dates, sales, marker='o', linewidth=2, markersize=6, color=self.colors[0])
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Sales Amount', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # Format x-axis
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=7))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Sales trend chart error: {e}")
            return None
    
    def generate_inventory_bar_chart(self, data, title="Inventory Levels"):
        """Generate inventory bar chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            products = [d['product_name'][:20] for d in data]  # Truncate long names
            quantities = [d['total_stock'] for d in data]
            
            bars = ax.bar(products, quantities, color=self.colors[1])
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('Products', fontsize=12)
            ax.set_ylabel('Stock Quantity', fontsize=12)
            
            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{int(height)}', ha='center', va='bottom')
            
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Inventory bar chart error: {e}")
            return None
    
    def generate_pie_chart(self, data, title="Distribution", value_key='value', label_key='label'):
        """Generate pie chart"""
        try:
            fig, ax = plt.subplots(figsize=(10, 8))
            
            labels = [d[label_key] for d in data]
            values = [d[value_key] for d in data]
            
            wedges, texts, autotexts = ax.pie(values, labels=labels, autopct='%1.1f%%',
                                            colors=self.colors, startangle=90)
            ax.set_title(title, fontsize=16, fontweight='bold')
            
            # Improve text readability
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Pie chart error: {e}")
            return None
    
    def generate_multi_line_chart(self, data, title="Multi-Line Chart"):
        """Generate multi-line chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # Assuming data has multiple series
            for i, series in enumerate(data):
                x_values = series.get('x', [])
                y_values = series.get('y', [])
                label = series.get('label', f'Series {i+1}')
                
                ax.plot(x_values, y_values, marker='o', linewidth=2, 
                       label=label, color=self.colors[i % len(self.colors)])
            
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('X Axis', fontsize=12)
            ax.set_ylabel('Y Axis', fontsize=12)
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Multi-line chart error: {e}")
            return None
    
    def generate_stacked_bar_chart(self, data, title="Stacked Bar Chart"):
        """Generate stacked bar chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            categories = data.get('categories', [])
            series_data = data.get('series', [])
            
            bottom = np.zeros(len(categories))
            
            for i, series in enumerate(series_data):
                values = series.get('values', [])
                label = series.get('label', f'Series {i+1}')
                
                ax.bar(categories, values, bottom=bottom, label=label,
                      color=self.colors[i % len(self.colors)])
                bottom += np.array(values)
            
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('Categories', fontsize=12)
            ax.set_ylabel('Values', fontsize=12)
            ax.legend()
            
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Stacked bar chart error: {e}")
            return None
    
    def _convert_to_base64(self, fig):
        """Convert matplotlib figure to base64 string"""
        try:
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight', dpi=150)
            img.seek(0)
            plot_url = base64.b64encode(img.getvalue()).decode()
            plt.close(fig)
            return plot_url
        except Exception as e:
            print(f"Base64 conversion error: {e}")
            plt.close(fig)
            return None

# Create global instance
chart_generator = PythonChartGenerator()

# Convenience functions
def generate_sales_chart(data, title="Sales Chart"):
    return chart_generator.generate_sales_trend_chart(data, title)

def generate_inventory_chart(data, title="Inventory Chart"):
    return chart_generator.generate_inventory_bar_chart(data, title)

def generate_pie_chart(data, title="Pie Chart"):
    return chart_generator.generate_pie_chart(data, title)

def generate_multi_line_chart(data, title="Multi-Line Chart"):
    return chart_generator.generate_multi_line_chart(data, title)
