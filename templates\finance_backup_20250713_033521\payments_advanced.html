{% extends "base.html" %}

{% block title %}Advanced Payment Management - Finance{% endblock %}

{% block head %}
<style>
.payment-card {
    border-left: 4px solid #28a745;
    transition: all 0.3s;
}
.payment-card:hover {
    box-shadow: 0 4px 8px rgba(40,167,69,0.3);
}
.unallocated-payment {
    border-left-color: #ffc107;
}
.partial-payment {
    border-left-color: #17a2b8;
}
.complete-payment {
    border-left-color: #28a745;
}
.payment-method-badge {
    font-size: 0.8em;
    padding: 0.25rem 0.5rem;
}
.allocation-progress {
    height: 8px;
    border-radius: 4px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">💰 Advanced Payment Management</h2>
                    <p class="mb-0">Professional payment processing and allocation system</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ payments|length }}</h4>
                    <p class="mb-0">Total Payments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h4>{{ unallocated_payments|length }}</h4>
                    <p class="mb-0">Unallocated</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ payments|selectattr('knock_off_status', 'equalto', 'partial')|list|length }}</h4>
                    <p class="mb-0">Partial Allocated</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ payments|selectattr('knock_off_status', 'equalto', 'complete')|list|length }}</h4>
                    <p class="mb-0">Fully Allocated</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">⚡ Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('payment_allocation') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-exchange-alt"></i> Payment Allocation
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success btn-block" onclick="showAddPaymentModal()">
                                <i class="fas fa-plus"></i> Add New Payment
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('finance_reports') }}" class="btn btn-info btn-block">
                                <i class="fas fa-chart-bar"></i> Payment Reports
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-warning btn-block" onclick="autoAllocatePayments()">
                                <i class="fas fa-magic"></i> Auto Allocate
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Unallocated Payments Alert -->
    {% if unallocated_payments %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> Unallocated Payments Require Attention</h5>
                <p class="mb-2">You have {{ unallocated_payments|length }} payments with unallocated amounts that need to be knocked off against invoices.</p>
                <a href="{{ url_for('payment_allocation') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-exchange-alt"></i> Allocate Now
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Payments List -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">💳 Payment Records</h5>
                    <div class="float-right">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="filterPayments('all')">All</button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="filterPayments('unallocated')">Unallocated</button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="filterPayments('partial')">Partial</button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="filterPayments('complete')">Complete</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="paymentsTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Payment ID</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Method</th>
                                    <th>Total Amount</th>
                                    <th>Allocated</th>
                                    <th>Unallocated</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr class="payment-row" data-status="{{ payment.knock_off_status }}">
                                    <td>
                                        <strong>{{ payment.payment_id }}</strong>
                                        {% if payment.reference_number %}
                                        <br><small class="text-muted">Ref: {{ payment.reference_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ payment.payment_date|format_date }}</td>
                                    <td>
                                        <a href="{{ url_for('customer_ledger_advanced', customer_id=payment.customer_id) }}">
                                            {{ payment.customer_name }}
                                        </a>
                                        <br><small class="text-muted">{{ payment.customer_id }}</small>
                                    </td>
                                    <td>
                                        <span class="badge payment-method-badge 
                                            {% if payment.payment_method == 'cash' %}badge-success
                                            {% elif payment.payment_method == 'cheque' %}badge-primary
                                            {% elif payment.payment_method == 'bank_transfer' %}badge-info
                                            {% else %}badge-secondary{% endif %}">
                                            {{ payment.payment_method|title }}
                                        </span>
                                        {% if payment.cheque_number %}
                                        <br><small>Cheque: {{ payment.cheque_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ "₹{:,.2f}".format(payment.total_amount) }}</strong>
                                        {% if payment.currency_code != 'PKR' %}
                                        <br><small>{{ payment.currency_code }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-success">{{ "₹{:,.2f}".format(payment.allocated_amount) }}</span>
                                        {% if payment.allocation_count > 0 %}
                                        <br><small class="text-muted">{{ payment.allocation_count }} allocations</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.unallocated_amount > 0 %}
                                        <span class="text-warning">{{ "₹{:,.2f}".format(payment.unallocated_amount) }}</span>
                                        {% else %}
                                        <span class="text-muted">₹0.00</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if payment.knock_off_status == 'complete' %}badge-success
                                            {% elif payment.knock_off_status == 'partial' %}badge-warning
                                            {% else %}badge-danger{% endif %}">
                                            {{ payment.knock_off_status|title }}
                                        </span>
                                        <br><small class="text-muted">{{ payment.payment_status|title }}</small>
                                    </td>
                                    <td>
                                        {% set allocation_percentage = (payment.allocated_amount / payment.total_amount * 100) if payment.total_amount > 0 else 0 %}
                                        <div class="progress allocation-progress">
                                            <div class="progress-bar 
                                                {% if allocation_percentage == 100 %}bg-success
                                                {% elif allocation_percentage > 0 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                style="width: {{ allocation_percentage }}%">
                                            </div>
                                        </div>
                                        <small>{{ "%.1f"|format(allocation_percentage) }}%</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if payment.unallocated_amount > 0 %}
                                            <a href="{{ url_for('payment_allocation') }}?payment_id={{ payment.payment_id }}" 
                                               class="btn btn-sm btn-warning" title="Allocate Payment">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="viewPaymentDetails('{{ payment.payment_id }}')" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-secondary" 
                                                    onclick="printPaymentReceipt('{{ payment.payment_id }}')" title="Print Receipt">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h4 class="modal-title">💰 Add New Payment</h4>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addPaymentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Customer</label>
                                <select class="form-control" name="customer_id" required>
                                    <option value="">Select Customer</option>
                                    <!-- Customer options will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Payment Date</label>
                                <input type="date" class="form-control" name="payment_date" 
                                       value="{{ now.strftime('%Y-%m-%d') }}" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Payment Method</label>
                                <select class="form-control" name="payment_method" required>
                                    <option value="cash">Cash</option>
                                    <option value="cheque">Cheque</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="online">Online Payment</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Amount</label>
                                <input type="number" step="0.01" class="form-control" name="total_amount" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Reference Number</label>
                                <input type="text" class="form-control" name="reference_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Cheque Number (if applicable)</label>
                                <input type="text" class="form-control" name="cheque_number">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="savePayment()">
                    <i class="fas fa-save"></i> Save Payment
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function filterPayments(status) {
    const rows = document.querySelectorAll('.payment-row');
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function showAddPaymentModal() {
    $('#addPaymentModal').modal('show');
    // Load customers dynamically
    loadCustomers();
}

function loadCustomers() {
    // Implementation for loading customers
    console.log('Loading customers...');
}

function savePayment() {
    // Implementation for saving payment
    console.log('Saving payment...');
}

function viewPaymentDetails(paymentId) {
    // Implementation for viewing payment details
    console.log('Viewing payment:', paymentId);
}

function printPaymentReceipt(paymentId) {
    // Implementation for printing receipt
    window.open(`/finance/payment_receipt/${paymentId}`, '_blank');
}

function autoAllocatePayments() {
    if (confirm('Auto-allocate unallocated payments to oldest outstanding invoices?')) {
        // Implementation for auto allocation
        console.log('Auto-allocating payments...');
    }
}
</script>
{% endblock %}
