{% extends 'base.html' %}

{% block title %}Select Customer for Ledger{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Select Customer for Ledger</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <input type="text" id="customerSearch" class="form-control" placeholder="Search by customer name or code...">
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="customerTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Customer Code</th>
                                    <th>Customer Name</th>
                                    <th>Address</th>
                                    <th>Phone</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if customers %}
                                    {% for customer in customers %}
                                    <tr class="customer-row">
                                        <td>{{ customer.code }}</td>
                                        <td>{{ customer.name }}</td>
                                        <td>{{ customer.address }}</td>
                                        <td>{{ customer.phone }}</td>
                                        <td>
                                            <a href="{{ url_for('finance') }}?view=ledger&customer_id={{ customer.customer_id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-book"></i> View Ledger
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">No customers found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Customer search functionality
        $("#customerSearch").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $(".customer-row").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
    });
</script>
{% endblock %}
