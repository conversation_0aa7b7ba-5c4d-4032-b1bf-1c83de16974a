{% extends 'base.html' %}

{% block title %}Delivery Challan - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Delivery Challan #{{ challan.dc_number if challan else 'N/A' }}</h4>
                    <div>
                        <button class="btn btn-light" id="printChallanBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        {% if challan and challan.pdf_path %}
                        <a href="{{ url_for('display_challan', order_id=order.order_id) }}" class="btn btn-success ml-2" target="_blank">
                            <i class="fas fa-file-pdf"></i> View PDF
                        </a>
                        {% endif %}
                        <a href="{{ url_for('workflow') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="challan-container">
                        <!-- Professional Challan Header -->
                        <div class="challan-header">
                            <div class="row">
                                <div class="col-md-2">
                                    <!-- Logo placeholder -->
                                    <div class="logo-placeholder">
                                        <div class="logo-circle">
                                            <span class="logo-text">HP</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h2 class="company-name">Himmel Pharmaceuticals (Pvt) Ltd</h2>
                                    <div class="company-details">
                                        <p><strong>Second Floor & Judicial Colony, Phase 1 (Ext.) Shahrah Nazaria e Pakistan, Lahore</strong></p>
                                        <p><strong>Phone:</strong> 042-99-0298068 • <strong>+92-300-8472 Email:</strong> <EMAIL> <strong>URL:</strong> www.himmelpharma.pk</p>
                                        <p><strong>GST #:</strong> 03-355-0045-0187-AD • <strong>NTN #:</strong> 7-716-7453 <strong>STRN #:</strong> 32-77-0781-21-888</p>
                                    </div>
                                </div>
                                <div class="col-md-4 text-right">
                                    <div class="challan-title-box">
                                        <h1 class="challan-title">Delivery Challan</h1>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer and Challan Info -->
                        <div class="customer-challan-info">
                            <table class="info-table">
                                <tr>
                                    <td class="label-cell"><strong>Customer Name</strong></td>
                                    <td class="value-cell">{{ order.customer_name }}</td>
                                    <td class="label-cell"><strong>DC No</strong></td>
                                    <td class="value-cell">{{ challan.dc_number if challan else ('DC' + order.order_id|string) }}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell"><strong>Customer Code</strong></td>
                                    <td class="value-cell">{{ order.customer_code or 'C' + order.order_id|string }}</td>
                                    <td class="label-cell"><strong>Posting Date</strong></td>
                                    <td class="value-cell">{{ challan.date_generated if challan else now.strftime('%d-%b-%Y %I:%M%p') }}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell"><strong>Address</strong></td>
                                    <td class="value-cell">{{ order.customer_address or 'Karachi, Pakistan' }}</td>
                                    <td class="label-cell"><strong>PO No.</strong></td>
                                    <td class="value-cell">{{ order.po_number or order.order_id }}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell"></td>
                                    <td class="value-cell"></td>
                                    <td class="label-cell"><strong>PO Date</strong></td>
                                    <td class="value-cell">{{ order.order_date }}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Professional Product Table -->
                        <div class="products-section">
                            <table class="products-table">
                                <thead>
                                    <tr class="table-header">
                                        <th class="sr-col">Sr#</th>
                                        <th class="product-col">Product Name</th>
                                        <th class="generic-col">Generic</th>
                                        <th class="company-col">Company</th>
                                        <th class="batch-col">Batch No</th>
                                        <th class="mfg-col">Mfg Date</th>
                                        <th class="exp-col">Exp Date</th>
                                        <th class="qty-col">Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in order_items %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td>{{ item.product_name }}</td>
                                        <td>{{ item.generic_name or item.product_name.split()[0] }}</td>
                                        <td>{{ item.company or 'ONKO (LAC SAN VE TIC A.S.' }}</td>
                                        <td class="text-center">{{ item.batch_number or ('B' + now.strftime('%m%y') + loop.index|string) }}</td>
                                        <td class="text-center">{{ item.mfg_date or '07-2024' }}</td>
                                        <td class="text-center">{{ item.exp_date or '07-2026' }}</td>
                                        <td class="text-center">{{ item.quantity }}</td>
                                    </tr>
                                    {% endfor %}
                                    <tr class="total-items-row">
                                        <td colspan="7" class="text-right"><strong>Total Items</strong></td>
                                        <td class="text-center"><strong>{{ order_items|sum(attribute='quantity') }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Signature Section -->
                        <div class="signature-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="signature-box">
                                        <p><strong>Packed By :</strong> ___________________</p>
                                    </div>
                                </div>
                                <div class="col-md-6 text-right">
                                    <div class="signature-box">
                                        <p><strong>Checked By :</strong> ___________________</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Footer Information -->
                        <div class="footer-section">
                            <div class="print-info">
                                <p><strong>Printed On :</strong> {{ now.strftime('%d-%m-%Y / %H:%M:%S') }}</p>
                                <p><strong>Generated by Medivent ERP System</strong></p>
                                <p class="page-info"><strong>Page 1 of 1</strong></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* Professional Delivery Challan Styling */
    .challan-container {
        font-family: Arial, sans-serif;
        font-size: 12px;
        line-height: 1.4;
        max-width: 100%;
        margin: 0 auto;
        background: white;
    }

    /* Header Styling */
    .challan-header {
        border: 2px solid #000;
        padding: 10px;
        margin-bottom: 0;
    }

    .logo-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80px;
    }

    .logo-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #ff6b35, #f7931e);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 18px;
    }

    .company-name {
        font-size: 18px;
        font-weight: bold;
        color: #000;
        margin-bottom: 5px;
    }

    .company-details {
        font-size: 10px;
        line-height: 1.3;
    }

    .challan-title-box {
        border: 2px solid #000;
        padding: 10px;
        background: #f8f9fa;
    }

    .challan-title {
        font-size: 16px;
        font-weight: bold;
        margin: 0;
        color: #000;
    }

    /* Customer Info Table */
    .customer-challan-info {
        margin: 0;
        border-left: 2px solid #000;
        border-right: 2px solid #000;
        border-bottom: 2px solid #000;
    }

    .info-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .info-table td {
        border: 1px solid #000;
        padding: 5px 8px;
        font-size: 11px;
    }

    .label-cell {
        background: #f8f9fa;
        font-weight: bold;
        width: 15%;
    }

    .value-cell {
        width: 35%;
    }

    /* Products Table */
    .products-section {
        margin: 0;
        border: 2px solid #000;
        border-top: none;
    }

    .products-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .products-table th,
    .products-table td {
        border: 1px solid #000;
        padding: 4px 6px;
        font-size: 10px;
        text-align: center;
    }

    .table-header {
        background: #f8f9fa;
        font-weight: bold;
    }

    .sr-col { width: 8%; }
    .product-col { width: 25%; text-align: left !important; }
    .generic-col { width: 15%; text-align: left !important; }
    .company-col { width: 20%; text-align: left !important; }
    .batch-col { width: 12%; }
    .mfg-col { width: 10%; }
    .exp-col { width: 10%; }
    .qty-col { width: 8%; }

    .total-items-row {
        background: #f8f9fa;
        font-weight: bold;
    }

    /* Signature Section */
    .signature-section {
        margin-top: 30px;
        border: 2px solid #000;
        border-top: none;
        padding: 20px;
    }

    .signature-box {
        text-align: center;
        padding: 10px;
    }

    /* Footer Section */
    .footer-section {
        margin-top: 20px;
        border: 2px solid #000;
        border-top: none;
        padding: 10px;
        font-size: 9px;
    }

    .print-info {
        text-align: center;
        border-top: 1px solid #000;
        padding-top: 5px;
        margin-top: 10px;
    }

    .page-info {
        float: right;
        margin-top: -20px;
    }

    /* Print Styles */
    @media print {
        body * {
            visibility: hidden;
        }
        .challan-container, .challan-container * {
            visibility: visible;
        }
        .challan-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 0;
        }
        .card-header, .btn, .navbar, .sidebar, .footer {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-body {
            padding: 0 !important;
        }

        /* Ensure borders print correctly */
        .challan-header,
        .customer-challan-info,
        .products-section,
        .signature-section,
        .footer-section {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .challan-container {
            font-size: 10px;
        }

        .products-table th,
        .products-table td {
            font-size: 8px;
            padding: 2px 3px;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printChallanBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';

                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .challan-container, .challan-container * {
                            visibility: visible;
                        }
                        .challan-container {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                            page-break-inside: avoid;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        .challan-header {
                            page-break-inside: avoid;
                        }
                        .products-table {
                            page-break-inside: auto;
                        }
                        .products-table tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                        .footer-section {
                            page-break-inside: avoid;
                        }
                    }
                `;

                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);

                // Print after a short delay
                setTimeout(function() {
                    window.print();

                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
