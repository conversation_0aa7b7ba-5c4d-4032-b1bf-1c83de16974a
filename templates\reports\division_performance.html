{% extends 'base.html' %}

{% block title %}Division Performance - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Division Performance Analysis</h4>
                    <div>
                        <button class="btn btn-light" id="printDivisionPerformanceBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Date Range Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('division_performance_chart') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Period</span>
                                    </div>
                                    <select class="form-control" name="date_range" onchange="this.form.submit()">
                                        <option value="this_month" {% if date_range == 'this_month' %}selected{% endif %}>This Month</option>
                                        <option value="last_month" {% if date_range == 'last_month' %}selected{% endif %}>Last Month</option>
                                        <option value="this_quarter" {% if date_range == 'this_quarter' %}selected{% endif %}>This Quarter</option>
                                        <option value="this_year" {% if date_range == 'this_year' %}selected{% endif %}>This Year</option>
                                        <option value="last_year" {% if date_range == 'last_year' %}selected{% endif %}>Last Year</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <small class="text-muted">Period: {{ start_date }} to {{ end_date }}</small>
                        </div>
                    </div>
                    
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h2>{{ division_data|length }}</h2>
                                    <p class="mb-0">Active Divisions</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h2>{{ division_data|sum(attribute='total_sales')|default(0)|round|int }}</h2>
                                    <p class="mb-0">Total Sales (₨)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h2>{{ division_data|sum(attribute='order_count')|default(0) }}</h2>
                                    <p class="mb-0">Total Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h2>{{ division_data|sum(attribute='customer_count')|default(0) }}</h2>
                                    <p class="mb-0">Total Customers</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Sales by Division</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="divisionSalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Monthly Trends</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlyTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Division Performance Table -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Division Performance Details</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered" id="divisionTable">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Division</th>
                                            <th>Total Sales</th>
                                            <th>Orders</th>
                                            <th>Customers</th>
                                            <th>Products</th>
                                            <th>Avg Order Value</th>
                                            <th>Performance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if division_data %}
                                            {% for division in division_data %}
                                            <tr>
                                                <td><strong>{{ division.division_name }}</strong></td>
                                                <td>₨{{ "{:,.2f}".format(division.total_sales|default(0)) }}</td>
                                                <td>{{ division.order_count|default(0) }}</td>
                                                <td>{{ division.customer_count|default(0) }}</td>
                                                <td>{{ division.product_count|default(0) }}</td>
                                                <td>₨{{ "{:,.2f}".format((division.total_sales|default(0) / division.order_count|default(1)) if division.order_count else 0) }}</td>
                                                <td>
                                                    {% set performance = (division.total_sales|default(0) / (division_data|sum(attribute='total_sales')|default(1))) * 100 %}
                                                    <div class="progress">
                                                        <div class="progress-bar 
                                                            {% if performance > 30 %}bg-success
                                                            {% elif performance > 15 %}bg-warning
                                                            {% else %}bg-danger{% endif %}" 
                                                            style="width: {{ performance }}%">
                                                            {{ "%.1f"|format(performance) }}%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center">No division data found for the selected period</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#divisionTable').DataTable({
        "order": [[ 1, "desc" ]], // Sort by total sales descending
        "pageLength": 10,
        "responsive": true
    });

    // Division Sales Pie Chart
    var ctx1 = document.getElementById('divisionSalesChart').getContext('2d');
    var divisionSalesChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: [{% for division in division_data %}'{{ division.division_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for division in division_data %}{{ division.total_sales|default(0) }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ₨' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Monthly Trend Line Chart
    var ctx2 = document.getElementById('monthlyTrendChart').getContext('2d');
    var monthlyTrendChart = new Chart(ctx2, {
        type: 'line',
        data: {{ chart_data|tojson }},
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₨' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ₨' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>

<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printDivisionPerformanceBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .container-fluid, .container-fluid * {
                            visibility: visible;
                        }
                        .container-fluid {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        table {
                            page-break-inside: auto;
                        }
                        tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                    }
                `;
                
                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
