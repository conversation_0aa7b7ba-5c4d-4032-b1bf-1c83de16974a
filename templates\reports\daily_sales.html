{% extends 'base.html' %}

{% block title %}Daily Sales Report{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Daily Sales Report</h3>
                    <div class="card-tools">
                        <form class="form-inline" method="get">
                            <div class="input-group mr-2">
                                <input type="date" name="date" class="form-control" value="{{ date }}">
                            </div>
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-shopping-cart"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Orders</span>
                                    <span class="info-box-number">{{ total_orders }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-money-bill-wave"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Sales</span>
                                    <span class="info-box-number">{{ total_sales|round(2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h4>Orders</h4>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Sales Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in sales_data %}
                                <tr>
                                    <td>{{ order.order_time }}</td>
                                    <td>{{ order.order_id }}</td>
                                    <td>{{ order.customer_name }}</td>
                                    <td>{{ order.total_amount|round(2) }}</td>
                                    <td>{{ order.status }}</td>
                                    <td>{{ order.sales_agent }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">No orders found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
