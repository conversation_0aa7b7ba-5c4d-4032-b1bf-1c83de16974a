{% extends 'base.html' %}

{% block title %}Finance Reports{% endblock %}

{% block content %}
<div class="mb-3">
    <a href="{{ url_for('finance') }}?view=overview" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Finance
    </a>
</div>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Finance Reports</h5>
                        <button type="button" class="btn btn-light btn-sm" onclick="goBack()">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="division-tab" data-toggle="tab" href="#division" role="tab" aria-controls="division" aria-selected="true">
                                        <i class="fas fa-sitemap"></i> Division Reports
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="head-tab" data-toggle="tab" href="#head" role="tab" aria-controls="head" aria-selected="false">
                                        <i class="fas fa-user-tie"></i> Head Reports
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="agent-tab" data-toggle="tab" href="#agent" role="tab" aria-controls="agent" aria-selected="false">
                                        <i class="fas fa-user"></i> Sales Agent Reports
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="product-tab" data-toggle="tab" href="#product" role="tab" aria-controls="product" aria-selected="false">
                                        <i class="fas fa-pills"></i> Product Reports
                                    </a>
                                </li>
                            </ul>

                            <div class="tab-content mt-3" id="reportTabsContent">
                                <!-- Division Reports Tab -->
                                <div class="tab-pane fade show active" id="division" role="tabpanel" aria-labelledby="division-tab">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Division Sales Report</h6>
                                        </div>
                                        <div class="card-body">
                                            <form action="{{ url_for('finance_reports') }}" method="GET">
                                                <input type="hidden" name="report_type" value="division">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="division">Division</label>
                                                            <select class="form-control" id="division" name="division_id">
                                                                <option value="">All Divisions</option>
                                                                {% for division in divisions %}
                                                                <option value="{{ division.division_id }}">{{ division.name }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="date_range">Date Range</label>
                                                            <select class="form-control" id="date_range" name="date_range">
                                                                <option value="custom">Custom Range</option>
                                                                <option value="today">Today</option>
                                                                <option value="yesterday">Yesterday</option>
                                                                <option value="this_week">This Week</option>
                                                                <option value="last_week">Last Week</option>
                                                                <option value="this_month">This Month</option>
                                                                <option value="last_month">Last Month</option>
                                                                <option value="this_quarter">This Quarter</option>
                                                                <option value="last_quarter">Last Quarter</option>
                                                                <option value="this_year">This Year</option>
                                                                <option value="last_year">Last Year</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3 custom-date-range">
                                                        <div class="form-group">
                                                            <label for="start_date">Start Date</label>
                                                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', now.strftime('%Y-%m-%d')) }}">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3 custom-date-range">
                                                        <div class="form-group">
                                                            <label for="end_date">End Date</label>
                                                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', now.strftime('%Y-%m-%d')) }}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="fas fa-search"></i> Generate Report
                                                        </button>
                                                        {% if division_report %}
                                                        <a href="{{ url_for('export_finance_report', report_type='division', format='excel') }}?{{ request.query_string.decode() }}" class="btn btn-success ml-2">
                                                            <i class="fas fa-file-excel"></i> Export to Excel
                                                        </a>
                                                        <a href="{{ url_for('export_finance_report', report_type='division', format='pdf') }}?{{ request.query_string.decode() }}" class="btn btn-danger ml-2">
                                                            <i class="fas fa-file-pdf"></i> Export to PDF
                                                        </a>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </form>

                                            {% if division_report %}
                                            <div class="mt-4">
                                                <h6>Report Results</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-bordered">
                                                        <thead class="thead-dark">
                                                            <tr>
                                                                <th>Division</th>
                                                                <th>Total Orders</th>
                                                                <th>Total Sales</th>
                                                                <th>Average Order Value</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for row in division_report %}
                                                            <tr>
                                                                <td>{{ row.division_name }}</td>
                                                                <td>{{ row.order_count }}</td>
                                                                <td>{{ row.total_sales|format_currency }}</td>
                                                                <td>{{ row.avg_order|format_currency }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <div class="mt-4">
                                                    <canvas id="divisionReportChart" width="800" height="400"></canvas>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Head Reports Tab -->
                                <div class="tab-pane fade" id="head" role="tabpanel" aria-labelledby="head-tab">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Business Unit Head Sales Report</h6>
                                        </div>
                                        <div class="card-body">
                                            <form action="{{ url_for('finance_reports') }}" method="GET">
                                                <input type="hidden" name="report_type" value="head">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="head">Business Unit Head</label>
                                                            <select class="form-control" id="head" name="head_id">
                                                                <option value="">All Heads</option>
                                                                {% for head in heads %}
                                                                <option value="{{ head.id }}">{{ head.full_name or head.username }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="head_date_range">Date Range</label>
                                                            <select class="form-control" id="head_date_range" name="date_range">
                                                                <option value="custom">Custom Range</option>
                                                                <option value="this_month">This Month</option>
                                                                <option value="last_month">Last Month</option>
                                                                <option value="this_quarter">This Quarter</option>
                                                                <option value="last_quarter">Last Quarter</option>
                                                                <option value="this_year">This Year</option>
                                                                <option value="last_year">Last Year</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3 head-custom-date-range">
                                                        <div class="form-group">
                                                            <label for="head_start_date">Start Date</label>
                                                            <input type="date" class="form-control" id="head_start_date" name="start_date" value="{{ request.args.get('start_date', now.strftime('%Y-%m-%d')) }}">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3 head-custom-date-range">
                                                        <div class="form-group">
                                                            <label for="head_end_date">End Date</label>
                                                            <input type="date" class="form-control" id="head_end_date" name="end_date" value="{{ request.args.get('end_date', now.strftime('%Y-%m-%d')) }}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="fas fa-search"></i> Generate Report
                                                        </button>
                                                        {% if head_report %}
                                                        <a href="{{ url_for('export_finance_report', report_type='head', format='excel') }}?{{ request.query_string.decode() }}" class="btn btn-success ml-2">
                                                            <i class="fas fa-file-excel"></i> Export to Excel
                                                        </a>
                                                        <a href="{{ url_for('export_finance_report', report_type='head', format='pdf') }}?{{ request.query_string.decode() }}" class="btn btn-danger ml-2">
                                                            <i class="fas fa-file-pdf"></i> Export to PDF
                                                        </a>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </form>

                                            {% if head_report %}
                                            <div class="mt-4">
                                                <h6>Report Results</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-bordered">
                                                        <thead class="thead-dark">
                                                            <tr>
                                                                <th>Head Name</th>
                                                                <th>Division</th>
                                                                <th>Total Orders</th>
                                                                <th>Total Sales</th>
                                                                <th>Target</th>
                                                                <th>Achievement %</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for row in head_report %}
                                                            <tr>
                                                                <td>{{ row.head_name }}</td>
                                                                <td>{{ row.division_name }}</td>
                                                                <td>{{ row.order_count }}</td>
                                                                <td>{{ row.total_sales|format_currency }}</td>
                                                                <td>{{ row.target|format_currency }}</td>
                                                                <td>{{ row.achievement }}%</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Agent Reports Tab -->
                                <div class="tab-pane fade" id="agent" role="tabpanel" aria-labelledby="agent-tab">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Sales Agent Report</h6>
                                        </div>
                                        <div class="card-body">
                                            <form action="{{ url_for('finance_reports') }}" method="GET">
                                                <input type="hidden" name="report_type" value="agent">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="agent">Sales Agent</label>
                                                            <select class="form-control" id="agent" name="agent_id">
                                                                <option value="">All Agents</option>
                                                                {% for agent in agents %}
                                                                <option value="{{ agent.id }}">{{ agent.full_name or agent.username }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="agent_date_range">Date Range</label>
                                                            <select class="form-control" id="agent_date_range" name="date_range">
                                                                <option value="custom">Custom Range</option>
                                                                <option value="this_month">This Month</option>
                                                                <option value="last_month">Last Month</option>
                                                                <option value="this_quarter">This Quarter</option>
                                                                <option value="this_year">This Year</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="agent_start_date">Start Date</label>
                                                            <input type="date" class="form-control" id="agent_start_date" name="start_date">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="agent_end_date">End Date</label>
                                                            <input type="date" class="form-control" id="agent_end_date" name="end_date">
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i> Generate Report
                                                </button>
                                            </form>

                                            {% if agent_report %}
                                            <div class="mt-4">
                                                <h6>Report Results</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-bordered">
                                                        <thead class="thead-dark">
                                                            <tr>
                                                                <th>Agent Name</th>
                                                                <th>Username</th>
                                                                <th>Total Orders</th>
                                                                <th>Total Sales</th>
                                                                <th>Target</th>
                                                                <th>Achievement %</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for row in agent_report %}
                                                            <tr>
                                                                <td>{{ row.agent_name }}</td>
                                                                <td>{{ row.username }}</td>
                                                                <td>{{ row.order_count }}</td>
                                                                <td>{{ row.total_sales|format_currency }}</td>
                                                                <td>{{ row.target|format_currency }}</td>
                                                                <td>{{ row.achievement }}%</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Product Reports Tab -->
                                <div class="tab-pane fade" id="product" role="tabpanel" aria-labelledby="product-tab">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Product Sales Report</h6>
                                        </div>
                                        <div class="card-body">
                                            <form action="{{ url_for('finance_reports') }}" method="GET">
                                                <input type="hidden" name="report_type" value="product">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="product">Product</label>
                                                            <select class="form-control" id="product" name="product_id">
                                                                <option value="">All Products</option>
                                                                {% for product in products %}
                                                                <option value="{{ product.product_id }}">{{ product.name }} ({{ product.strength }})</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="product_date_range">Date Range</label>
                                                            <select class="form-control" id="product_date_range" name="date_range">
                                                                <option value="custom">Custom Range</option>
                                                                <option value="this_month">This Month</option>
                                                                <option value="last_month">Last Month</option>
                                                                <option value="this_quarter">This Quarter</option>
                                                                <option value="this_year">This Year</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="product_start_date">Start Date</label>
                                                            <input type="date" class="form-control" id="product_start_date" name="start_date">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="product_end_date">End Date</label>
                                                            <input type="date" class="form-control" id="product_end_date" name="end_date">
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i> Generate Report
                                                </button>
                                            </form>

                                            {% if product_report %}
                                            <div class="mt-4">
                                                <h6>Report Results</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-bordered">
                                                        <thead class="thead-dark">
                                                            <tr>
                                                                <th>Product Name</th>
                                                                <th>Strength</th>
                                                                <th>Total Orders</th>
                                                                <th>Total Quantity</th>
                                                                <th>Total Sales</th>
                                                                <th>Average Price</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for row in product_report %}
                                                            <tr>
                                                                <td>{{ row.product_name }}</td>
                                                                <td>{{ row.strength }}</td>
                                                                <td>{{ row.order_count }}</td>
                                                                <td>{{ row.total_quantity }}</td>
                                                                <td>{{ row.total_sales|format_currency }}</td>
                                                                <td>{{ row.avg_price|format_currency }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    $(function() {
        // Handle date range selection
        $('#date_range').change(function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').show();
            } else {
                $('.custom-date-range').hide();
            }
        });

        $('#head_date_range').change(function() {
            if ($(this).val() === 'custom') {
                $('.head-custom-date-range').show();
            } else {
                $('.head-custom-date-range').hide();
            }
        });

        // Initialize based on current selection
        if ($('#date_range').val() !== 'custom') {
            $('.custom-date-range').hide();
        }

        if ($('#head_date_range').val() !== 'custom') {
            $('.head-custom-date-range').hide();
        }

        // Division Report Chart
        {% if division_report %}
        var divisionReportCtx = document.getElementById('divisionReportChart').getContext('2d');
        var divisionReportChart = new Chart(divisionReportCtx, {
            type: 'bar',
            data: {
                labels: [{% for row in division_report %}'{{ row.division_name }}',{% endfor %}],
                datasets: [{
                    label: 'Total Sales',
                    data: [{% for row in division_report %}{{ row.total_sales }},{% endfor %}],
                    backgroundColor: 'rgba(54, 162, 235, 0.7)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Sales Amount'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Division'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Sales by Division'
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}
