{% extends "base.html" %}

{% block title %}Finance Dashboard - Medivent ERP{% endblock %}

{% block content %}
<style>
    /* Modern Finance Dashboard Styles - Matching Reference Image */
    .finance-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Top Statistics Cards */
    .stats-row {
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        position: relative;
        height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin-bottom: 15px;
        float: left;
    }

    .stat-content {
        margin-left: 65px;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
        line-height: 1;
    }

    .stat-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
        margin: 0;
    }

    /* Icon Colors */
    .stat-icon.revenue { background: linear-gradient(135deg, #27ae60, #2ecc71); }
    .stat-icon.monthly { background: linear-gradient(135deg, #3498db, #5dade2); }
    .stat-icon.pending { background: linear-gradient(135deg, #f39c12, #f1c40f); }
    .stat-icon.orders { background: linear-gradient(135deg, #e67e22, #f39c12); }

    /* Navigation Grid */
    .nav-grid {
        margin-bottom: 30px;
    }

    .nav-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 20px;
        text-decoration: none;
        color: inherit;
    }

    .nav-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        text-decoration: none;
        color: inherit;
    }

    .nav-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: white;
        margin: 0 auto 10px;
    }

    .nav-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        line-height: 1.2;
    }

    .nav-subtitle {
        font-size: 0.75rem;
        color: #7f8c8d;
        margin: 5px 0 0;
        line-height: 1.2;
    }

    /* Navigation Icon Colors */
    .nav-icon.pending-invoices { background: linear-gradient(135deg, #e74c3c, #c0392b); }
    .nav-icon.customer-ledger { background: linear-gradient(135deg, #1abc9c, #16a085); }
    .nav-icon.payment-collection { background: linear-gradient(135deg, #3498db, #2980b9); }
    .nav-icon.financial-reports { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
    .nav-icon.accounts-receivable { background: linear-gradient(135deg, #34495e, #2c3e50); }
    .nav-icon.accounts-payable { background: linear-gradient(135deg, #e91e63, #ad1457); }
    .nav-icon.duplicate-detection { background: linear-gradient(135deg, #ff9800, #f57c00); }
    .nav-icon.payment-knockoff { background: linear-gradient(135deg, #795548, #5d4037); }
    .nav-icon.salesperson-ledger { background: linear-gradient(135deg, #607d8b, #455a64); }
    .nav-icon.division-ledger { background: linear-gradient(135deg, #4caf50, #388e3c); }
    .nav-icon.comprehensive-report { background: linear-gradient(135deg, #673ab7, #512da8); }
    .nav-icon.order-workflow { background: linear-gradient(135deg, #ff5722, #d84315); }

    /* Recent Orders Section */
    .recent-orders {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-top: 30px;
    }

    .recent-orders h3 {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .recent-orders h3 i {
        margin-right: 10px;
        color: #3498db;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ecf0f1;
    }

    .order-item:last-child {
        border-bottom: none;
    }

    .order-info h5 {
        color: #2c3e50;
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 3px;
    }

    .order-info p {
        color: #7f8c8d;
        font-size: 0.8rem;
        margin: 0;
    }

    .order-amount {
        font-size: 1rem;
        font-weight: 700;
        color: #27ae60;
        text-align: right;
    }

    .status-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 500;
        margin-top: 3px;
        display: inline-block;
    }

    .status-paid {
        background: #d5f4e6;
        color: #27ae60;
    }

    .status-pending {
        background: #fef9e7;
        color: #f39c12;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stat-value {
            font-size: 1.5rem;
        }

        .nav-card {
            padding: 15px;
            height: 100px;
        }

        .nav-icon {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }

        .nav-title {
            font-size: 0.8rem;
        }

        .nav-subtitle {
            font-size: 0.7rem;
        }
    }
</style>

<div class="finance-dashboard">
    <div class="dashboard-container">
        <!-- Statistics Cards -->
        <div class="row stats-row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon revenue">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format(stats.total_revenue) }}</div>
                        <div class="stat-label">Total Revenue</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon monthly">
                        <i class="fas fa-calendar-month"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format(stats.monthly_revenue) }}</div>
                        <div class="stat-label">This Month</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format(stats.pending_payments) }}</div>
                        <div class="stat-label">Pending Payments</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.paid_orders }}</div>
                        <div class="stat-label">Paid Orders</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Grid -->
        <div class="row nav-grid">
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('finance_pending_invoices_management') }}" class="nav-card">
                    <div class="nav-icon pending-invoices">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="nav-title">Pending Invoices</div>
                    <div class="nav-subtitle">Manage and track pending invoices</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('finance_customer_ledger') }}" class="nav-card">
                    <div class="nav-icon customer-ledger">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="nav-title">Customer Ledger</div>
                    <div class="nav-subtitle">Customer account statements</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('finance_payment_collection') }}" class="nav-card">
                    <div class="nav-icon payment-collection">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="nav-title">Payment Collection</div>
                    <div class="nav-subtitle">Collect and process payments</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('financial_reports') }}" class="nav-card">
                    <div class="nav-icon financial-reports">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="nav-title">Financial Reports</div>
                    <div class="nav-subtitle">Generate financial reports</div>
                </a>
            </div>
        </div>

        <div class="row nav-grid">
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('accounts_receivable') }}" class="nav-card">
                    <div class="nav-icon accounts-receivable">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="nav-title">Accounts Receivable</div>
                    <div class="nav-subtitle">Manage outstanding receivables</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('accounts_payable') }}" class="nav-card">
                    <div class="nav-icon accounts-payable">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="nav-title">Accounts Payable</div>
                    <div class="nav-subtitle">Manage outstanding payables</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('duplicate_order_detection') }}" class="nav-card">
                    <div class="nav-icon duplicate-detection">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="nav-title">Duplicate Detection</div>
                    <div class="nav-subtitle">Detect duplicate entries</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="#" class="nav-card">
                    <div class="nav-icon payment-knockoff">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <div class="nav-title">Payment Knock-off</div>
                    <div class="nav-subtitle">Knock off pending payments</div>
                </a>
            </div>
        </div>

        <div class="row nav-grid">
            <div class="col-lg-3 col-md-6">
                <a href="#" class="nav-card">
                    <div class="nav-icon salesperson-ledger">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="nav-title">Salesperson Ledger</div>
                    <div class="nav-subtitle">Sales team performance</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="#" class="nav-card">
                    <div class="nav-icon division-ledger">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="nav-title">Division Ledger</div>
                    <div class="nav-subtitle">Division-wise analysis</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('comprehensive_finance_reports') }}" class="nav-card">
                    <div class="nav-icon comprehensive-report">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="nav-title">Comprehensive Report</div>
                    <div class="nav-subtitle">Detailed financial analysis</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('advanced_financial_analytics') }}" class="nav-card">
                    <div class="nav-icon order-workflow">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="nav-title">Advanced Analytics</div>
                    <div class="nav-subtitle">AI-powered financial insights</div>
                </a>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="row">
            <div class="col-12">
                <div class="recent-orders">
                    <h3>
                        <i class="fas fa-history"></i>Recent Orders
                    </h3>

                    {% if stats.recent_orders %}
                        {% for order in stats.recent_orders %}
                        <div class="order-item">
                            <div class="order-info">
                                <h5>{{ order.customer_name }}</h5>
                                <p>{{ order.order_id }} • {{ order.order_date }}</p>
                            </div>
                            <div class="order-amount">
                                ₹{{ "{:,.0f}".format(order.order_amount) }}
                                <div class="status-badge {% if order.payment_status == 'paid' %}status-paid{% else %}status-pending{% endif %}">
                                    {{ order.payment_status.title() }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No recent orders found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh stats every 30 seconds
setInterval(function() {
    fetch('{{ url_for("finance_api_stats") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update stats without page reload
                console.log('Stats refreshed');
            }
        })
        .catch(error => console.log('Auto-refresh error:', error));
}, 30000);
</script>
{% endblock %}
