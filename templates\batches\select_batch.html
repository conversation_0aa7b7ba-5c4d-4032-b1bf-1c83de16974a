{% extends 'base.html' %}

{% block title %}Select Batch{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Select Batch</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Batches
                            </a>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Select a Batch</h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" action="{{ url_for('batches', view='details') }}">
                                        <div class="form-group">
                                            <label for="batch_id">Batch</label>
                                            <select class="form-control" id="batch_id" name="batch_id" required>
                                                <option value="">-- Select Batch --</option>
                                                {% for batch in batches %}
                                                <option value="{{ batch.batch_id }}">{{ batch.product_name }} - {{ batch.batch_number }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">View Details</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
