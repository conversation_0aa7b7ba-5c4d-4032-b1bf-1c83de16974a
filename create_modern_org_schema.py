#!/usr/bin/env python3
"""
Create modern organization database schema with hierarchical structure
"""

import sqlite3
import os
from datetime import datetime

def create_modern_organization_schema():
    """Create clean, normalized database structure for organization module"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🏗️  CREATING MODERN ORGANIZATION SCHEMA")
        print("=" * 60)
        
        # 1. Modern Divisions Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modern_divisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                code VARCHAR(20) NOT NULL UNIQUE,
                description TEXT,
                manager_id INTEGER,
                parent_division_id INTEGER,
                budget DECIMAL(15,2) DEFAULT 0,
                target_revenue DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'active',
                color_code VARCHAR(7) DEFAULT '#007bff',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (manager_id) REFERENCES modern_employees(id),
                FOREIGN KEY (parent_division_id) REFERENCES modern_divisions(id)
            )
        ''')
        print("✅ Created modern_divisions table")
        
        # 2. Modern Teams Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modern_teams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                division_id INTEGER NOT NULL,
                team_lead_id INTEGER,
                description TEXT,
                team_type VARCHAR(50) DEFAULT 'operational',
                target_members INTEGER DEFAULT 5,
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (division_id) REFERENCES modern_divisions(id) ON DELETE CASCADE,
                FOREIGN KEY (team_lead_id) REFERENCES modern_employees(id)
            )
        ''')
        print("✅ Created modern_teams table")
        
        # 3. Modern Employees Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modern_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_code VARCHAR(20) NOT NULL UNIQUE,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE,
                phone VARCHAR(20),
                division_id INTEGER,
                team_id INTEGER,
                position_id INTEGER,
                manager_id INTEGER,
                hire_date DATE,
                salary DECIMAL(10,2),
                status VARCHAR(20) DEFAULT 'active',
                profile_image VARCHAR(255),
                address TEXT,
                emergency_contact VARCHAR(100),
                emergency_phone VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (division_id) REFERENCES modern_divisions(id),
                FOREIGN KEY (team_id) REFERENCES modern_teams(id),
                FOREIGN KEY (position_id) REFERENCES modern_positions(id),
                FOREIGN KEY (manager_id) REFERENCES modern_employees(id)
            )
        ''')
        print("✅ Created modern_employees table")
        
        # 4. Modern Positions Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modern_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(100) NOT NULL,
                level INTEGER DEFAULT 1,
                department VARCHAR(50),
                description TEXT,
                min_salary DECIMAL(10,2),
                max_salary DECIMAL(10,2),
                required_skills TEXT,
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ Created modern_positions table")
        
        # 5. Performance Metrics Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                metric_type VARCHAR(50) NOT NULL,
                metric_value DECIMAL(10,2) NOT NULL,
                target_value DECIMAL(10,2),
                period_start DATE NOT NULL,
                period_end DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES modern_employees(id) ON DELETE CASCADE
            )
        ''')
        print("✅ Created employee_performance table")
        
        # 6. Division Performance Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS division_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                division_id INTEGER NOT NULL,
                revenue DECIMAL(15,2) DEFAULT 0,
                orders_count INTEGER DEFAULT 0,
                target_revenue DECIMAL(15,2) DEFAULT 0,
                employee_count INTEGER DEFAULT 0,
                performance_score DECIMAL(5,2) DEFAULT 0,
                period_month INTEGER NOT NULL,
                period_year INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (division_id) REFERENCES modern_divisions(id) ON DELETE CASCADE
            )
        ''')
        print("✅ Created division_performance table")
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_employees_division ON modern_employees(division_id)",
            "CREATE INDEX IF NOT EXISTS idx_employees_team ON modern_employees(team_id)",
            "CREATE INDEX IF NOT EXISTS idx_employees_manager ON modern_employees(manager_id)",
            "CREATE INDEX IF NOT EXISTS idx_teams_division ON modern_teams(division_id)",
            "CREATE INDEX IF NOT EXISTS idx_performance_employee ON employee_performance(employee_id)",
            "CREATE INDEX IF NOT EXISTS idx_division_performance ON division_performance(division_id, period_year, period_month)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        print("✅ Created performance indexes")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 MODERN ORGANIZATION SCHEMA CREATED!")
        print("=" * 60)
        print("✅ Tables: modern_divisions, modern_teams, modern_employees")
        print("✅ Tables: modern_positions, employee_performance, division_performance")
        print("✅ Foreign key relationships established")
        print("✅ Performance indexes created")
        print("🎯 Ready for modern organization module!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating organization schema: {e}")
        return False

if __name__ == "__main__":
    success = create_modern_organization_schema()
    if success:
        print("\n🎉 SUCCESS! Modern organization schema created!")
    else:
        print("\n❌ FAILED to create organization schema")
