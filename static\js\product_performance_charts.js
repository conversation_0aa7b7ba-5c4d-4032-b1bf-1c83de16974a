// Product Performance Charts

function initProductPerformanceCharts() {
    // Get product data from the table
    const productData = [];
    const table = document.querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 7) {
            productData.push({
                name: cells[0].textContent + (cells[1].textContent ? ` (${cells[1].textContent})` : ''),
                quantity: parseInt(cells[2].textContent) || 0,
                sales: parseFloat(cells[3].textContent.replace(/[^\d.-]/g, '')) || 0,
                orders: parseInt(cells[4].textContent) || 0,
                customers: parseInt(cells[5].textContent) || 0,
                avgPrice: parseFloat(cells[6].textContent.replace(/[^\d.-]/g, '')) || 0
            });
        }
    });
    
    // Sort products by sales and get top 10
    const topProductsBySales = [...productData].sort((a, b) => b.sales - a.sales).slice(0, 10);
    
    // Sort products by quantity and get top 10
    const topProductsByQuantity = [...productData].sort((a, b) => b.quantity - a.quantity).slice(0, 10);
    
    // Group products by division
    const divisionMap = {};
    const divisionQuantityMap = {};
    
    // Try to extract division from product name or use "Unknown"
    productData.forEach(product => {
        let division = "Unknown";
        
        // Check if we can find division in the product name
        const divisionMatch = product.name.match(/\((.*?)\)/);
        if (divisionMatch && divisionMatch[1]) {
            division = divisionMatch[1];
        }
        
        // Accumulate sales by division
        if (!divisionMap[division]) {
            divisionMap[division] = 0;
        }
        divisionMap[division] += product.sales;
        
        // Accumulate quantity by division
        if (!divisionQuantityMap[division]) {
            divisionQuantityMap[division] = 0;
        }
        divisionQuantityMap[division] += product.quantity;
    });
    
    // Create charts
    if (topProductsBySales.length > 0) {
        // Product Sales Chart
        createBarChart(
            'productSalesChart',
            topProductsBySales.map(p => p.name),
            topProductsBySales.map(p => p.sales),
            'rgba(54, 162, 235, 0.5)',
            'rgba(54, 162, 235, 1)',
            'Sales (PKR)'
        );
        
        // Product Quantity Chart
        createBarChart(
            'productQuantityChart',
            topProductsByQuantity.map(p => p.name),
            topProductsByQuantity.map(p => p.quantity),
            'rgba(75, 192, 192, 0.5)',
            'rgba(75, 192, 192, 1)',
            'Quantity Sold'
        );
        
        // Division Sales Chart
        const divisionLabels = Object.keys(divisionMap);
        const divisionSales = divisionLabels.map(d => divisionMap[d]);
        const divisionColors = generateColors(divisionLabels.length);
        
        createPieChart(
            'divisionSalesChart',
            divisionLabels,
            divisionSales,
            divisionColors,
            'white'
        );
        
        // Division Quantity Chart
        const divisionQuantityLabels = Object.keys(divisionQuantityMap);
        const divisionQuantities = divisionQuantityLabels.map(d => divisionQuantityMap[d]);
        
        createPieChart(
            'divisionQuantityChart',
            divisionQuantityLabels,
            divisionQuantities,
            divisionColors,
            'white'
        );
    }
}

// Initialize charts when the DOM is loaded
document.addEventListener('DOMContentLoaded', initProductPerformanceCharts);
