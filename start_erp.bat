@echo off
setlocal enabledelayedexpansion
title Medivent ERP System - Automated Setup and Launch

echo.
echo ================================================================
echo                    MEDIVENT ERP SYSTEM
echo                  Automated Setup and Launch
echo ================================================================
echo.

:: Check if Python is installed
echo [1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo ✅ Python !PYTHON_VERSION! found
)

echo.
echo [2/5] Checking and installing dependencies...
echo Installing required packages from requirements.txt...

:: Install dependencies with progress indication
pip install -r requirements.txt --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ❌ ERROR: Failed to install dependencies
    echo Trying alternative installation method...
    pip install Flask pandas matplotlib plotly reportlab pillow Flask-Login Flask-SQLAlchemy Flask-WTF xlsxwriter openpyxl seaborn bleach
    if errorlevel 1 (
        echo ❌ CRITICAL ERROR: Could not install required dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)
echo ✅ Dependencies installed successfully

echo.
echo [3/5] Verifying application integrity...
python -c "import app; print('✅ Application modules verified')" 2>nul
if errorlevel 1 (
    echo ❌ ERROR: Application has syntax errors or missing modules
    echo Please check the application files
    pause
    exit /b 1
)

echo.
echo [4/5] Checking database and configuration...
if exist "instance\medivent.db" (
    echo ✅ Database found
) else (
    echo ⚠️  Database not found - will be created on first run
)

echo.
echo [5/5] Starting Medivent ERP System...
echo.
echo ================================================================
echo                        SERVER STARTING
echo ================================================================
echo.
echo 🌐 Server URL: http://localhost:3000
echo 📱 Network URL: http://*************:3000 (if accessible)
echo.
echo 👤 Default Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 🔧 System Features Available:
echo    • Inventory Management
echo    • Order Processing
echo    • Financial Management
echo    • Customer Management
echo    • Reporting & Analytics
echo    • User Management
echo.
echo ⚠️  IMPORTANT NOTES:
echo    • Keep this window open while using the system
echo    • Press Ctrl+C to stop the server
echo    • The browser will open automatically
echo.
echo ================================================================
echo.

:: Start the application
echo Starting Flask server...
start "" "http://localhost:3000"
python run_app.py

echo.
echo ================================================================
echo Server has stopped. Press any key to exit.
pause >nul
