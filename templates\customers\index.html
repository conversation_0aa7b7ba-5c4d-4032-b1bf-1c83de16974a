{% extends 'base.html' %}

{% block title %}Customer Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Customer Management</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View All Customers
                            </a>
                            <a href="{{ url_for('customers', view='by_type') }}" class="btn btn-secondary">
                                <i class="fas fa-user-tag"></i> View Customers by Type
                            </a>
                            <a href="{{ url_for('customers', action='add') }}" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> Add New Customer
                            </a>
                            <a href="{{ url_for('customers', view='pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-tags"></i> View Customer Pricing
                            </a>
                            <a href="{{ url_for('customers', action='set_pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-dollar-sign"></i> Set Customer Pricing
                            </a>
                            <a href="{{ url_for('customers', view='orders') }}" class="btn btn-secondary">
                                <i class="fas fa-history"></i> View Customer Order History
                            </a>
                            <a href="{{ url_for('export_customers') }}" class="btn btn-success">
                                <i class="fas fa-download"></i> Export Customers
                            </a>
                        </div>
                    </div>

                    {% if action == 'add' %}
                    <!-- Add Customer Form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Add New Customer</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('add_customer') }}">
                                        <div class="form-group">
                                            <label for="name">Customer Name</label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="contact">Contact Number</label>
                                            <input type="text" class="form-control" id="contact" name="contact" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="address">Address</label>
                                            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="customer_type">Customer Type</label>
                                            <select class="form-control" id="customer_type" name="customer_type">
                                                <option value="Distributor">Distributor</option>
                                                <option value="Hospital">Hospital</option>
                                                <option value="Pharmacy">Pharmacy</option>
                                                <option value="Doctor">Doctor</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Add Customer</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <!-- Customers List -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Contact</th>
                                    <th>Address</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if customers %}
                                    {% for customer in customers %}
                                    <tr>
                                        <td>{{ customer.customer_id }}</td>
                                        <td>{{ customer.name }}</td>
                                        <td>{{ customer.phone }}</td>
                                        <td>{{ customer.address }}</td>
                                        <td>{{ customer.type }}</td>
                                        <td>
                                            <a href="{{ url_for('customers', view='orders', customer_id=customer.customer_id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-history"></i> Order History
                                            </a>
                                            <a href="{{ url_for('customers', view='pricing', customer_id=customer.customer_id) }}" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-tags"></i> Pricing
                                            </a>
                                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editCustomerModal{{ customer.customer_id }}">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center">No customers found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Customer Modals -->
{% if customers %}
    {% for customer in customers %}
    <div class="modal fade" id="editCustomerModal{{ customer.customer_id }}" tabindex="-1" role="dialog" aria-labelledby="editCustomerModalLabel{{ customer.customer_id }}" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editCustomerModalLabel{{ customer.customer_id }}">Edit Customer</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="post" action="{{ url_for('customers', action='edit', customer_id=customer.customer_id) }}">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="name{{ customer.customer_id }}">Customer Name</label>
                            <input type="text" class="form-control" id="name{{ customer.customer_id }}" name="name" value="{{ customer.name }}" required>
                        </div>
                        <div class="form-group">
                            <label for="contact{{ customer.customer_id }}">Contact Number</label>
                            <input type="text" class="form-control" id="contact{{ customer.customer_id }}" name="contact" value="{{ customer.contact }}" required>
                        </div>
                        <div class="form-group">
                            <label for="address{{ customer.customer_id }}">Address</label>
                            <textarea class="form-control" id="address{{ customer.customer_id }}" name="address" rows="3" required>{{ customer.address }}</textarea>
                        </div>
                        <div class="form-group">
                            <label for="customer_type{{ customer.customer_id }}">Customer Type</label>
                            <select class="form-control" id="customer_type{{ customer.customer_id }}" name="customer_type">
                                <option value="Distributor" {% if customer.type == 'Distributor' %}selected{% endif %}>Distributor</option>
                                <option value="Hospital" {% if customer.type == 'Hospital' %}selected{% endif %}>Hospital</option>
                                <option value="Pharmacy" {% if customer.type == 'Pharmacy' %}selected{% endif %}>Pharmacy</option>
                                <option value="Doctor" {% if customer.type == 'Doctor' %}selected{% endif %}>Doctor</option>
                                <option value="Other" {% if customer.type == 'Other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endfor %}
{% endif %}
{% endblock %}
