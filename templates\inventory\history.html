{% extends 'base.html' %}

{% block title %}Inventory History - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Inventory History</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Stock Details</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Product:</th>
                                    <td>{{ inventory.product_name }} ({{ inventory.strength }})</td>
                                </tr>
                                <tr>
                                    <th>Batch Number:</th>
                                    <td>{{ inventory.batch_number }}</td>
                                </tr>
                                <tr>
                                    <th>Warehouse:</th>
                                    <td>{{ inventory.warehouse_name }}</td>
                                </tr>
                                <tr>
                                    <th>Current Quantity:</th>
                                    <td>{{ inventory.stock_quantity }} {{ inventory.unit_of_measure }}</td>
                                </tr>
                                <tr>
                                    <th>Expiry Date:</th>
                                    <td>{{ inventory.expiry_date }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge 
                                            {% if inventory.status == 'active' %}badge-success
                                            {% elif inventory.status == 'low' %}badge-warning
                                            {% elif inventory.status == 'expired' %}badge-danger
                                            {% else %}badge-secondary
                                            {% endif %}">
                                            {{ inventory.status }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Inventory Timeline</h5>
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Stock Received</h6>
                                        <p>{{ inventory.date_received }} by {{ inventory.received_by }}</p>
                                    </div>
                                </div>
                                
                                {% if inventory.last_updated %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Last Updated</h6>
                                        <p>{{ inventory.last_updated }} by {{ inventory.updated_by }}</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <h5>Movement History</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Quantity</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Moved By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in movements %}
                                <tr>
                                    <td>{{ movement.movement_date }}</td>
                                    <td>{{ movement.movement_type }}</td>
                                    <td>{{ movement.quantity }}</td>
                                    <td>{{ movement.from_warehouse_name }}</td>
                                    <td>{{ movement.to_warehouse_name }}</td>
                                    <td>{{ movement.moved_by }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12 text-right">
                            <a href="{{ url_for('inventory') }}" class="btn btn-secondary">Back to Inventory</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline-item {
        position: relative;
        padding-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-top: 5px;
    }
    
    .timeline-item:not(:last-child):before {
        content: '';
        position: absolute;
        left: -23px;
        top: 20px;
        height: calc(100% - 20px);
        width: 2px;
        background-color: #e9ecef;
    }
    
    .timeline-content {
        margin-bottom: 10px;
    }
    
    .timeline-title {
        margin-bottom: 5px;
    }
</style>
{% endblock %}
