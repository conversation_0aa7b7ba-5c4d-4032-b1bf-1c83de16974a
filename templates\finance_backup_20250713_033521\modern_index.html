{% extends 'base.html' %}

{% block title %}Finance & Accounts - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<style>
/* Modern 2025 Finance Management UI */
.finance-management {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.finance-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.finance-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4e73df, #224abe, #36b9cc);
}

.finance-title {
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.modern-finance-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modern-finance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.modern-btn-finance {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
    text-decoration: none;
    display: inline-block;
}

.modern-btn-finance:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
    text-decoration: none;
}

.modern-btn-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.modern-btn-success:hover {
    box-shadow: 0 15px 30px rgba(28, 200, 138, 0.4);
}

.modern-btn-warning {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    box-shadow: 0 10px 20px rgba(246, 194, 62, 0.3);
}

.modern-btn-warning:hover {
    box-shadow: 0 15px 30px rgba(246, 194, 62, 0.4);
}

.modern-btn-danger {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    box-shadow: 0 10px 20px rgba(231, 74, 59, 0.3);
}

.modern-btn-danger:hover {
    box-shadow: 0 15px 30px rgba(231, 74, 59, 0.4);
}

.modern-btn-info {
    background: linear-gradient(135deg, #36b9cc, #258391);
    box-shadow: 0 10px 20px rgba(54, 185, 204, 0.3);
}

.modern-btn-info:hover {
    box-shadow: 0 15px 30px rgba(54, 185, 204, 0.4);
}

.stat-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #4e73df, #224abe);
}

.stat-number-modern {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stat-label-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0;
}

.finance-nav-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.finance-nav-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.finance-nav-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@media (max-width: 768px) {
    .finance-title {
        font-size: 2rem;
    }
    .stat-number-modern {
        font-size: 2.5rem;
    }
}
</style>

<div class="finance-management">
    <div class="container-fluid">
        <!-- Modern Header -->
        <div class="finance-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-money-bill-wave mr-3"></i>Finance & Accounts
                    </h1>
                    <p class="text-muted mb-0 mt-2">Comprehensive financial management and reporting system</p>
                </div>
                <div>
                    <a href="{{ url_for('finance_financial_reports') }}" class="modern-btn-finance modern-btn-info">
                        <i class="fas fa-chart-bar mr-2"></i>Reports
                    </a>
                    <a href="{{ url_for('finance_payment_collection') }}" class="modern-btn-finance modern-btn-success">
                        <i class="fas fa-hand-holding-usd mr-2"></i>Collect Payment
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern Performance Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern">Rs. {{ "{:,.0f}"|format(total_revenue or 0) }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-chart-line mr-2"></i>Total Revenue
                            </div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-dollar-sign fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-warning">{{ pending_invoices_count or 0 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-file-invoice-dollar mr-2 text-warning"></i>Pending Invoices
                            </div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-info">Rs. {{ "{:,.0f}"|format(outstanding_amount or 0) }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-exclamation-triangle mr-2 text-info"></i>Outstanding
                            </div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-balance-scale fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-success">{{ payments_today or 0 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-check-circle mr-2 text-success"></i>Payments Today
                            </div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-money-bill-wave fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Finance Navigation Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_pending_invoices') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <h5 class="font-weight-bold text-primary">Pending Invoices</h5>
                        <p class="text-muted mb-0">Review and process pending invoices</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_payment_collection') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <h5 class="font-weight-bold text-success">Payment Collection</h5>
                        <p class="text-muted mb-0">Record and manage payments</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_customer_ledger') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="font-weight-bold text-info">Customer Ledger</h5>
                        <p class="text-muted mb-0">View customer account details</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_bank_details') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <h5 class="font-weight-bold text-secondary">Bank Details</h5>
                        <p class="text-muted mb-0">Manage bank account information</p>
                    </div>
                </a>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_invoices') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h5 class="font-weight-bold text-primary">All Invoices</h5>
                        <p class="text-muted mb-0">View all generated invoices</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_financial_reports') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="font-weight-bold text-dark">Financial Reports</h5>
                        <p class="text-muted mb-0">Generate comprehensive reports</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_batch_costing') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h5 class="font-weight-bold text-warning">Batch Costing</h5>
                        <p class="text-muted mb-0">Analyze batch costs and margins</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('finance_accounts_receivable') }}" class="text-decoration-none">
                    <div class="finance-nav-card">
                        <div class="finance-nav-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <h5 class="font-weight-bold text-danger">Receivables</h5>
                        <p class="text-muted mb-0">Track accounts receivable</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Modern Finance Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects and animations
    const cards = document.querySelectorAll('.finance-nav-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Auto-refresh stats every 30 seconds
    setInterval(refreshStats, 30000);
});

function refreshStats() {
    // Refresh financial statistics
    fetch('/api/finance-data')
        .then(response => response.json())
        .then(data => {
            // Update stats if needed
            console.log('Finance stats refreshed');
        })
        .catch(error => {
            console.log('Error refreshing stats:', error);
        });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>
{% endblock %}
