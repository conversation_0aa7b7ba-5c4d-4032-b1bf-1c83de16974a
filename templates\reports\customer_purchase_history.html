{% extends 'base.html' %}

{% block title %}Customer Purchase History - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Customer Purchase History</h4>
                    <div>
                        <button class="btn btn-light" id="printCustomerPurchaseHistoryBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form action="{{ url_for('customer_purchase_history') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Customer</span>
                                    </div>
                                    <select class="form-control" name="customer_id" style="min-width: 200px;">
                                        <option value="">All Customers</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_id }}" 
                                                {% if customer.customer_id == selected_customer_id %}selected{% endif %}>
                                            {{ customer.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" class="form-control" name="start_date" 
                                           value="{{ request.args.get('start_date', '') }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" class="form-control" name="end_date" 
                                           value="{{ request.args.get('end_date', '') }}">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    {% if selected_customer %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Customer Summary: {{ selected_customer.name }}</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <p><strong>Total Orders:</strong> {{ summary.order_count|default(0) }}</p>
                                            <p><strong>First Order:</strong> {{ summary.first_order|default('N/A') }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Total Spent:</strong> ₨{{ "{:,.2f}".format(summary.total_spent|default(0)) }}</p>
                                            <p><strong>Last Order:</strong> {{ summary.last_order|default('N/A') }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Average Order:</strong> ₨{{ "{:,.2f}".format(summary.average_order|default(0)) }}</p>
                                            <p><strong>Phone:</strong> {{ selected_customer.phone|default('N/A') }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Address:</strong> {{ selected_customer.address|default('N/A') }}</p>
                                            <p><strong>Status:</strong> 
                                                <span class="badge badge-{% if selected_customer.is_active %}success{% else %}secondary{% endif %}">
                                                    {% if selected_customer.is_active %}Active{% else %}Inactive{% endif %}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Purchase History</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered" id="purchaseHistoryTable">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Payment Method</th>
                                            <th>Sales Agent</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if purchase_history %}
                                            {% for order in purchase_history %}
                                            <tr>
                                                <td>{{ order.order_id }}</td>
                                                <td>{{ order.customer_name }}</td>
                                                <td>{{ safe_strftime(order.order_date, '%Y-%m-%d') if order.order_date else 'N/A' }}</td>
                                                <td>₨{{ "{:,.2f}".format(order.order_amount) }}</td>
                                                <td>
                                                    <span class="badge 
                                                        {% if order.status == 'Delivered' %}badge-success
                                                        {% elif order.status == 'Dispatched' %}badge-info
                                                        {% elif order.status == 'Approved' %}badge-primary
                                                        {% elif order.status == 'Placed' %}badge-secondary
                                                        {% elif order.status == 'Cancelled' %}badge-danger
                                                        {% else %}badge-warning{% endif %}">
                                                        {{ order.status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-{% if order.payment_method == 'cash' %}success{% elif order.payment_method == 'cheque' %}warning{% else %}info{% endif %}">
                                                        {{ order.payment_method|title }}
                                                    </span>
                                                </td>
                                                <td>{{ order.sales_agent|default('N/A') }}</td>
                                                <td>
                                                    <a href="{{ url_for('view_order', order_id=order.order_id) }}"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    {% if order.status == 'Approved' or order.status == 'Delivered' %}
                                                    <a href="{{ url_for('generate_invoice', order_id=order.order_id) }}" 
                                                       class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-file-invoice"></i> Invoice
                                                    </a>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="8" class="text-center">No purchase history found</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {% if purchase_history %}
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Purchase Summary</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="purchaseTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="paymentMethodChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#purchaseHistoryTable').DataTable({
        "order": [[ 2, "desc" ]], // Sort by date descending
        "pageLength": 25,
        "responsive": true
    });

    {% if purchase_history %}
    // Purchase Trend Chart
    var ctx1 = document.getElementById('purchaseTrendChart').getContext('2d');
    var purchaseTrendChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: [{% for order in purchase_history %}'{{ safe_strftime(order.order_date, "%m/%d") if order.order_date else "N/A" }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Order Amount',
                data: [{% for order in purchase_history %}{{ order.order_amount }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₨' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Payment Method Chart
    var paymentMethods = {};
    {% for order in purchase_history %}
    var method = '{{ order.payment_method|title }}';
    if (paymentMethods[method]) {
        paymentMethods[method]++;
    } else {
        paymentMethods[method] = 1;
    }
    {% endfor %}

    var ctx2 = document.getElementById('paymentMethodChart').getContext('2d');
    var paymentMethodChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: Object.keys(paymentMethods),
            datasets: [{
                data: Object.values(paymentMethods),
                backgroundColor: ['#1cc88a', '#f6c23e', '#36b9cc', '#e74a3b']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    {% endif %}
});
</script>

<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printCustomerPurchaseHistoryBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .container-fluid, .container-fluid * {
                            visibility: visible;
                        }
                        .container-fluid {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        table {
                            page-break-inside: auto;
                        }
                        tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                    }
                `;
                
                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
