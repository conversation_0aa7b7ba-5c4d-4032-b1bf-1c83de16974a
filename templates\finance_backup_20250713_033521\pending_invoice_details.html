{% extends "base.html" %}

{% block title %}Pending Invoice Details - Finance{% endblock %}

{% block head %}
<style>
.detail-card {
    border-left: 4px solid #007bff;
    margin-bottom: 20px;
}
.customer-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}
.financial-summary {
    background-color: #e9ecef;
    border-radius: 8px;
    padding: 15px;
}
.payment-history {
    max-height: 300px;
    overflow-y: auto;
}
.batch-info {
    background-color: #fff3cd;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0">📋 Pending Invoice Details</h2>
                            <p class="mb-0">Order {{ pending_invoice.order_id }} - DC {{ pending_invoice.dc_number }}</p>
                        </div>
                        <div class="text-right">
                            <h3 class="text-primary">₹{{ "{:,.2f}".format(pending_invoice.total_amount) }}</h3>
                            <span class="badge badge-warning">{{ pending_invoice.pending_status|title }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{{ url_for('finance_pending_invoices') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Pending Invoices
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-md-6">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="customer-info">
                        <h6><strong>{{ pending_invoice.customer_name }}</strong></h6>
                        <p class="mb-2">📍 {{ pending_invoice.customer_address }}</p>
                        <p class="mb-2">📞 {{ pending_invoice.customer_phone }}</p>
                        <p class="mb-2">🏢 Branch: {{ pending_invoice.branch_code }}</p>
                        <p class="mb-0">🏭 Warehouse: {{ pending_invoice.warehouse_id }}</p>
                    </div>
                    
                    {% if pending_invoice.credit_limit %}
                    <div class="financial-summary mt-3">
                        <h6>Financial Summary</h6>
                        <div class="row">
                            <div class="col-6">
                                <small>Credit Limit:</small><br>
                                <strong>₹{{ "{:,.0f}".format(pending_invoice.credit_limit) }}</strong>
                            </div>
                            <div class="col-6">
                                <small>Outstanding:</small><br>
                                <strong class="text-warning">₹{{ "{:,.0f}".format(pending_invoice.total_outstanding or 0) }}</strong>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <small>Credit Rating:</small><br>
                                <span class="badge badge-info">{{ pending_invoice.credit_rating or 'B' }}</span>
                            </div>
                            <div class="col-6">
                                <small>Overdue:</small><br>
                                <strong class="text-danger">₹{{ "{:,.0f}".format(pending_invoice.overdue_amount or 0) }}</strong>
                            </div>
                        </div>
                        {% if pending_invoice.is_credit_hold %}
                        <div class="mt-2">
                            <span class="badge badge-danger">Credit Hold</span>
                            <small class="text-muted d-block">{{ pending_invoice.credit_hold_reason }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Order & DC Information -->
        <div class="col-md-6">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-file-alt"></i> Order & DC Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Order ID:</strong><br>
                            <span class="text-primary">{{ pending_invoice.order_id }}</span>
                        </div>
                        <div class="col-6">
                            <strong>DC Number:</strong><br>
                            <span class="text-info">{{ pending_invoice.dc_number }}</span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6">
                            <strong>DC Generated:</strong><br>
                            {{ pending_invoice.dc_generated_date }}
                        </div>
                        <div class="col-6">
                            <strong>Generated By:</strong><br>
                            {{ pending_invoice.dc_generated_by }}
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6">
                            <strong>Priority:</strong><br>
                            <span class="badge 
                                {% if pending_invoice.priority_level == 'urgent' %}badge-danger
                                {% elif pending_invoice.priority_level == 'high' %}badge-warning
                                {% else %}badge-success{% endif %}">
                                {{ pending_invoice.priority_level|title }}
                            </span>
                        </div>
                        <div class="col-6">
                            <strong>Total Amount:</strong><br>
                            <strong class="text-success">₹{{ "{:,.2f}".format(pending_invoice.total_amount) }}</strong>
                        </div>
                    </div>
                    
                    {% if order_details %}
                    <div class="mt-3">
                        <h6>Order Details</h6>
                        <small class="text-muted">
                            Order Date: {{ order_details.order_date }}<br>
                            Status: {{ order_details.status }}<br>
                            Payment Method: {{ order_details.payment_method }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Payment History -->
    <div class="row">
        <div class="col-md-6">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Payment History</h5>
                </div>
                <div class="card-body">
                    {% if payment_history %}
                        <div class="payment-history">
                            {% for payment in payment_history %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                <div>
                                    <strong>{{ payment.payment_date }}</strong><br>
                                    <small class="text-muted">{{ payment.payment_method|title }}</small>
                                    {% if payment.reference_number %}
                                    <br><small>Ref: {{ payment.reference_number }}</small>
                                    {% endif %}
                                </div>
                                <div class="text-right">
                                    <strong class="text-success">₹{{ "{:,.2f}".format(payment.amount) }}</strong>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No payment history available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Other Outstanding Invoices -->
        <div class="col-md-6">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Other Outstanding</h5>
                </div>
                <div class="card-body">
                    {% if other_outstanding %}
                        <div class="payment-history">
                            {% for invoice in other_outstanding %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                <div>
                                    <strong>{{ invoice.invoice_number }}</strong><br>
                                    <small class="text-muted">{{ invoice.invoice_date }}</small>
                                    {% if invoice.due_date %}
                                    <br><small>Due: {{ invoice.due_date }}</small>
                                    {% endif %}
                                </div>
                                <div class="text-right">
                                    <strong class="text-danger">₹{{ "{:,.2f}".format(invoice.outstanding_amount) }}</strong>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted">No other outstanding invoices</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Details -->
    {% if batch_details %}
    <div class="row">
        <div class="col-md-12">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-boxes"></i> Batch Details</h5>
                </div>
                <div class="card-body">
                    {% for batch in batch_details %}
                    <div class="batch-info">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>{{ batch.product_name }}</strong><br>
                                <small>{{ batch.strength }}</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Batch:</strong> {{ batch.batch_number }}<br>
                                <small>Qty: {{ batch.quantity }}</small>
                            </div>
                            <div class="col-md-3">
                                <strong>Mfg:</strong> {{ batch.manufacturing_date }}<br>
                                <strong>Exp:</strong> {{ batch.expiry_date }}
                            </div>
                            <div class="col-md-3">
                                <strong>Cost:</strong> ₹{{ "{:,.2f}".format(batch.total_cost) }}<br>
                                <small>Per unit: ₹{{ "{:.2f}".format(batch.cost_per_unit) }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-center">
                    {% if not pending_invoice.is_credit_hold %}
                    <form method="POST" action="{{ url_for('finance_generate_invoice_from_pending', pending_id=pending_invoice.pending_id) }}" 
                          class="d-inline">
                        <button type="submit" class="btn btn-success btn-lg mr-3"
                                onclick="return confirm('Generate invoice for {{ pending_invoice.customer_name }}?\n\nOrder: {{ pending_invoice.order_id }}\nAmount: ₹{{ \"{:,.2f}\".format(pending_invoice.total_amount) }}\n\nThis action cannot be undone.')">
                            <i class="fas fa-file-invoice"></i> Generate Invoice
                        </button>
                    </form>
                    {% endif %}
                    
                    <button type="button" class="btn btn-warning btn-lg mr-3" 
                            data-toggle="modal" data-target="#holdModal">
                        <i class="fas fa-pause"></i> Hold Invoice
                    </button>
                    
                    <a href="{{ url_for('finance_customer_ledger_details', customer_id=pending_invoice.customer_id) }}" 
                       class="btn btn-info btn-lg">
                        <i class="fas fa-user"></i> Customer Ledger
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hold Modal -->
<div class="modal fade" id="holdModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Hold Invoice Generation</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ url_for('finance_hold_invoice', pending_id=pending_invoice.pending_id) }}">
                <div class="modal-body">
                    <p><strong>Order:</strong> {{ pending_invoice.order_id }}</p>
                    <p><strong>Customer:</strong> {{ pending_invoice.customer_name }}</p>
                    <p><strong>Amount:</strong> ₹{{ "{:,.2f}".format(pending_invoice.total_amount) }}</p>
                    
                    <div class="form-group">
                        <label>Hold Reason:</label>
                        <select name="hold_reason" class="form-control" required>
                            <option value="">Select reason...</option>
                            <option value="Credit limit exceeded">Credit limit exceeded</option>
                            <option value="Payment overdue">Payment overdue</option>
                            <option value="Customer verification required">Customer verification required</option>
                            <option value="Pricing review needed">Pricing review needed</option>
                            <option value="Management approval required">Management approval required</option>
                            <option value="Batch verification needed">Batch verification needed</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Hold Invoice</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
