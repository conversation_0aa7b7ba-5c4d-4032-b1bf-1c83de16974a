# ERP System Backup - COMPREHENSIVE_ERP_BACKUP_20250629_015033

## Backup Information
- **Date**: 2025-06-29T01:50:33.058279
- **Type**: Comprehensive ERP System Backup
- **Total Files**: 235
- **Total Size**: 63.22 MB

## Contents
This backup contains:
- Main application file (app.py)
- Database schema (schema.sql)
- Python dependencies (requirements.txt)
- Database file (erp_system.db)
- All HTML templates
- Static files (CSS, JS, images)
- Route modules
- Utility functions
- Instance data

## Restoration Instructions
1. Extract all files to your desired location
2. Install dependencies: `pip install -r requirements.txt`
3. Run the application: `python app.py`
4. Access via browser: http://localhost:5000

## System Requirements
- Python 3.7+
- Flask 2.3.3
- SQLite3
- See requirements.txt for complete dependencies

## Support
For support, contact the development team.
