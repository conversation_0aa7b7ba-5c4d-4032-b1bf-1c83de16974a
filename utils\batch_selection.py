"""
Batch Selection Utility
This module provides functions for selecting product batches using FIFO, LIFO, or manual selection
"""

import sqlite3
from datetime import datetime

def get_available_batches(db, product_id, warehouse_id=None):
    """
    Get all available batches for a product, optionally filtered by warehouse

    Args:
        db: Database connection
        product_id: Product ID to find batches for
        warehouse_id: Optional warehouse ID to filter by

    Returns:
        List of batch dictionaries with batch details
    """
    query = '''
        SELECT
            i.id, i.inventory_id, i.product_id, i.batch_number,
            i.manufacturing_date, i.expiry_date, i.stock_quantity,
            i.allocated_quantity, i.warehouse_id, w.name as warehouse_name,
            p.name as product_name
        FROM inventory i
        JOIN products p ON i.product_id = p.product_id
        JOIN warehouses w ON i.warehouse_id = w.warehouse_id
        WHERE i.product_id = ?
        AND i.stock_quantity > i.allocated_quantity
    '''

    params = [product_id]

    if warehouse_id:
        query += ' AND i.warehouse_id = ?'
        params.append(warehouse_id)

    cursor = db.cursor()
    cursor.execute(query, params)
    batches = cursor.fetchall()

    return batches

def select_batch_fifo(db, product_id, quantity_needed, warehouse_id=None):
    """
    Select batches using First-In-First-Out (FIFO) method
    Selects oldest batches first based on manufacturing date

    Args:
        db: Database connection
        product_id: Product ID to find batches for
        quantity_needed: Quantity needed for the order
        warehouse_id: Optional warehouse ID to filter by

    Returns:
        List of selected batch dictionaries with quantities to allocate
    """
    batches = get_available_batches(db, product_id, warehouse_id)

    # Sort by manufacturing date (oldest first)
    batches = sorted(batches, key=lambda x: x['manufacturing_date'] or datetime.max)

    return _allocate_from_batches(batches, quantity_needed)

def select_batch_lifo(db, product_id, quantity_needed, warehouse_id=None):
    """
    Select batches using Last-In-First-Out (LIFO) method
    Selects newest batches first based on manufacturing date

    Args:
        db: Database connection
        product_id: Product ID to find batches for
        quantity_needed: Quantity needed for the order
        warehouse_id: Optional warehouse ID to filter by

    Returns:
        List of selected batch dictionaries with quantities to allocate
    """
    batches = get_available_batches(db, product_id, warehouse_id)

    # Sort by manufacturing date (newest first)
    batches = sorted(batches, key=lambda x: x['manufacturing_date'] or datetime.min, reverse=True)

    return _allocate_from_batches(batches, quantity_needed)

def select_batch_manual(db, batch_ids, quantities):
    """
    Select batches manually based on provided batch IDs and quantities

    Args:
        db: Database connection
        batch_ids: List of batch IDs to allocate from
        quantities: List of quantities to allocate from each batch

    Returns:
        List of selected batch dictionaries with quantities to allocate
    """
    if len(batch_ids) != len(quantities):
        raise ValueError("Batch IDs and quantities must have the same length")

    selected_batches = []
    cursor = db.cursor()

    for i, batch_id in enumerate(batch_ids):
        quantity = quantities[i]

        if quantity <= 0:
            continue

        cursor.execute('''
            SELECT
                i.id, i.inventory_id, i.product_id, i.batch_number,
                i.manufacturing_date, i.expiry_date, i.stock_quantity,
                i.allocated_quantity, i.warehouse_id, w.name as warehouse_name,
                p.name as product_name
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.id = ?
        ''', (batch_id,))

        batch = cursor.fetchone()

        if not batch:
            continue

        available_quantity = batch['stock_quantity'] - batch['allocated_quantity']

        if available_quantity <= 0:
            continue

        # Limit allocation to available quantity
        allocation_quantity = min(quantity, available_quantity)

        batch_with_allocation = dict(batch)
        batch_with_allocation['allocation_quantity'] = allocation_quantity

        selected_batches.append(batch_with_allocation)

    return selected_batches

def _allocate_from_batches(batches, quantity_needed):
    """
    Helper function to allocate quantity from a list of batches

    Args:
        batches: List of batch dictionaries or sqlite3.Row objects
        quantity_needed: Total quantity needed

    Returns:
        List of selected batch dictionaries with quantities to allocate
    """
    remaining_quantity = quantity_needed
    selected_batches = []

    for batch in batches:
        if remaining_quantity <= 0:
            break

        # Convert to dict if it's a sqlite3.Row
        if not isinstance(batch, dict):
            batch = dict(batch)

        available_quantity = batch['stock_quantity'] - batch['allocated_quantity']

        if available_quantity <= 0:
            continue

        # Determine how much to take from this batch
        allocation_quantity = min(remaining_quantity, available_quantity)
        remaining_quantity -= allocation_quantity

        # Create a copy of the batch with the allocation quantity
        batch_with_allocation = dict(batch)
        batch_with_allocation['allocation_quantity'] = allocation_quantity

        selected_batches.append(batch_with_allocation)

    return selected_batches

def update_inventory_allocations(db, selected_batches):
    """
    Update inventory allocations based on selected batches

    Args:
        db: Database connection
        selected_batches: List of batch dictionaries with allocation quantities

    Returns:
        True if successful, False otherwise
    """
    cursor = db.cursor()

    try:
        for batch in selected_batches:
            cursor.execute('''
                UPDATE inventory
                SET allocated_quantity = allocated_quantity + ?
                WHERE id = ?
            ''', (batch['allocation_quantity'], batch['id']))

        return True
    except Exception as e:
        print(f"Error updating inventory allocations: {e}")
        return False
