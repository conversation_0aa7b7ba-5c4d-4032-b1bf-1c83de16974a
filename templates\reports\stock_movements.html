{% extends 'base.html' %}

{% block title %}Stock Movement Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Stock Movement Report</h4>
                    <div>
                        <button class="btn btn-light" id="printStockMovementsBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Filter Options</h5>
                                </div>
                                <div class="card-body">
                                    <form action="{{ url_for('stock_movement_report') }}" method="get">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="product_id">Product</label>
                                                    <select class="form-control" id="product_id" name="product_id">
                                                        <option value="">All Products</option>
                                                        {% for product in products %}
                                                        <option value="{{ product.product_id }}" {% if filters.product_id == product.product_id %}selected{% endif %}>
                                                            {{ product.name }} ({{ product.strength }})
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="warehouse_id">Warehouse</label>
                                                    <select class="form-control" id="warehouse_id" name="warehouse_id">
                                                        <option value="">All Warehouses</option>
                                                        {% for warehouse in warehouses %}
                                                        <option value="{{ warehouse.warehouse_id }}" {% if filters.warehouse_id == warehouse.warehouse_id %}selected{% endif %}>
                                                            {{ warehouse.name }}
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="movement_type">Movement Type</label>
                                                    <select class="form-control" id="movement_type" name="movement_type">
                                                        <option value="">All Types</option>
                                                        {% for type in movement_types %}
                                                        <option value="{{ type.movement_type }}" {% if filters.movement_type == type.movement_type %}selected{% endif %}>
                                                            {{ type.movement_type }}
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="from_date">From Date</label>
                                                    <input type="date" class="form-control" id="from_date" name="from_date" value="{{ filters.from_date }}">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="to_date">To Date</label>
                                                    <input type="date" class="form-control" id="to_date" name="to_date" value="{{ filters.to_date }}">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="user">User</label>
                                                    <select class="form-control" id="user" name="user">
                                                        <option value="">All Users</option>
                                                        {% for user_item in users %}
                                                        <option value="{{ user_item.moved_by }}" {% if filters.user == user_item.moved_by %}selected{% endif %}>
                                                            {{ user_item.moved_by }}
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3 d-flex align-items-end">
                                                <div class="form-group w-100">
                                                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Movement ID</th>
                                    <th>Date</th>
                                    <th>Product</th>
                                    <th>Batch #</th>
                                    <th>Quantity</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Type</th>
                                    <th>Moved By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if movements %}
                                    {% for movement in movements %}
                                    <tr>
                                        <td>{{ movement.movement_id }}</td>
                                        <td>{{ movement.movement_date }}</td>
                                        <td>{{ movement.product_name }}</td>
                                        <td>{{ movement.batch_number }}</td>
                                        <td>{{ movement.quantity }}</td>
                                        <td>{{ movement.from_warehouse_name }}</td>
                                        <td>{{ movement.to_warehouse_name }}</td>
                                        <td>{{ movement.movement_type }}</td>
                                        <td>{{ movement.moved_by }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center">No movements found matching the filter criteria</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Summary</h5>
                                    <p><strong>Total Movements:</strong> {{ movements|length }}</p>
                                    <p><strong>Movement Types:</strong>
                                        {% set movement_types_list = movements|map(attribute='movement_type')|unique|list %}
                                        {% for type in movement_types_list %}
                                            <span class="badge badge-info">{{ type }}</span>
                                        {% endfor %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body .table-responsive,
        .card-body .table-responsive *,
        .card-body .row:last-child,
        .card-body .row:last-child * {
            visibility: visible;
        }
        .card-body .table-responsive {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form, .card:first-of-type, .filter-options {
            display: none !important;
        }

        /* Add a title for the printed page */
        .table-responsive:before {
            content: "Stock Movement Report";
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            visibility: visible;
        }

        /* Ensure table is visible and properly formatted */
        table {
            width: 100% !important;
            border-collapse: collapse !important;
        }

        th, td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }

        th {
            background-color: #f2f2f2 !important;
            color: black !important;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printStockMovementsBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';

                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .container-fluid, .container-fluid * {
                            visibility: visible;
                        }
                        .container-fluid {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        table {
                            page-break-inside: auto;
                        }
                        tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                    }
                `;

                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);

                // Print after a short delay
                setTimeout(function() {
                    window.print();

                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
