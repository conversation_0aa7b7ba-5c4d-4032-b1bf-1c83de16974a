{% extends 'base.html' %}

{% block title %}Product Inventory Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Product Inventory Report</h4>
                    <div>
                        <button class="btn btn-light" id="printProductInventoryBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product ID</th>
                                    <th>Product Name</th>
                                    <th>Strength</th>
                                    <th>Total Quantity</th>
                                    <th>Locations</th>
                                    <th>Batches</th>
                                    <th>Earliest Expiry</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if product_summary %}
                                    {% for product in product_summary %}
                                    <tr>
                                        <td>{{ product.product_id }}</td>
                                        <td>{{ product.name }}</td>
                                        <td>{{ product.strength }}</td>
                                        <td>{{ product.total_quantity }} {{ product.unit_of_measure }}</td>
                                        <td>{{ product.location_count }}</td>
                                        <td>{{ product.batch_count }}</td>
                                        <td>{{ product.earliest_expiry }}</td>
                                        <td>
                                            <a href="{{ url_for('product_inventory_report', product_id=product.product_id) }}" class="btn btn-sm btn-info">Details</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">No products found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    {% if product_details %}
                    <div class="row mt-5">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Detailed Inventory for {{ product_details.name }} ({{ product_details.strength }})</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Product ID:</th>
                                                    <td>{{ product_details.product_id }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Name:</th>
                                                    <td>{{ product_details.name }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Strength:</th>
                                                    <td>{{ product_details.strength }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Manufacturer:</th>
                                                    <td>{{ product_details.manufacturer }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Category:</th>
                                                    <td>{{ product_details.category }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Unit of Measure:</th>
                                                    <td>{{ product_details.unit_of_measure }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Total Quantity:</th>
                                                    <td>{{ product_details.total_quantity }} {{ product_details.unit_of_measure }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Quantity by Location</h5>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Warehouse</th>
                                                            <th>Quantity</th>
                                                            <th>Percentage</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for location in location_breakdown %}
                                                        <tr>
                                                            <td>{{ location.warehouse_name }}</td>
                                                            <td>{{ location.quantity }} {{ product_details.unit_of_measure }}</td>
                                                            <td>
                                                                {% if product_details.total_quantity > 0 %}
                                                                {{ ((location.quantity / product_details.total_quantity) * 100)|round(1) }}%
                                                                <div class="progress">
                                                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ ((location.quantity / product_details.total_quantity) * 100)|round(1) }}%"></div>
                                                                </div>
                                                                {% else %}
                                                                0%
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <h5 class="mt-4">Batch Details</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Batch #</th>
                                                    <th>Warehouse</th>
                                                    <th>Quantity</th>
                                                    <th>Expiry Date</th>
                                                    <th>Days Until Expiry</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for batch in batch_details %}
                                                <tr>
                                                    <td>{{ batch.batch_number }}</td>
                                                    <td>{{ batch.warehouse_name }}</td>
                                                    <td>{{ batch.stock_quantity }} {{ product_details.unit_of_measure }}</td>
                                                    <td>{{ batch.expiry_date }}</td>
                                                    <td>{{ batch.days_until_expiry|int }}</td>
                                                    <td>
                                                        {% if batch.days_until_expiry <= 30 %}
                                                        <span class="badge badge-danger">Critical</span>
                                                        {% elif batch.days_until_expiry <= 90 %}
                                                        <span class="badge badge-warning">Warning</span>
                                                        {% else %}
                                                        <span class="badge badge-success">OK</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 20px;
        }
        .card-header, .btn, .navbar, .sidebar, .footer {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .actions-column {
            display: none !important;
        }
        table {
            width: 100% !important;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printProductInventoryBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';

                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .container-fluid, .container-fluid * {
                            visibility: visible;
                        }
                        .container-fluid {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        table {
                            page-break-inside: auto;
                        }
                        tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                    }
                `;

                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);

                // Print after a short delay
                setTimeout(function() {
                    window.print();

                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
