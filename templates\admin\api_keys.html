{% extends "base.html" %}

{% block title %}API Key Management - Medivent ERP{% endblock %}

{% block content %}
<style>
/* Modern 2025 API Keys UI */
.api-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.api-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.api-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4e73df, #224abe, #36b9cc);
}

.api-title {
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.modern-api-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modern-api-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.api-key-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.api-key-card.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1cc88a, #13855c);
    border-radius: 20px 20px 0 0;
}

.api-key-card.inactive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e74a3b, #c0392b);
    border-radius: 20px 20px 0 0;
}

.modern-btn-api {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
}

.modern-btn-api:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.modern-btn-success:hover {
    box-shadow: 0 15px 30px rgba(28, 200, 138, 0.4);
}

.modern-btn-danger {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    box-shadow: 0 10px 20px rgba(231, 74, 59, 0.3);
}

.modern-btn-danger:hover {
    box-shadow: 0 15px 30px rgba(231, 74, 59, 0.4);
}

.api-input {
    border: 2px solid #e3f2fd;
    border-radius: 15px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.api-input:focus {
    border-color: #4e73df;
    box-shadow: 0 0 20px rgba(78, 115, 223, 0.2);
    background: white;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-active {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    color: white;
}

.status-inactive {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
}

@media (max-width: 768px) {
    .api-title {
        font-size: 2rem;
    }
}
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(40,167,69,0.3);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">🗝️ API Key Management</h2>
                    <p class="mb-0">Manage Google Maps and other API integrations</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Maps API Key Management -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card api-key-card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">🗺️ Google Maps API Key</h4>
                    <small>Required for route calculation, location tracking, and mapping services</small>
                </div>
                <div class="card-body">
                    {% set google_maps_key = api_keys|selectattr('service_name', 'equalto', 'google_maps')|first %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="google_maps_api_key">Google Maps API Key:</label>
                                <div class="input-group">
                                    <input type="password" class="form-control api-key-input" 
                                           id="google_maps_api_key" 
                                           placeholder="Enter your Google Maps API key (AIza...)"
                                           value="{{ google_maps_key.api_key if google_maps_key else '' }}">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="toggleApiKeyVisibility('google_maps_api_key')">
                                            <i class="fas fa-eye" id="google_maps_api_key_icon"></i>
                                        </button>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    Get your API key from <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a>
                                </small>
                            </div>
                            
                            <div class="form-group">
                                <label for="google_maps_description">Description:</label>
                                <textarea class="form-control" id="google_maps_description" rows="2" 
                                          placeholder="Optional description for this API key">{{ google_maps_key.description if google_maps_key else 'Google Maps API key for route calculation, location tracking, and mapping services' }}</textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>Status</h5>
                                <div class="mb-3">
                                    {% if google_maps_key and google_maps_key.status == 'active' and google_maps_key.api_key %}
                                        <span class="badge badge-success p-3 status-active">
                                            <i class="fas fa-check-circle"></i> ACTIVE
                                        </span>
                                    {% else %}
                                        <span class="badge badge-danger p-3 status-inactive">
                                            <i class="fas fa-times-circle"></i> INACTIVE
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <div class="btn-group-vertical w-100">
                                    <button type="button" class="btn btn-success mb-2" 
                                            onclick="saveApiKey('google_maps')">
                                        <i class="fas fa-save"></i> Save API Key
                                    </button>
                                    
                                    <button type="button" class="btn test-button mb-2" 
                                            onclick="testApiKey('google_maps')" id="test_google_maps_btn">
                                        <i class="fas fa-vial"></i> Test API Key
                                    </button>
                                    
                                    <button type="button" class="btn btn-warning mb-2" 
                                            onclick="clearApiKey('google_maps')">
                                        <i class="fas fa-eraser"></i> Clear
                                    </button>
                                    
                                    <button type="button" class="btn btn-danger" 
                                            onclick="deleteApiKey('google_maps')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Usage Statistics -->
                    {% set google_usage = usage_stats|selectattr('service_name', 'equalto', 'google_maps')|first %}
                    {% if google_usage %}
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-chart-bar"></i> Usage Statistics</h6>
                                <p class="mb-0">
                                    <strong>Total API Calls:</strong> {{ google_usage.total_calls }}<br>
                                    <strong>Last Used:</strong> {{ google_usage.last_used|format_datetime if google_usage.last_used else 'Never' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Map Settings -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">⚙️ Map Configuration Settings</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for setting in map_settings %}
                        <div class="col-md-6 mb-3">
                            <div class="form-group">
                                <label for="setting_{{ setting.setting_key }}">{{ setting.description }}:</label>
                                {% if setting.setting_type == 'boolean' %}
                                    <select class="form-control" id="setting_{{ setting.setting_key }}" 
                                            data-setting-key="{{ setting.setting_key }}">
                                        <option value="true" {% if setting.setting_value == 'true' %}selected{% endif %}>Enabled</option>
                                        <option value="false" {% if setting.setting_value == 'false' %}selected{% endif %}>Disabled</option>
                                    </select>
                                {% elif setting.setting_type == 'float' %}
                                    <input type="number" step="0.000001" class="form-control" 
                                           id="setting_{{ setting.setting_key }}" 
                                           data-setting-key="{{ setting.setting_key }}"
                                           value="{{ setting.setting_value }}"
                                           placeholder="Enter {{ setting.setting_key.replace('_', ' ') }}">
                                {% elif setting.setting_type == 'integer' %}
                                    <input type="number" class="form-control" 
                                           id="setting_{{ setting.setting_key }}" 
                                           data-setting-key="{{ setting.setting_key }}"
                                           value="{{ setting.setting_value }}"
                                           placeholder="Enter {{ setting.setting_key.replace('_', ' ') }}">
                                {% else %}
                                    <input type="text" class="form-control" 
                                           id="setting_{{ setting.setting_key }}" 
                                           data-setting-key="{{ setting.setting_key }}"
                                           value="{{ setting.setting_value }}"
                                           placeholder="Enter {{ setting.setting_key.replace('_', ' ') }}">
                                {% endif %}
                                <small class="form-text text-muted">{{ setting.description }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-primary btn-lg" onclick="saveMapSettings()">
                            <i class="fas fa-save"></i> Save Map Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key Setup Instructions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">📋 Google Maps API Setup Instructions</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>🔧 Setup Steps:</h5>
                            <ol>
                                <li>Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                                <li>Create a new project or select existing</li>
                                <li>Enable these APIs:
                                    <ul>
                                        <li>Maps JavaScript API</li>
                                        <li>Directions API</li>
                                        <li>Geocoding API</li>
                                        <li>Places API (optional)</li>
                                    </ul>
                                </li>
                                <li>Create API Key in Credentials</li>
                                <li>Copy and paste the key above</li>
                                <li>Click "Test API Key" to verify</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>💰 Pricing Information:</h5>
                            <ul>
                                <li><strong>Maps JavaScript API:</strong> $7 per 1,000 loads</li>
                                <li><strong>Directions API:</strong> $5 per 1,000 requests</li>
                                <li><strong>Geocoding API:</strong> $5 per 1,000 requests</li>
                                <li><strong>Free Tier:</strong> $200 credit per month</li>
                            </ul>
                            
                            <div class="alert alert-warning mt-3">
                                <h6><i class="fas fa-shield-alt"></i> Security Tip:</h6>
                                <p class="mb-0">Restrict your API key to your domain only in Google Cloud Console for security.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">⚡ Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('rider_dashboard') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-motorcycle"></i> Test Rider Dashboard
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('admin_rider_tracking') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-map-marker-alt"></i> Test Admin Tracking
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-success btn-block" onclick="refreshPage()">
                                <i class="fas fa-sync"></i> Refresh Status
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-home"></i> Main Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Modal -->
<div class="modal fade" id="resultModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" id="resultModalHeader">
                <h4 class="modal-title" id="resultModalTitle">Result</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- Result content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function toggleApiKeyVisibility(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '_icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function saveApiKey(serviceName) {
    const apiKey = document.getElementById(serviceName + '_api_key').value.trim();
    const description = document.getElementById(serviceName + '_description').value.trim();
    
    const formData = new FormData();
    formData.append('service_name', serviceName);
    formData.append('api_key', apiKey);
    formData.append('description', description);
    formData.append('action', 'save');
    
    fetch('/admin/api_keys/save', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResult('success', 'Success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showResult('error', 'Error', data.message);
        }
    })
    .catch(error => {
        showResult('error', 'Error', 'Failed to save API key');
    });
}

function testApiKey(serviceName) {
    const apiKey = document.getElementById(serviceName + '_api_key').value.trim();
    
    if (!apiKey) {
        showResult('error', 'Error', 'Please enter an API key first');
        return;
    }
    
    const testBtn = document.getElementById('test_' + serviceName + '_btn');
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    testBtn.disabled = true;
    
    const formData = new FormData();
    formData.append('service_name', serviceName);
    formData.append('api_key', apiKey);
    
    fetch('/admin/api_keys/test', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        testBtn.innerHTML = '<i class="fas fa-vial"></i> Test API Key';
        testBtn.disabled = false;
        
        if (data.success) {
            showResult('success', 'Test Successful', data.message);
        } else {
            showResult('error', 'Test Failed', data.message);
        }
    })
    .catch(error => {
        testBtn.innerHTML = '<i class="fas fa-vial"></i> Test API Key';
        testBtn.disabled = false;
        showResult('error', 'Test Error', 'Failed to test API key');
    });
}

function clearApiKey(serviceName) {
    if (confirm('Are you sure you want to clear this API key?')) {
        document.getElementById(serviceName + '_api_key').value = '';
        saveApiKey(serviceName);
    }
}

function deleteApiKey(serviceName) {
    if (confirm('Are you sure you want to delete this API key? This will disable all related functionality.')) {
        const formData = new FormData();
        formData.append('service_name', serviceName);
        formData.append('action', 'delete');
        
        fetch('/admin/api_keys/save', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult('success', 'Deleted', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showResult('error', 'Error', data.message);
            }
        })
        .catch(error => {
            showResult('error', 'Error', 'Failed to delete API key');
        });
    }
}

function saveMapSettings() {
    const settings = {};
    
    document.querySelectorAll('[data-setting-key]').forEach(element => {
        const key = element.getAttribute('data-setting-key');
        settings[key] = element.value;
    });
    
    fetch('/admin/settings/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResult('success', 'Settings Saved', data.message);
        } else {
            showResult('error', 'Error', data.message);
        }
    })
    .catch(error => {
        showResult('error', 'Error', 'Failed to save settings');
    });
}

function showResult(type, title, message) {
    const modal = document.getElementById('resultModal');
    const header = document.getElementById('resultModalHeader');
    const titleEl = document.getElementById('resultModalTitle');
    const body = document.getElementById('resultModalBody');
    
    // Set colors based on type
    if (type === 'success') {
        header.className = 'modal-header bg-success text-white';
        titleEl.innerHTML = '<i class="fas fa-check-circle"></i> ' + title;
    } else {
        header.className = 'modal-header bg-danger text-white';
        titleEl.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ' + title;
    }
    
    body.innerHTML = '<p>' + message + '</p>';
    
    $('#resultModal').modal('show');
}

function refreshPage() {
    location.reload();
}
</script>
{% endblock %}
