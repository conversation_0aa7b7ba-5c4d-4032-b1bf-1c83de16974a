{% extends "base.html" %}

{% block title %}Division Analytics - Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-chart-line"></i> Division Analytics
                    </h2>
                    <p class="text-muted mb-0">Comprehensive analytics and performance insights for all divisions</p>
                </div>
                <div>
                    <a href="{{ url_for('divisions_list') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Divisions
                    </a>
                    <a href="{{ url_for('divisions_export') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-download"></i> Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if divisions and divisions|length > 0 %}
    <!-- Analytics Charts -->
    <div class="row mb-4">
        <!-- Revenue Performance Chart -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i> Revenue Performance Comparison
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="revenueComparisonChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Performance Distribution -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i> Performance Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="performanceDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Progress Overview Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-area"></i> Target vs Achievement
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="targetAchievementChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Employee Distribution -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users"></i> Employee Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="employeeDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Growth Trend -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line"></i> Monthly Growth Trend
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyGrowthChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table"></i> Detailed Analytics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="analyticsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Division</th>
                                    <th>Target Revenue</th>
                                    <th>Achieved Revenue</th>
                                    <th>Progress %</th>
                                    <th>Employees</th>
                                    <th>Revenue per Employee</th>
                                    <th>Status</th>
                                    <th>Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for division in divisions %}
                                {% set revenue_per_employee = (division.achieved_revenue / division.employee_count) if division.employee_count > 0 else 0 %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                {{ division.name[0] if division.name else 'D' }}
                                            </div>
                                            <strong>{{ division.name }}</strong>
                                        </div>
                                    </td>
                                    <td>Rs.{{ "{:,.0f}".format(division.target_revenue) if division.target_revenue else "0" }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(division.achieved_revenue) if division.achieved_revenue else "0" }}</td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar 
                                                {% if division.progress_percentage >= 90 %}bg-success
                                                {% elif division.progress_percentage >= 70 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ division.progress_percentage }}%">
                                            </div>
                                        </div>
                                        <small>{{ division.progress_percentage }}%</small>
                                    </td>
                                    <td>{{ division.employee_count or 0 }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(revenue_per_employee) }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if division.status == 'active' else 'secondary' }}">
                                            {{ division.status.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if division.progress_percentage >= 90 %}
                                            <span class="badge bg-success">Excellent</span>
                                        {% elif division.progress_percentage >= 70 %}
                                            <span class="badge bg-warning">Good</span>
                                        {% else %}
                                            <span class="badge bg-danger">Needs Improvement</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- Empty State -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-chart-line fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted mb-3">No Analytics Data Available</h4>
                    <p class="text-muted mb-4">No divisions have been created yet. Create divisions to view comprehensive analytics and performance insights.</p>
                    <a href="{{ url_for('divisions_list') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus"></i> Create First Division
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Chart.js configuration and data
document.addEventListener('DOMContentLoaded', function() {
    {% if divisions and divisions|length > 0 %}
    
    // Prepare chart data
    const divisionNames = {{ chart_data.division_names|tojson }};
    const targetRevenues = {{ chart_data.target_revenues|tojson }};
    const achievedRevenues = {{ chart_data.achieved_revenues|tojson }};
    const progressPercentages = {{ chart_data.progress_percentages|tojson }};
    const employeeCounts = {{ chart_data.employee_counts|tojson }};
    
    // Performance distribution data
    const performanceData = {{ performance_data|tojson }};
    
    // Monthly data
    const monthlyData = {{ monthly_data|tojson }};

    // 1. Revenue Comparison Chart
    const revenueCtx = document.getElementById('revenueComparisonChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'bar',
        data: {
            labels: divisionNames,
            datasets: [{
                label: 'Target Revenue',
                data: targetRevenues,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }, {
                label: 'Achieved Revenue',
                data: achievedRevenues,
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rs.' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': Rs.' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // 2. Performance Distribution Chart
    const performanceCtx = document.getElementById('performanceDistributionChart').getContext('2d');
    new Chart(performanceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Excellent (≥90%)', 'Good (70-89%)', 'Needs Improvement (<70%)'],
            datasets: [{
                data: [performanceData.excellent, performanceData.good, performanceData.needs_improvement],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 3. Target vs Achievement Chart
    const targetCtx = document.getElementById('targetAchievementChart').getContext('2d');
    new Chart(targetCtx, {
        type: 'radar',
        data: {
            labels: divisionNames,
            datasets: [{
                label: 'Progress %',
                data: progressPercentages,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(255, 99, 132, 1)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });

    // 4. Employee Distribution Chart
    const employeeCtx = document.getElementById('employeeDistributionChart').getContext('2d');
    new Chart(employeeCtx, {
        type: 'pie',
        data: {
            labels: divisionNames,
            datasets: [{
                data: employeeCounts,
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + ' employees';
                        }
                    }
                }
            }
        }
    });

    // 5. Monthly Growth Chart
    const monthlyCtx = document.getElementById('monthlyGrowthChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: monthlyData.map(d => d.month),
            datasets: [{
                label: 'Total Revenue',
                data: monthlyData.map(d => d.total_revenue),
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Division Count',
                data: monthlyData.map(d => d.divisions_count),
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                borderWidth: 3,
                fill: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    ticks: {
                        callback: function(value) {
                            return 'Rs.' + (value / 1000000).toFixed(1) + 'M';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' divisions';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'Revenue: Rs.' + context.parsed.y.toLocaleString();
                            } else {
                                return 'Divisions: ' + context.parsed.y;
                            }
                        }
                    }
                }
            }
        }
    });

    // Initialize DataTable
    $('#analyticsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[3, "desc"]], // Sort by progress percentage
        columnDefs: [
            { orderable: false, targets: [7] } // Disable sorting for performance column
        ]
    });

    {% endif %}
});
</script>
{% endblock %}
