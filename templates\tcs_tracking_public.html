<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCS Express Tracking - Public</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .tracking-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            color: #333;
            padding: 40px 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .search-section h1 {
            color: #D40511;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .search-section p {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        
        .search-form {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0;
            max-width: 500px;
            margin: 0 auto;
            margin-bottom: 20px;
        }
        
        .search-input {
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 8px 0 0 8px;
            font-size: 16px;
            width: 300px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #D40511;
        }
        
        .search-btn {
            background: #D40511;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 0 8px 8px 0;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .search-btn:hover {
            background: #b8040f;
            transform: translateY(-2px);
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .bulk-toggle {
            margin-top: 20px;
        }

        .bulk-toggle button {
            background: transparent;
            border: 2px solid #D40511;
            color: #D40511;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .bulk-toggle button:hover {
            background: #D40511;
            color: white;
        }

        .bulk-section {
            display: none;
            margin-top: 20px;
        }

        .bulk-input {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
        }

        .bulk-input:focus {
            border-color: #D40511;
            outline: none;
        }

        .bulk-help {
            margin-top: 10px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .results-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            display: none;
        }
        
        .shipment-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .detail-card {
            background: #f8f8f8;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #D40511;
        }
        
        .detail-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .detail-item {
            display: flex;
            margin-bottom: 10px;
        }
        
        .detail-label {
            color: #666;
            min-width: 150px;
            font-weight: 500;
        }
        
        .detail-value {
            color: #333;
            font-weight: bold;
        }
        
        .tracking-number {
            color: #D40511 !important;
            font-size: 18px;
        }
        
        .status-delivered {
            color: #28a745;
            font-weight: bold;
        }

        .status-in-transit {
            color: #ffc107;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .track-history {
            margin-top: 30px;
        }
        
        .track-history h2 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .history-table th {
            background: #f1f1f1;
            padding: 15px;
            text-align: left;
            font-weight: bold;
            color: #333;
        }
        
        .history-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .history-table tr:hover {
            background: #f9f9f9;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .error {
            background: rgba(248, 215, 218, 0.95);
            backdrop-filter: blur(20px);
            color: #721c24;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .error i {
            margin-right: 10px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #D40511;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .bulk-results {
            display: none;
        }

        .bulk-result-item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .bulk-result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .bulk-result-header h3 {
            color: #D40511;
            margin: 0;
        }

        .bulk-result-status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .progress-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #D40511, #b8040f);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            color: #666;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .shipment-details {
                grid-template-columns: 1fr;
            }
            
            .search-form {
                flex-direction: column;
                gap: 10px;
            }
            
            .search-input {
                border-radius: 8px;
                width: 100%;
                max-width: 300px;
            }
            
            .search-btn {
                border-radius: 8px;
                width: 100%;
                max-width: 300px;
            }

            .search-section h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="tracking-container">
        <div class="search-section">
            <h1><i class="fas fa-truck"></i> TCS Express Tracking</h1>
            <p>Enter your tracking number to get real-time shipment information</p>
            
            <!-- Single Tracking Form -->
            <form class="search-form" id="trackingForm">
                <input type="text" 
                       class="search-input" 
                       id="trackingNumber" 
                       placeholder="Enter Tracking Number" 
                       required>
                <button type="submit" class="search-btn" id="trackBtn">
                    <i class="fas fa-search"></i> Track Shipment
                </button>
            </form>

            <!-- Bulk Toggle -->
            <div class="bulk-toggle">
                <button type="button" id="bulkToggleBtn">
                    <i class="fas fa-list"></i> Track Multiple Shipments
                </button>
            </div>

            <!-- Bulk Tracking Section -->
            <div class="bulk-section" id="bulkSection">
                <textarea class="bulk-input" 
                          id="bulkTrackingNumbers" 
                          placeholder="Enter multiple tracking numbers (one per line or comma-separated)&#10;Example:&#10;31442084039&#10;31442083394&#10;31442083525"></textarea>
                <div class="bulk-help">
                    <i class="fas fa-info-circle"></i> 
                    Enter tracking numbers separated by commas or on separate lines
                </div>
                <div style="margin-top: 15px;">
                    <button type="button" class="search-btn" id="bulkTrackBtn">
                        <i class="fas fa-search"></i> Track All Shipments
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Progress Container -->
        <div class="progress-container" id="progressContainer">
            <div class="progress-text" id="progressText">Processing tracking numbers...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressCount">0 / 0</div>
        </div>

        <!-- Loading Section -->
        <div class="loading" id="loadingSection">
            <div class="spinner"></div>
            <p>Fetching tracking information...</p>
        </div>
        
        <!-- Error Section -->
        <div class="error" id="errorSection">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorMessage"></span>
        </div>
        
        <!-- Single Result Section -->
        <div class="results-section" id="resultsSection">
            <div class="shipment-details" id="shipmentDetails">
                <!-- Shipment details will be populated here -->
            </div>
            
            <div class="track-history">
                <h2>Track History</h2>
                <table class="history-table" id="historyTable">
                    <thead>
                        <tr>
                            <th>Date Time</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody">
                        <!-- History will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Bulk Results Section -->
        <div class="bulk-results" id="bulkResults">
            <!-- Bulk results will be populated here -->
        </div>
    </div>

    <script>
        // Global variables
        let isBulkMode = false;
        let bulkTrackingInProgress = false;

        // Event listeners
        document.getElementById('trackingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const trackingNumber = document.getElementById('trackingNumber').value.trim();
            if (!trackingNumber) {
                showError('Please enter a tracking number');
                return;
            }
            
            trackSingleShipment(trackingNumber);
        });

        document.getElementById('bulkToggleBtn').addEventListener('click', function() {
            toggleBulkMode();
        });

        document.getElementById('bulkTrackBtn').addEventListener('click', function() {
            const bulkInput = document.getElementById('bulkTrackingNumbers').value.trim();
            if (!bulkInput) {
                showError('Please enter tracking numbers for bulk tracking');
                return;
            }
            
            trackBulkShipments(bulkInput);
        });

        // UI Functions
        function toggleBulkMode() {
            isBulkMode = !isBulkMode;
            const bulkSection = document.getElementById('bulkSection');
            const bulkToggleBtn = document.getElementById('bulkToggleBtn');
            
            if (isBulkMode) {
                bulkSection.style.display = 'block';
                bulkToggleBtn.innerHTML = '<i class="fas fa-user"></i> Track Single Shipment';
                hideAllResults();
            } else {
                bulkSection.style.display = 'none';
                bulkToggleBtn.innerHTML = '<i class="fas fa-list"></i> Track Multiple Shipments';
                hideAllResults();
            }
        }

        function showLoading() {
            document.getElementById('loadingSection').style.display = 'block';
            hideResults();
            hideError();
            hideBulkResults();
        }

        function hideLoading() {
            document.getElementById('loadingSection').style.display = 'none';
        }

        function showError(message) {
            hideLoading();
            hideProgress();
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorSection').style.display = 'block';
            hideResults();
            hideBulkResults();
        }

        function hideError() {
            document.getElementById('errorSection').style.display = 'none';
        }

        function showResults(data) {
            hideLoading();
            hideError();
            hideBulkResults();
            
            // Populate shipment details
            populateShipmentDetails(data);
            
            // Populate track history
            populateTrackHistory(data.track_history || []);
            
            document.getElementById('resultsSection').style.display = 'block';
        }

        function hideResults() {
            document.getElementById('resultsSection').style.display = 'none';
        }

        function showBulkResults() {
            hideLoading();
            hideError();
            hideResults();
            document.getElementById('bulkResults').style.display = 'block';
        }

        function hideBulkResults() {
            document.getElementById('bulkResults').style.display = 'none';
        }

        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        function updateProgress(current, total, message) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressCount = document.getElementById('progressCount');
            
            const percentage = total > 0 ? (current / total) * 100 : 0;
            progressFill.style.width = percentage + '%';
            progressText.textContent = message;
            progressCount.textContent = `${current} / ${total}`;
        }

        function hideAllResults() {
            hideResults();
            hideBulkResults();
            hideError();
            hideLoading();
            hideProgress();
        }

        // Data population functions
        function populateShipmentDetails(data) {
            const detailsContainer = document.getElementById('shipmentDetails');

            const statusClass = getStatusClass(data.current_status);

            detailsContainer.innerHTML = `
                <div class="detail-card">
                    <h3>Shipment Booking Details</h3>
                    <div class="detail-item">
                        <span class="detail-label">Tracking Number:</span>
                        <span class="detail-value tracking-number">${data.tracking_number}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Agent Reference:</span>
                        <span class="detail-value">${data.agent_reference || 'NA'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Origin:</span>
                        <span class="detail-value">${data.origin}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Destination:</span>
                        <span class="detail-value">${data.destination}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Booking Date:</span>
                        <span class="detail-value">${data.booking_date}</span>
                    </div>
                </div>

                <div class="detail-card">
                    <h3>Shipment Track Summary</h3>
                    <div class="detail-item">
                        <span class="detail-label">Current Status:</span>
                        <span class="detail-value ${statusClass}">${data.current_status}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Delivered On:</span>
                        <span class="detail-value">${data.delivered_on || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Received by:</span>
                        <span class="detail-value">${data.received_by || 'N/A'}</span>
                    </div>
                </div>
            `;
        }

        function populateTrackHistory(history) {
            const tableBody = document.getElementById('historyTableBody');
            tableBody.innerHTML = '';

            if (!history || history.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="2" style="text-align: center; color: #666;">No tracking history available</td>
                `;
                tableBody.appendChild(row);
                return;
            }

            history.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.date_time}</td>
                    <td><strong>${item.status}</strong><br>${item.location || ''}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        function getStatusClass(status) {
            if (!status) return '';

            const statusLower = status.toLowerCase();
            if (statusLower.includes('delivered')) return 'status-delivered';
            if (statusLower.includes('transit') || statusLower.includes('dispatch')) return 'status-in-transit';
            if (statusLower.includes('error') || statusLower.includes('failed')) return 'status-error';
            return 'status-in-transit';
        }

        // Tracking functions
        function trackSingleShipment(trackingNumber) {
            showLoading();

            // Validate tracking number
            if (!validateTrackingNumber(trackingNumber)) {
                showError('Invalid tracking number format. Please enter a valid TCS tracking number.');
                return;
            }

            // Try CORS-enabled API endpoint
            fetch('http://localhost:5001/api/track-tcs-public', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tracking_number: trackingNumber })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showResults(data.data);
                } else {
                    handleTrackingError(data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Network error. Please check your connection and try again.');
            });
        }

        function trackBulkShipments(bulkInput) {
            if (bulkTrackingInProgress) return;

            // Parse tracking numbers
            const trackingNumbers = parseTrackingNumbers(bulkInput);

            if (trackingNumbers.length === 0) {
                showError('No valid tracking numbers found. Please check your input.');
                return;
            }

            if (trackingNumbers.length > 20) {
                showError('Maximum 20 tracking numbers allowed at once.');
                return;
            }

            bulkTrackingInProgress = true;
            hideAllResults();
            showProgress();

            // Clear previous bulk results
            document.getElementById('bulkResults').innerHTML = '';

            // Process tracking numbers
            processBulkTracking(trackingNumbers);
        }

        async function processBulkTracking(trackingNumbers) {
            const results = [];
            const total = trackingNumbers.length;

            updateProgress(0, total, 'Starting bulk tracking...');

            for (let i = 0; i < trackingNumbers.length; i++) {
                const trackingNumber = trackingNumbers[i];
                updateProgress(i, total, `Processing ${trackingNumber}...`);

                try {
                    const result = await trackSingleNumber(trackingNumber);
                    results.push({
                        trackingNumber: trackingNumber,
                        success: true,
                        data: result
                    });
                } catch (error) {
                    results.push({
                        trackingNumber: trackingNumber,
                        success: false,
                        error: error.message
                    });
                }

                // Add delay between requests
                if (i < trackingNumbers.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            updateProgress(total, total, 'Bulk tracking complete!');

            // Display results
            setTimeout(() => {
                hideProgress();
                displayBulkResults(results);
                bulkTrackingInProgress = false;
            }, 1000);
        }

        async function trackSingleNumber(trackingNumber) {
            const response = await fetch('http://localhost:5001/api/track-tcs-public', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tracking_number: trackingNumber })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.error || 'Tracking failed');
            }
        }

        function displayBulkResults(results) {
            const bulkResultsContainer = document.getElementById('bulkResults');

            let successCount = 0;
            let failureCount = 0;

            let html = '<h2 style="text-align: center; color: #333; margin-bottom: 30px;">Bulk Tracking Results</h2>';

            results.forEach(result => {
                if (result.success) {
                    successCount++;
                    html += createBulkResultItem(result.trackingNumber, result.data, true);
                } else {
                    failureCount++;
                    html += createBulkResultItem(result.trackingNumber, null, false, result.error);
                }
            });

            // Add summary
            const summary = `
                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-radius: 15px; padding: 20px; margin-bottom: 20px; text-align: center; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
                    <h3 style="color: #333; margin-bottom: 15px;">Summary</h3>
                    <div style="display: flex; justify-content: center; gap: 30px;">
                        <div><strong style="color: #28a745;">${successCount}</strong> Successful</div>
                        <div><strong style="color: #dc3545;">${failureCount}</strong> Failed</div>
                        <div><strong style="color: #333;">${results.length}</strong> Total</div>
                    </div>
                </div>
            `;

            bulkResultsContainer.innerHTML = summary + html;
            showBulkResults();
        }

        function createBulkResultItem(trackingNumber, data, success, error = null) {
            if (success && data) {
                const statusClass = getStatusClass(data.current_status);
                return `
                    <div class="bulk-result-item">
                        <div class="bulk-result-header">
                            <h3>${trackingNumber}</h3>
                            <span class="bulk-result-status status-success">Success</span>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div><strong>Status:</strong> <span class="${statusClass}">${data.current_status}</span></div>
                            <div><strong>Origin:</strong> ${data.origin}</div>
                            <div><strong>Destination:</strong> ${data.destination}</div>
                            <div><strong>Booking Date:</strong> ${data.booking_date}</div>
                        </div>
                        ${data.track_history && data.track_history.length > 0 ?
                            `<div style="margin-top: 15px;"><strong>Latest Update:</strong> ${data.track_history[0].status} (${data.track_history[0].date_time})</div>` :
                            ''
                        }
                    </div>
                `;
            } else {
                return `
                    <div class="bulk-result-item">
                        <div class="bulk-result-header">
                            <h3>${trackingNumber}</h3>
                            <span class="bulk-result-status status-failed">Failed</span>
                        </div>
                        <div style="color: #dc3545;">
                            <i class="fas fa-exclamation-triangle"></i> ${error || 'Tracking failed'}
                        </div>
                    </div>
                `;
            }
        }

        // Utility functions
        function validateTrackingNumber(trackingNumber) {
            if (!trackingNumber || typeof trackingNumber !== 'string') return false;

            const cleaned = trackingNumber.trim();
            return cleaned.length >= 8 && cleaned.length <= 15 && /^\d+$/.test(cleaned);
        }

        function parseTrackingNumbers(input) {
            if (!input) return [];

            // Split by newlines and commas, then clean up
            const numbers = input
                .split(/[\n,]+/)
                .map(num => num.trim())
                .filter(num => num.length > 0)
                .filter(num => validateTrackingNumber(num));

            // Remove duplicates
            return [...new Set(numbers)];
        }

        function handleTrackingError(error) {
            if (!error) {
                showError('Unknown error occurred while tracking shipment.');
                return;
            }

            const errorLower = error.toLowerCase();

            // Enhanced error categorization
            if (error === 'Incorrect Number' ||
                errorLower.includes('not found') ||
                errorLower.includes('invalid') ||
                errorLower.includes('incorrect')) {
                showError('Incorrect Number - Please verify your tracking number and try again.');
            } else if (errorLower.includes('network timeout') ||
                       errorLower.includes('timeout')) {
                showError('Request timeout. The tracking service is slow. Please try again.');
            } else if (errorLower.includes('network') ||
                       errorLower.includes('connection')) {
                showError('Network error. Please check your internet connection and try again.');
            } else if (errorLower.includes('system') ||
                       errorLower.includes('server')) {
                showError('System error. The tracking service is temporarily unavailable. Please try again later.');
            } else if (errorLower.includes('cors') ||
                       errorLower.includes('access-control')) {
                showError('Browser security error. Please ensure the tracking server is running.');
            } else {
                // Show the original error message for debugging
                showError(`Tracking error: ${error}`);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus on tracking input
            document.getElementById('trackingNumber').focus();

            // Add sample data for testing
            const urlParams = new URLSearchParams(window.location.search);
            const sampleNumber = urlParams.get('sample');
            if (sampleNumber) {
                document.getElementById('trackingNumber').value = sampleNumber;
                trackSingleShipment(sampleNumber);
            }
        });
    </script>
</body>
</html>
