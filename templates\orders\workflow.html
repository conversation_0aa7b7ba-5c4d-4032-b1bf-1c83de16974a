{% extends 'base.html' %}

{% block title %}Order Workflow Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Order Workflow Management</h4>
                </div>
                <div class="card-body">
                    <!-- Status Filter Tabs -->
                    <ul class="nav nav-tabs mb-4">
                        <li class="nav-item">
                            <a class="nav-link {% if current_filter == 'all' %}active{% endif %}" href="{{ url_for('workflow') }}">All Orders</a>
                        </li>
                        {% for status in statuses %}
                        <li class="nav-item">
                            <a class="nav-link {% if current_filter == status %}active{% endif %}" href="{{ url_for('workflow', status=status) }}">{{ status }}</a>
                        </li>
                        {% endfor %}
                    </ul>

                    <!-- Dashboard Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total Orders</h5>
                                    <h2 class="card-text">{{ orders|length }}</h2>
                                </div>
                            </div>
                        </div>
                        {% for status in statuses %}
                        <div class="col-md-3">
                            <div class="card {% if status == 'Placed' %}bg-warning{% elif status == 'Approved' %}bg-primary{% elif status == 'Processing' %}bg-info{% elif status == 'Ready for Pickup' %}bg-secondary{% elif status == 'Dispatched' %}bg-dark{% elif status == 'Delivered' %}bg-success{% elif status == 'Cancelled' %}bg-danger{% elif status == 'Pending' %}bg-warning{% endif %} text-white">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        {% if status == 'Placed' %}📋 {{ status }}
                                        {% elif status == 'Approved' %}✅ Pending DC
                                        {% elif status == 'Processing' %}📄 Pending Invoice
                                        {% elif status == 'Ready for Pickup' %}📦 Ready to Dispatch
                                        {% elif status == 'Dispatched' %}🚚 {{ status }}
                                        {% elif status == 'Delivered' %}✅ {{ status }}
                                        {% else %}{{ status }}
                                        {% endif %}
                                    </h5>
                                    <h2 class="card-text">{{ orders|selectattr('status', 'equalto', status)|list|length }}</h2>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Search Bar -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('orders') }}" method="get" class="form-inline">
                                <div class="input-group w-100">
                                    <input type="text" name="q" class="form-control" placeholder="Search by Order ID, Customer, or Phone">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID / Invoice #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_id }}</strong>
                                        {% if order.invoice_number %}
                                        <br><small class="text-muted">{{ order.invoice_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.customer_name }}</td>
                                    <td>{{ order.order_date|format_datetime }}</td>
                                    <td>
                                        <span class="badge {% if order.status == 'Placed' %}badge-warning{% elif order.status == 'Approved' %}badge-primary{% elif order.status == 'Processing' %}badge-info{% elif order.status == 'Ready for Pickup' %}badge-secondary{% elif order.status == 'Dispatched' %}badge-dark{% elif order.status == 'Delivered' %}badge-success{% elif order.status == 'Cancelled' %}badge-danger{% elif order.status == 'Pending' %}badge-warning{% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                    <td>{{ order.order_amount|format_currency }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('view_order_history', order_id=order.order_id) }}" class="btn btn-sm btn-info">View</a>

                                            <!-- Step 1: Boss Approval (Only for Placed orders) -->
                                            {% if order.status == 'Placed' and has_permission('order_approve') %}
                                            <form action="{{ url_for('approve_order', order_id=order.order_id) }}" method="post" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i> Boss Approve
                                                </button>
                                            </form>
                                            {% endif %}

                                            <!-- Step 2: Warehouse DC Generation (Only for Approved orders) -->
                                            {% if order.status == 'Approved' and has_permission('challan_generate') %}
                                            <a href="{{ url_for('generate_challan', order_id=order.order_id) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-file-alt"></i> Generate DC
                                            </a>
                                            {% endif %}

                                            <!-- Step 3: View DC (Only after DC is generated) -->
                                            {% if order.status in ['Processing', 'Ready for Pickup', 'Dispatched', 'Delivered'] and has_permission('challan_view') %}
                                            <div class="btn-group">
                                                <a href="{{ url_for('view_challan', order_id=order.order_id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-file-alt"></i> View DC
                                                </a>
                                                <a href="{{ url_for('display_challan', order_id=order.order_id) }}" class="btn btn-sm btn-outline-info" target="_blank">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                            </div>
                                            {% endif %}

                                            <!-- Step 4: Finance Invoice Generation (Only for Processing orders - after DC) -->
                                            {% if order.status == 'Processing' and has_permission('invoice_generate') %}
                                            <a href="{{ url_for('finance_generate_invoice', order_id=order.order_id) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-file-invoice"></i> Generate Invoice
                                            </a>
                                            {% endif %}

                                            <!-- Step 5: View Invoice (Only after invoice is generated) -->
                                            {% if order.status in ['Ready for Pickup', 'Dispatched', 'Delivered'] and order.invoice_number and has_permission('invoice_view') %}
                                            <div class="btn-group">
                                                <a href="{{ url_for('view_invoice', order_id=order.order_id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-file-invoice"></i> View Invoice
                                                </a>
                                                <a href="{{ url_for('display_invoice', order_id=order.order_id) }}" class="btn btn-sm btn-outline-info" target="_blank">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                            </div>
                                            {% endif %}

                                            <!-- Step 6: Warehouse Dispatch (Only after invoice is generated) -->
                                            {% if order.status == 'Ready for Pickup' and has_permission('order_dispatch') %}
                                            <a href="{{ url_for('dispatch_order', order_id=order.order_id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-truck"></i> Dispatch Order
                                            </a>
                                            {% endif %}

                                            {% if order.status == 'Dispatched' and has_permission('order_deliver') %}
                                            <a href="{{ url_for('deliver_order', order_id=order.order_id) }}" class="btn btn-sm btn-success">Deliver</a>
                                            {% endif %}

                                            {% if order.status not in ['Delivered', 'Cancelled'] and has_permission('order_edit') %}
                                            <form action="{{ url_for('update_order', order_id=order.order_id) }}" method="post" class="d-inline">
                                                <input type="hidden" name="customer_name" value="{{ order.customer_name }}">
                                                <input type="hidden" name="customer_phone" value="{{ order.customer_phone }}">
                                                <input type="hidden" name="customer_address" value="{{ order.customer_address }}">
                                                <input type="hidden" name="payment_mode" value="{{ order.payment_method }}">
                                                <input type="hidden" name="sales_agent" value="{{ order.sales_agent }}">
                                                <input type="hidden" name="status" value="Cancelled">
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to cancel this order?')">Cancel</button>
                                            </form>
                                            {% endif %}
                                        </div>


                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
