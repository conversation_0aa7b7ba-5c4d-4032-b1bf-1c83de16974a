"""
Final validation test for TCS tracking system
Tests both terminal and browser functionality for consistency
"""

import requests
import json
import time
import os
from tcs_scraper_demo import track_tcs_demo

def load_sample_numbers():
    """Load tracking numbers from sample.txt"""
    try:
        with open('tcs/sample.txt', 'r') as f:
            lines = f.readlines()
        
        numbers = []
        for line in lines:
            line = line.strip()
            if line.isdigit() and len(line) >= 8:
                numbers.append(line)
        
        return numbers[:10]  # Limit to first 10 for testing
    except FileNotFoundError:
        print("⚠️  sample.txt not found, using hardcoded test numbers")
        return [
            '31442083522', '31442083523', '31442083525', 
            '31442083524', '31442083393', '31442083394',
            '31442084039', '31442084041'
        ]

def test_terminal_functionality(tracking_numbers):
    """Test terminal functionality"""
    print("🖥️  TERMINAL FUNCTIONALITY TEST")
    print("=" * 50)
    
    terminal_results = []
    
    for i, number in enumerate(tracking_numbers, 1):
        print(f"\n📦 Terminal Test {i}/{len(tracking_numbers)}: {number}")
        
        try:
            start_time = time.time()
            result = track_tcs_demo(number, headless=True)
            duration = time.time() - start_time
            
            if result.get('success'):
                print(f"   ✅ SUCCESS ({duration:.1f}s)")
                print(f"   Status: {result.get('current_status')}")
                print(f"   Route: {result.get('origin')} → {result.get('destination')}")
                
                terminal_results.append({
                    'tracking_number': number,
                    'success': True,
                    'status': result.get('current_status'),
                    'origin': result.get('origin'),
                    'destination': result.get('destination'),
                    'duration': duration
                })
            else:
                print(f"   ❌ FAILED ({duration:.1f}s)")
                print(f"   Error: {result.get('error')}")
                
                terminal_results.append({
                    'tracking_number': number,
                    'success': False,
                    'error': result.get('error'),
                    'duration': duration
                })
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
            terminal_results.append({
                'tracking_number': number,
                'success': False,
                'error': str(e),
                'duration': 0
            })
        
        # Brief pause between tests
        time.sleep(1)
    
    return terminal_results

def test_browser_api_functionality(tracking_numbers):
    """Test browser API functionality"""
    print("\n🌐 BROWSER API FUNCTIONALITY TEST")
    print("=" * 50)
    
    # First check if server is running
    try:
        health_response = requests.get('http://localhost:5001/health', timeout=5)
        if health_response.status_code != 200:
            print("❌ CORS server not running on port 5001")
            return []
    except:
        print("❌ CORS server not accessible")
        return []
    
    browser_results = []
    
    for i, number in enumerate(tracking_numbers, 1):
        print(f"\n📦 Browser Test {i}/{len(tracking_numbers)}: {number}")
        
        try:
            start_time = time.time()
            response = requests.post(
                'http://localhost:5001/api/track-tcs-public',
                json={'tracking_number': number},
                headers={
                    'Content-Type': 'application/json',
                    'Origin': 'file://',  # Simulate browser origin
                },
                timeout=60
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    tracking_data = data.get('data', {})
                    print(f"   ✅ SUCCESS ({duration:.1f}s)")
                    print(f"   Status: {tracking_data.get('current_status')}")
                    print(f"   Route: {tracking_data.get('origin')} → {tracking_data.get('destination')}")
                    
                    browser_results.append({
                        'tracking_number': number,
                        'success': True,
                        'status': tracking_data.get('current_status'),
                        'origin': tracking_data.get('origin'),
                        'destination': tracking_data.get('destination'),
                        'duration': duration
                    })
                else:
                    error = data.get('error', 'Unknown error')
                    print(f"   ❌ FAILED ({duration:.1f}s)")
                    print(f"   Error: {error}")
                    
                    browser_results.append({
                        'tracking_number': number,
                        'success': False,
                        'error': error,
                        'duration': duration
                    })
            else:
                print(f"   ❌ HTTP ERROR ({duration:.1f}s)")
                print(f"   Status: {response.status_code}")
                
                browser_results.append({
                    'tracking_number': number,
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'duration': duration
                })
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
            browser_results.append({
                'tracking_number': number,
                'success': False,
                'error': str(e),
                'duration': 0
            })
        
        # Brief pause between tests
        time.sleep(2)
    
    return browser_results

def compare_results(terminal_results, browser_results):
    """Compare terminal and browser results"""
    print("\n🔍 TERMINAL vs BROWSER COMPARISON")
    print("=" * 50)
    
    # Create lookup for easy comparison
    terminal_lookup = {r['tracking_number']: r for r in terminal_results}
    browser_lookup = {r['tracking_number']: r for r in browser_results}
    
    all_numbers = set(terminal_lookup.keys()) | set(browser_lookup.keys())
    
    consistent_results = 0
    inconsistent_results = 0
    
    for number in sorted(all_numbers):
        terminal_result = terminal_lookup.get(number)
        browser_result = browser_lookup.get(number)
        
        print(f"\n📦 {number}:")
        
        if not terminal_result:
            print("   ⚠️  Missing terminal result")
            inconsistent_results += 1
            continue
            
        if not browser_result:
            print("   ⚠️  Missing browser result")
            inconsistent_results += 1
            continue
        
        # Compare success status
        if terminal_result['success'] != browser_result['success']:
            print(f"   ❌ SUCCESS MISMATCH: Terminal={terminal_result['success']}, Browser={browser_result['success']}")
            inconsistent_results += 1
        elif terminal_result['success'] and browser_result['success']:
            # Both successful - compare data
            terminal_status = terminal_result.get('status', '').lower()
            browser_status = browser_result.get('status', '').lower()
            
            if terminal_status == browser_status:
                print(f"   ✅ CONSISTENT: Both successful with status '{terminal_result['status']}'")
                consistent_results += 1
            else:
                print(f"   ⚠️  STATUS DIFFERENCE: Terminal='{terminal_result['status']}', Browser='{browser_result['status']}'")
                print("      (This may be normal due to real-time updates)")
                consistent_results += 1  # Still count as consistent since both succeeded
        else:
            # Both failed
            print(f"   ✅ CONSISTENT: Both failed")
            consistent_results += 1
    
    return consistent_results, inconsistent_results

def generate_report(terminal_results, browser_results, consistent, inconsistent):
    """Generate final validation report"""
    
    terminal_success = len([r for r in terminal_results if r['success']])
    browser_success = len([r for r in browser_results if r['success']])
    
    total_tested = len(terminal_results)
    
    report = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'total_tested': total_tested,
        'terminal_results': {
            'successful': terminal_success,
            'failed': total_tested - terminal_success,
            'success_rate': round((terminal_success / total_tested) * 100, 1) if total_tested > 0 else 0
        },
        'browser_results': {
            'successful': browser_success,
            'failed': total_tested - browser_success,
            'success_rate': round((browser_success / total_tested) * 100, 1) if total_tested > 0 else 0
        },
        'consistency': {
            'consistent': consistent,
            'inconsistent': inconsistent,
            'consistency_rate': round((consistent / (consistent + inconsistent)) * 100, 1) if (consistent + inconsistent) > 0 else 0
        },
        'detailed_results': {
            'terminal': terminal_results,
            'browser': browser_results
        }
    }
    
    # Save report
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    filename = f'tcs_validation_report_{timestamp}.json'
    
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    return report, filename

def print_summary(report):
    """Print validation summary"""
    print("\n" + "=" * 60)
    print("🏁 FINAL VALIDATION SUMMARY")
    print("=" * 60)
    
    print(f"📊 OVERALL STATISTICS:")
    print(f"   Total Numbers Tested: {report['total_tested']}")
    print(f"   Terminal Success Rate: {report['terminal_results']['success_rate']}%")
    print(f"   Browser Success Rate: {report['browser_results']['success_rate']}%")
    print(f"   Consistency Rate: {report['consistency']['consistency_rate']}%")
    
    print(f"\n🎯 VALIDATION RESULTS:")
    
    if report['consistency']['consistency_rate'] >= 90:
        print("   ✅ EXCELLENT: Browser-Terminal consistency is excellent (≥90%)")
    elif report['consistency']['consistency_rate'] >= 75:
        print("   ✅ GOOD: Browser-Terminal consistency is good (≥75%)")
    elif report['consistency']['consistency_rate'] >= 50:
        print("   ⚠️  FAIR: Browser-Terminal consistency needs improvement (≥50%)")
    else:
        print("   ❌ POOR: Browser-Terminal consistency is poor (<50%)")
    
    if report['browser_results']['success_rate'] >= 80:
        print("   ✅ Browser implementation is working well")
    else:
        print("   ⚠️  Browser implementation needs attention")
    
    print(f"\n📋 RECOMMENDATIONS:")
    
    if report['consistency']['consistency_rate'] < 90:
        print("   • Investigate inconsistent results")
        print("   • Check for timing-related differences")
        print("   • Verify error handling consistency")
    
    if report['browser_results']['success_rate'] < report['terminal_results']['success_rate']:
        print("   • Browser implementation may have additional issues")
        print("   • Check CORS configuration")
        print("   • Verify timeout settings")
    
    if report['browser_results']['success_rate'] >= 80 and report['consistency']['consistency_rate'] >= 90:
        print("   ✅ System is ready for production use")
        print("   ✅ Browser-Terminal discrepancy has been resolved")

def main():
    """Main validation function"""
    print("🚀 TCS TRACKING SYSTEM - FINAL VALIDATION")
    print("=" * 60)
    
    # Load test numbers
    tracking_numbers = load_sample_numbers()
    print(f"📦 Testing {len(tracking_numbers)} tracking numbers")
    
    # Test terminal functionality
    terminal_results = test_terminal_functionality(tracking_numbers)
    
    # Test browser API functionality
    browser_results = test_browser_api_functionality(tracking_numbers)
    
    # Compare results
    consistent, inconsistent = compare_results(terminal_results, browser_results)
    
    # Generate report
    report, filename = generate_report(terminal_results, browser_results, consistent, inconsistent)
    
    # Print summary
    print_summary(report)
    
    print(f"\n💾 Detailed report saved to: {filename}")
    print(f"\n🌐 Browser test page: test_browser_terminal_consistency.html")
    print(f"🖥️  Public tracking page: templates/tcs_tracking_public.html")

if __name__ == "__main__":
    main()
