"""
Data Exporter Module
Export functionality for various data formats
"""

import pandas as pd
import xlsxwriter
import csv
import json
from io import BytesIO, StringIO
from datetime import datetime

def export_to_excel(data, filename=None, sheet_name='Data'):
    """Export data to Excel format"""
    try:
        if not filename:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # Create BytesIO object for in-memory file
        output = BytesIO()
        
        # Create workbook and worksheet
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet(sheet_name)
        
        # Add header format
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1
        })
        
        # Write data
        if data and len(data) > 0:
            # Write headers
            headers = list(data[0].keys())
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)
            
            # Write data rows
            for row, record in enumerate(data, 1):
                for col, header in enumerate(headers):
                    worksheet.write(row, col, record.get(header, ''))
        
        workbook.close()
        output.seek(0)
        
        return output, filename
    except Exception as e:
        print(f"Excel export error: {e}")
        return None, None

def export_to_csv(data, filename=None):
    """Export data to CSV format"""
    try:
        if not filename:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        output = StringIO()
        
        if data and len(data) > 0:
            fieldnames = list(data[0].keys())
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        output.seek(0)
        return output, filename
    except Exception as e:
        print(f"CSV export error: {e}")
        return None, None

def export_to_json(data, filename=None):
    """Export data to JSON format"""
    try:
        if not filename:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output = StringIO()
        json.dump(data, output, indent=2, default=str)
        output.seek(0)
        
        return output, filename
    except Exception as e:
        print(f"JSON export error: {e}")
        return None, None

def export_orders_data(db_connection, format_type='excel'):
    """Export orders data in specified format"""
    try:
        query = """
        SELECT 
            o.order_id,
            o.customer_name,
            o.order_date,
            o.order_amount,
            o.status,
            o.payment_status
        FROM orders o
        ORDER BY o.order_date DESC
        """
        
        df = pd.read_sql_query(query, db_connection)
        data = df.to_dict('records')
        
        if format_type == 'excel':
            return export_to_excel(data, 'orders_export.xlsx', 'Orders')
        elif format_type == 'csv':
            return export_to_csv(data, 'orders_export.csv')
        elif format_type == 'json':
            return export_to_json(data, 'orders_export.json')
        else:
            return None, None
    except Exception as e:
        print(f"Orders export error: {e}")
        return None, None

def export_inventory_data(db_connection, format_type='excel'):
    """Export inventory data in specified format"""
    try:
        query = """
        SELECT 
            p.name as product_name,
            i.batch_number,
            i.stock_quantity,
            i.allocated_quantity,
            (i.stock_quantity - i.allocated_quantity) as available_quantity,
            i.expiry_date,
            w.name as warehouse_name
        FROM inventory i
        JOIN products p ON i.product_id = p.product_id
        JOIN warehouses w ON i.warehouse_id = w.warehouse_id
        ORDER BY p.name, i.batch_number
        """
        
        df = pd.read_sql_query(query, db_connection)
        data = df.to_dict('records')
        
        if format_type == 'excel':
            return export_to_excel(data, 'inventory_export.xlsx', 'Inventory')
        elif format_type == 'csv':
            return export_to_csv(data, 'inventory_export.csv')
        elif format_type == 'json':
            return export_to_json(data, 'inventory_export.json')
        else:
            return None, None
    except Exception as e:
        print(f"Inventory export error: {e}")
        return None, None

def export_customers_data(db_connection, format_type='excel'):
    """Export customers data in specified format"""
    try:
        query = """
        SELECT 
            c.customer_id,
            c.name,
            c.email,
            c.phone,
            c.address,
            c.credit_limit,
            c.risk_category
        FROM customers c
        ORDER BY c.name
        """
        
        df = pd.read_sql_query(query, db_connection)
        data = df.to_dict('records')
        
        if format_type == 'excel':
            return export_to_excel(data, 'customers_export.xlsx', 'Customers')
        elif format_type == 'csv':
            return export_to_csv(data, 'customers_export.csv')
        elif format_type == 'json':
            return export_to_json(data, 'customers_export.json')
        else:
            return None, None
    except Exception as e:
        print(f"Customers export error: {e}")
        return None, None
