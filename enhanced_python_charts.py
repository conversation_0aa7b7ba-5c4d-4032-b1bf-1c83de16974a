"""
Enhanced Python Charts Module
Advanced chart generation with enhanced features
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
import io
import base64
from datetime import datetime, timedelta

class EnhancedChartGenerator:
    """Enhanced chart generator with advanced styling and features"""
    
    def __init__(self):
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        self.colors = sns.color_palette("husl", 10)
        
    def create_dashboard_chart(self, chart_type, data, **kwargs):
        """Create dashboard-ready charts"""
        try:
            if chart_type == 'revenue_trend':
                return self.revenue_trend_chart(data, **kwargs)
            elif chart_type == 'product_performance':
                return self.product_performance_chart(data, **kwargs)
            elif chart_type == 'customer_analysis':
                return self.customer_analysis_chart(data, **kwargs)
            elif chart_type == 'inventory_status':
                return self.inventory_status_chart(data, **kwargs)
            elif chart_type == 'financial_overview':
                return self.financial_overview_chart(data, **kwargs)
            else:
                return self.generic_chart(data, **kwargs)
        except Exception as e:
            print(f"Enhanced chart generation error: {e}")
            return None
    
    def revenue_trend_chart(self, data, title="Revenue Trend", **kwargs):
        """Generate enhanced revenue trend chart"""
        try:
            fig, ax = plt.subplots(figsize=(14, 8))
            
            # Prepare data
            df = pd.DataFrame(data)
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date')
            
            # Main trend line
            ax.plot(df['date'], df['revenue'], linewidth=3, marker='o', 
                   markersize=8, color=self.colors[0], label='Revenue')
            
            # Add trend line
            z = np.polyfit(range(len(df)), df['revenue'], 1)
            p = np.poly1d(z)
            ax.plot(df['date'], p(range(len(df))), "--", alpha=0.7, 
                   color=self.colors[1], label='Trend')
            
            # Styling
            ax.set_title(title, fontsize=18, fontweight='bold', pad=20)
            ax.set_xlabel('Date', fontsize=14)
            ax.set_ylabel('Revenue', fontsize=14)
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # Format axes
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.xticks(rotation=45)
            
            # Add annotations for peaks
            max_idx = df['revenue'].idxmax()
            ax.annotate(f'Peak: {df.loc[max_idx, "revenue"]:,.0f}',
                       xy=(df.loc[max_idx, 'date'], df.loc[max_idx, 'revenue']),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Revenue trend chart error: {e}")
            return None
    
    def product_performance_chart(self, data, title="Product Performance", **kwargs):
        """Generate product performance chart"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            df = pd.DataFrame(data)
            
            # Bar chart for sales
            bars = ax1.bar(df['product_name'], df['total_sales'], 
                          color=self.colors[:len(df)])
            ax1.set_title('Sales by Product', fontsize=14, fontweight='bold')
            ax1.set_xlabel('Products', fontsize=12)
            ax1.set_ylabel('Sales Amount', fontsize=12)
            plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # Add value labels
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:,.0f}', ha='center', va='bottom')
            
            # Pie chart for market share
            ax2.pie(df['total_sales'], labels=df['product_name'], autopct='%1.1f%%',
                   colors=self.colors[:len(df)], startangle=90)
            ax2.set_title('Market Share', fontsize=14, fontweight='bold')
            
            plt.suptitle(title, fontsize=18, fontweight='bold')
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Product performance chart error: {e}")
            return None
    
    def customer_analysis_chart(self, data, title="Customer Analysis", **kwargs):
        """Generate customer analysis chart"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            df = pd.DataFrame(data)
            
            # Top customers by revenue
            top_customers = df.nlargest(10, 'total_amount')
            ax1.barh(top_customers['customer_name'], top_customers['total_amount'],
                    color=self.colors[0])
            ax1.set_title('Top 10 Customers by Revenue', fontweight='bold')
            ax1.set_xlabel('Revenue')
            
            # Order frequency
            ax2.scatter(df['order_count'], df['total_amount'], 
                       alpha=0.6, s=60, color=self.colors[1])
            ax2.set_title('Order Frequency vs Revenue', fontweight='bold')
            ax2.set_xlabel('Number of Orders')
            ax2.set_ylabel('Total Revenue')
            
            # Average order value distribution
            ax3.hist(df['avg_order_value'], bins=20, alpha=0.7, color=self.colors[2])
            ax3.set_title('Average Order Value Distribution', fontweight='bold')
            ax3.set_xlabel('Average Order Value')
            ax3.set_ylabel('Frequency')
            
            # Customer segments
            segments = df.groupby(pd.cut(df['total_amount'], bins=5))['customer_name'].count()
            ax4.pie(segments.values, labels=[f'Segment {i+1}' for i in range(len(segments))],
                   autopct='%1.1f%%', colors=self.colors[3:8])
            ax4.set_title('Customer Segments', fontweight='bold')
            
            plt.suptitle(title, fontsize=18, fontweight='bold')
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Customer analysis chart error: {e}")
            return None
    
    def inventory_status_chart(self, data, title="Inventory Status", **kwargs):
        """Generate inventory status chart"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            df = pd.DataFrame(data)
            
            # Stock levels
            ax1.bar(df['product_name'], df['total_stock'], 
                   label='Total Stock', alpha=0.8, color=self.colors[0])
            ax1.bar(df['product_name'], df['available_stock'], 
                   label='Available Stock', alpha=0.8, color=self.colors[1])
            ax1.set_title('Stock Levels by Product', fontweight='bold')
            ax1.set_xlabel('Products')
            ax1.set_ylabel('Quantity')
            ax1.legend()
            plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # Stock utilization
            df['utilization'] = (df['allocated_stock'] / df['total_stock'] * 100).fillna(0)
            colors = ['red' if x > 80 else 'orange' if x > 60 else 'green' for x in df['utilization']]
            ax2.barh(df['product_name'], df['utilization'], color=colors)
            ax2.set_title('Stock Utilization (%)', fontweight='bold')
            ax2.set_xlabel('Utilization Percentage')
            ax2.axvline(x=80, color='red', linestyle='--', alpha=0.7, label='High Utilization')
            ax2.axvline(x=60, color='orange', linestyle='--', alpha=0.7, label='Medium Utilization')
            ax2.legend()
            
            plt.suptitle(title, fontsize=18, fontweight='bold')
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Inventory status chart error: {e}")
            return None
    
    def financial_overview_chart(self, data, title="Financial Overview", **kwargs):
        """Generate financial overview chart"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            # Revenue vs Expenses
            months = data.get('months', [])
            revenue = data.get('revenue', [])
            expenses = data.get('expenses', [])
            
            ax1.plot(months, revenue, marker='o', linewidth=3, label='Revenue', color=self.colors[0])
            ax1.plot(months, expenses, marker='s', linewidth=3, label='Expenses', color=self.colors[1])
            ax1.set_title('Revenue vs Expenses', fontweight='bold')
            ax1.set_xlabel('Month')
            ax1.set_ylabel('Amount')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # Profit margin
            profit = [r - e for r, e in zip(revenue, expenses)]
            ax2.bar(months, profit, color=['green' if p > 0 else 'red' for p in profit])
            ax2.set_title('Monthly Profit', fontweight='bold')
            ax2.set_xlabel('Month')
            ax2.set_ylabel('Profit')
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            
            # Cash flow
            cash_flow = data.get('cash_flow', [])
            ax3.fill_between(months, cash_flow, alpha=0.7, color=self.colors[2])
            ax3.plot(months, cash_flow, linewidth=2, color=self.colors[3])
            ax3.set_title('Cash Flow', fontweight='bold')
            ax3.set_xlabel('Month')
            ax3.set_ylabel('Cash Flow')
            ax3.grid(True, alpha=0.3)
            
            # Financial ratios
            ratios = data.get('ratios', {})
            ratio_names = list(ratios.keys())
            ratio_values = list(ratios.values())
            ax4.barh(ratio_names, ratio_values, color=self.colors[4:4+len(ratio_names)])
            ax4.set_title('Key Financial Ratios', fontweight='bold')
            ax4.set_xlabel('Ratio Value')
            
            plt.suptitle(title, fontsize=18, fontweight='bold')
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Financial overview chart error: {e}")
            return None
    
    def generic_chart(self, data, chart_type='bar', title="Chart", **kwargs):
        """Generate generic chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            if chart_type == 'bar':
                ax.bar(data.get('labels', []), data.get('values', []), 
                      color=self.colors[0])
            elif chart_type == 'line':
                ax.plot(data.get('labels', []), data.get('values', []), 
                       marker='o', linewidth=2, color=self.colors[0])
            
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            return self._convert_to_base64(fig)
        except Exception as e:
            print(f"Generic chart error: {e}")
            return None
    
    def _convert_to_base64(self, fig):
        """Convert matplotlib figure to base64 string"""
        try:
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight', dpi=150)
            img.seek(0)
            plot_url = base64.b64encode(img.getvalue()).decode()
            plt.close(fig)
            return plot_url
        except Exception as e:
            print(f"Base64 conversion error: {e}")
            plt.close(fig)
            return None
