{% extends 'base.html' %}

{% block title %}Batch Movements{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Batch Movements</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> View All Batches
                            </a>
                            <a href="{{ url_for('batches', view='by_product') }}" class="btn btn-secondary">
                                <i class="fas fa-pills"></i> View Batches by Product
                            </a>
                            <a href="{{ url_for('batches', view='details') }}" class="btn btn-secondary">
                                <i class="fas fa-info-circle"></i> View Batch Details
                            </a>
                            <a href="{{ url_for('batches', view='deliveries') }}" class="btn btn-secondary">
                                <i class="fas fa-truck"></i> View Batch Deliveries
                            </a>
                            <a href="{{ url_for('batches', view='expiring') }}" class="btn btn-secondary">
                                <i class="fas fa-exclamation-triangle"></i> View Expiring Batches
                            </a>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#recordMovementModal">
                                <i class="fas fa-dolly"></i> Record Batch Movement
                            </button>
                        </div>
                    </div>
                    
                    {% if batch_id %}
                    <!-- Batch-specific movements -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Movements for Batch #{{ batch.batch_number }} - {{ batch.product_name }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Movement ID</th>
                                                    <th>Date</th>
                                                    <th>From Location</th>
                                                    <th>To Location</th>
                                                    <th>Quantity</th>
                                                    <th>Moved By</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if movements %}
                                                    {% for movement in movements %}
                                                    <tr>
                                                        <td>{{ movement.movement_id }}</td>
                                                        <td>{{ movement.movement_date }}</td>
                                                        <td>{{ movement.from_location }}</td>
                                                        <td>{{ movement.to_location }}</td>
                                                        <td>{{ movement.quantity }}</td>
                                                        <td>{{ movement.moved_by }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="6" class="text-center">No movements found for this batch</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <!-- All batch movements -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Movement ID</th>
                                    <th>Product</th>
                                    <th>Batch Number</th>
                                    <th>Date</th>
                                    <th>From Location</th>
                                    <th>To Location</th>
                                    <th>Quantity</th>
                                    <th>Moved By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if movements %}
                                    {% for movement in movements %}
                                    <tr>
                                        <td>{{ movement.movement_id }}</td>
                                        <td>{{ movement.product_name }}</td>
                                        <td>{{ movement.batch_number }}</td>
                                        <td>{{ movement.movement_date }}</td>
                                        <td>{{ movement.from_location }}</td>
                                        <td>{{ movement.to_location }}</td>
                                        <td>{{ movement.quantity }}</td>
                                        <td>{{ movement.moved_by }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">No batch movements found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Record Movement Modal -->
<div class="modal fade" id="recordMovementModal" tabindex="-1" role="dialog" aria-labelledby="recordMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="recordMovementModalLabel">Record Batch Movement</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{{ url_for('batches', action='movement') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="batch_id">Batch</label>
                        <select class="form-control" id="batch_id" name="batch_id" required>
                            <option value="">-- Select Batch --</option>
                            {% for batch in batches %}
                            <option value="{{ batch.batch_id }}">{{ batch.product_name }} - {{ batch.batch_number }} ({{ batch.location }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="from_location">From Location</label>
                        <select class="form-control" id="from_location" name="from_location" required>
                            <option value="">-- Select Location --</option>
                            {% for location in locations %}
                            <option value="{{ location.location }}">{{ location.location }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="to_location">To Location</label>
                        <select class="form-control" id="to_location" name="to_location" required>
                            <option value="">-- Select Location --</option>
                            {% for location in locations %}
                            <option value="{{ location.location }}">{{ location.location }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" required>
                    </div>
                    <div class="form-group">
                        <label for="movement_date">Movement Date</label>
                        <input type="date" class="form-control" id="movement_date" name="movement_date" value="{{ now.strftime('%Y-%m-%d') }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Record Movement</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
