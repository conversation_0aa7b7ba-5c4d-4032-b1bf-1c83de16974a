{% extends "base.html" %}

{% block title %}Inventory Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-warehouse"></i> Inventory Management
                            </h4>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('add_inventory') }}" class="btn btn-light">
                                <i class="fas fa-plus"></i> Add Stock
                            </a>
                            <a href="{{ url_for('inventory_transfer') }}" class="btn btn-warning">
                                <i class="fas fa-exchange-alt"></i> Transfer Stock
                            </a>
                            <a href="{{ url_for('products') }}" class="btn btn-success">
                                <i class="fas fa-pills"></i> Products
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ url_for('inventory') }}" class="form-inline">
                                <div class="input-group" style="width: 100%;">
                                    <input type="text" name="q" class="form-control" 
                                           placeholder="Search by product name, batch number..." 
                                           value="{{ search_query }}" id="search-input">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-outline-info">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        {% if search_query %}
                                        <a href="{{ url_for('inventory') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{{ url_for('inventory') }}">All Items</a>
                                    <a class="dropdown-item" href="{{ url_for('inventory') }}?status=active">Active Only</a>
                                    <a class="dropdown-item" href="{{ url_for('inventory') }}?status=low_stock">Low Stock</a>
                                    <a class="dropdown-item" href="{{ url_for('inventory') }}?status=expiring">Expiring Soon</a>
                                    <a class="dropdown-item" href="{{ url_for('inventory') }}?status=expired">Expired</a>
                                    <div class="dropdown-divider"></div>
                                    <h6 class="dropdown-header">By Warehouse</h6>
                                    {% for warehouse in warehouses %}
                                    <a class="dropdown-item" href="{{ url_for('inventory') }}?warehouse={{ warehouse.warehouse_id }}">{{ warehouse.name }}</a>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ inventory|length }}</h5>
                                    <small>Total Items</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>{{ inventory|selectattr('status', 'equalto', 'active')|list|length }}</h5>
                                    <small>Active Items</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ low_stock_count or 0 }}</h5>
                                    <small>Low Stock</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h5>{{ expiring_count or 0 }}</h5>
                                    <small>Expiring Soon</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="inventory-table">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Batch Number</th>
                                    <th>Warehouse</th>
                                    <th>Stock Qty</th>
                                    <th>Available</th>
                                    <th>Allocated</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inventory %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ item.product_name }}</strong>
                                            {% if item.strength %}
                                            <br><small class="text-muted">{{ item.strength }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ item.batch_number }}</strong>
                                        {% if item.manufacturing_date %}
                                        <br><small class="text-muted">Mfg: {{ item.manufacturing_date | format_datetime('%Y-%m-%d') }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.warehouse_name }}</td>
                                    <td>
                                        <span class="badge badge-{% if item.stock_quantity > 100 %}success{% elif item.stock_quantity > 50 %}warning{% else %}danger{% endif %}">
                                            {{ item.stock_quantity }}
                                        </span>
                                    </td>
                                    <td>
                                        {% set available = item.stock_quantity - (item.allocated_quantity or 0) %}
                                        <span class="badge badge-{% if available > 0 %}success{% else %}danger{% endif %}">
                                            {{ available }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if item.allocated_quantity and item.allocated_quantity > 0 %}
                                        <span class="badge badge-warning">{{ item.allocated_quantity }}</span>
                                        {% else %}
                                        <span class="badge badge-secondary">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.expiry_date %}
                                            {% set expiry_date = item.expiry_date | format_datetime('%Y-%m-%d') %}
                                            {% set days_to_expiry = (item.expiry_date | format_datetime('%Y-%m-%d') | strptime('%Y-%m-%d') - now.date()).days %}
                                            {% if days_to_expiry < 0 %}
                                            <span class="text-danger"><strong>{{ expiry_date }}</strong><br><small>EXPIRED</small></span>
                                            {% elif days_to_expiry < 30 %}
                                            <span class="text-danger">{{ expiry_date }}<br><small>{{ days_to_expiry }} days</small></span>
                                            {% elif days_to_expiry < 90 %}
                                            <span class="text-warning">{{ expiry_date }}<br><small>{{ days_to_expiry }} days</small></span>
                                            {% else %}
                                            <span class="text-success">{{ expiry_date }}</span>
                                            {% endif %}
                                        {% else %}
                                        <span class="text-muted">No Expiry</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.status == 'active' %}
                                        <span class="badge badge-success">Active</span>
                                        {% elif item.status == 'inactive' %}
                                        <span class="badge badge-secondary">Inactive</span>
                                        {% elif item.status == 'expired' %}
                                        <span class="badge badge-danger">Expired</span>
                                        {% else %}
                                        <span class="badge badge-light">{{ item.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-toggle="dropdown">
                                                Actions
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="{{ url_for('edit_inventory', inventory_id=item.id) }}">
                                                    <i class="fas fa-edit"></i> Edit Stock
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('inventory_transfer') }}?from_inventory={{ item.id }}">
                                                    <i class="fas fa-exchange-alt"></i> Transfer Stock
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="{{ url_for('inventory_history', inventory_id=item.id) }}">
                                                    <i class="fas fa-history"></i> View History
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('products') }}/{{ item.product_id }}">
                                                    <i class="fas fa-pills"></i> View Product
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                {% if item.status == 'active' %}
                                                <a class="dropdown-item text-warning" href="#" onclick="toggleInventoryStatus('{{ item.id }}', 'inactive')">
                                                    <i class="fas fa-pause"></i> Deactivate
                                                </a>
                                                {% else %}
                                                <a class="dropdown-item text-success" href="#" onclick="toggleInventoryStatus('{{ item.id }}', 'active')">
                                                    <i class="fas fa-play"></i> Activate
                                                </a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="fas fa-warehouse fa-3x mb-3"></i>
                                        <br>No inventory items found
                                        {% if search_query %}
                                        for "{{ search_query }}"
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label" for="select-all">
                                    Select All
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="bulkAction('export')">
                                    <i class="fas fa-download"></i> Export Selected
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="bulkAction('transfer')">
                                    <i class="fas fa-exchange-alt"></i> Bulk Transfer
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="bulkAction('update-status')">
                                    <i class="fas fa-toggle-on"></i> Update Status
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make table sortable and searchable
    $('#inventory-table').DataTable({
        "pageLength": 25,
        "order": [[ 6, "asc" ]], // Sort by expiry date
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on Actions column
        ]
    });

    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][data-inventory-id]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function toggleInventoryStatus(inventoryId, newStatus) {
    const message = `Change status to ${newStatus}?`;
    
    if (confirm(message)) {
        fetch(`/inventory/${inventoryId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating inventory status');
        });
    }
}

function bulkAction(action) {
    const selectedItems = [];
    const checkboxes = document.querySelectorAll('input[type="checkbox"][data-inventory-id]:checked');
    
    checkboxes.forEach(checkbox => {
        selectedItems.push(checkbox.dataset.inventoryId);
    });

    if (selectedItems.length === 0) {
        alert('Please select at least one item.');
        return;
    }

    switch(action) {
        case 'export':
            window.location.href = `/inventory/export?ids=${selectedItems.join(',')}`;
            break;
        case 'transfer':
            window.location.href = `/inventory/bulk-transfer?ids=${selectedItems.join(',')}`;
            break;
        case 'update-status':
            // Open modal for bulk status update
            $('#bulk-status-modal').modal('show');
            break;
    }
}
</script>
{% endblock %}
