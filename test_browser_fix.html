<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCS Tracking Browser Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #D40511;
            margin-top: 0;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .test-item {
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-item.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-item.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-item.loading {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background: #D40511;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #b8040f;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
        .status.delivered { background: #d4edda; color: #155724; }
        .status.in-transit { background: #fff3cd; color: #856404; }
        .status.dispatched { background: #cce5ff; color: #004085; }
        .status.error { background: #f8d7da; color: #721c24; }
        .bulk-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }
        .summary {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 TCS Tracking Browser Fix Validation</h1>
        <p>Testing the CORS-enabled TCS tracking system with known working tracking numbers.</p>
        
        <!-- Server Status Test -->
        <div class="test-section">
            <h3>🌐 Server Status Test</h3>
            <button onclick="testServerStatus()">Test Server Connection</button>
            <div id="serverStatus"></div>
        </div>

        <!-- Single Number Tests -->
        <div class="test-section">
            <h3>📦 Single Tracking Number Tests</h3>
            <p>Testing with tracking numbers that work in terminal testing:</p>
            <button onclick="testKnownNumbers()">Test Known Working Numbers</button>
            <button onclick="testInvalidNumber()">Test Invalid Number</button>
            <div id="singleTestResults" class="test-grid"></div>
        </div>

        <!-- Bulk Test -->
        <div class="test-section">
            <h3>📋 Bulk Tracking Test</h3>
            <textarea class="bulk-input" id="bulkNumbers" placeholder="Enter tracking numbers (one per line)">31442083522
31442083523
31442083525
31442083524
31442083393</textarea>
            <br>
            <button onclick="testBulkTracking()">Test Bulk Tracking</button>
            <div id="bulkResults"></div>
        </div>

        <!-- Summary -->
        <div class="summary" id="testSummary" style="display: none;">
            <h3>📊 Test Summary</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        let testResults = {
            server: null,
            single: [],
            bulk: null
        };

        async function testServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.innerHTML = '<div class="test-item loading">Testing server connection...</div>';
            
            try {
                const response = await fetch('http://localhost:5001/test');
                const data = await response.json();
                
                if (response.ok && data.status === 'ok') {
                    statusDiv.innerHTML = `
                        <div class="test-item success">
                            ✅ Server is running on port 5001<br>
                            CORS Enabled: ${data.cors_enabled}<br>
                            Message: ${data.message}
                        </div>
                    `;
                    testResults.server = true;
                } else {
                    throw new Error('Server responded but status not ok');
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="test-item error">
                        ❌ Server connection failed: ${error.message}
                    </div>
                `;
                testResults.server = false;
            }
            updateSummary();
        }

        async function testKnownNumbers() {
            const knownNumbers = ['31442083522', '31442083523', '31442083525', '31442083524', '31442083393'];
            const resultsDiv = document.getElementById('singleTestResults');
            
            resultsDiv.innerHTML = '<div class="test-item loading">Testing known working numbers...</div>';
            testResults.single = [];
            
            for (const number of knownNumbers) {
                try {
                    const response = await fetch('http://localhost:5001/api/track-tcs-public', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ tracking_number: number })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        const trackingData = data.data;
                        const statusClass = getStatusClass(trackingData.current_status);
                        
                        resultsDiv.innerHTML += `
                            <div class="test-item success">
                                <strong>✅ ${number}</strong><br>
                                <span class="status ${statusClass}">${trackingData.current_status}</span><br>
                                Route: ${trackingData.origin} → ${trackingData.destination}<br>
                                History: ${trackingData.track_history.length} entries
                            </div>
                        `;
                        
                        testResults.single.push({ number, success: true, status: trackingData.current_status });
                    } else {
                        resultsDiv.innerHTML += `
                            <div class="test-item error">
                                <strong>❌ ${number}</strong><br>
                                Error: ${data.error}
                            </div>
                        `;
                        
                        testResults.single.push({ number, success: false, error: data.error });
                    }
                } catch (error) {
                    resultsDiv.innerHTML += `
                        <div class="test-item error">
                            <strong>💥 ${number}</strong><br>
                            Exception: ${error.message}
                        </div>
                    `;
                    
                    testResults.single.push({ number, success: false, error: error.message });
                }
                
                // Add delay between requests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Remove loading message
            const loadingDiv = resultsDiv.querySelector('.loading');
            if (loadingDiv) loadingDiv.remove();
            
            updateSummary();
        }

        async function testInvalidNumber() {
            const resultsDiv = document.getElementById('singleTestResults');
            const invalidNumber = '12345';
            
            try {
                const response = await fetch('http://localhost:5001/api/track-tcs-public', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tracking_number: invalidNumber })
                });
                
                const data = await response.json();
                
                if (!data.success && data.error === 'Incorrect Number') {
                    resultsDiv.innerHTML += `
                        <div class="test-item success">
                            <strong>✅ Invalid Number Test</strong><br>
                            Number: ${invalidNumber}<br>
                            Correctly shows: "${data.error}"
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="test-item error">
                            <strong>❌ Invalid Number Test</strong><br>
                            Expected "Incorrect Number", got: "${data.error || 'Success'}"
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-item error">
                        <strong>💥 Invalid Number Test</strong><br>
                        Exception: ${error.message}
                    </div>
                `;
            }
        }

        async function testBulkTracking() {
            const bulkNumbers = document.getElementById('bulkNumbers').value
                .split('\n')
                .map(n => n.trim())
                .filter(n => n.length > 0);
            
            const resultsDiv = document.getElementById('bulkResults');
            resultsDiv.innerHTML = '<div class="test-item loading">Processing bulk tracking...</div>';
            
            let successful = 0;
            let failed = 0;
            
            for (const number of bulkNumbers) {
                try {
                    const response = await fetch('http://localhost:5001/api/track-tcs-public', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ tracking_number: number })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        successful++;
                        const trackingData = data.data;
                        resultsDiv.innerHTML += `
                            <div class="test-item success">
                                ✅ ${number}: ${trackingData.current_status}
                            </div>
                        `;
                    } else {
                        failed++;
                        resultsDiv.innerHTML += `
                            <div class="test-item error">
                                ❌ ${number}: ${data.error}
                            </div>
                        `;
                    }
                } catch (error) {
                    failed++;
                    resultsDiv.innerHTML += `
                        <div class="test-item error">
                            💥 ${number}: ${error.message}
                        </div>
                    `;
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Remove loading message
            const loadingDiv = resultsDiv.querySelector('.loading');
            if (loadingDiv) loadingDiv.remove();
            
            // Add summary
            resultsDiv.innerHTML += `
                <div class="test-item ${successful > failed ? 'success' : 'error'}">
                    <strong>Bulk Test Summary:</strong><br>
                    ✅ Successful: ${successful}<br>
                    ❌ Failed: ${failed}<br>
                    📊 Success Rate: ${((successful / bulkNumbers.length) * 100).toFixed(1)}%
                </div>
            `;
            
            testResults.bulk = { successful, failed, total: bulkNumbers.length };
            updateSummary();
        }

        function getStatusClass(status) {
            if (!status) return 'error';
            const statusLower = status.toLowerCase();
            if (statusLower.includes('delivered')) return 'delivered';
            if (statusLower.includes('transit')) return 'in-transit';
            if (statusLower.includes('dispatch')) return 'dispatched';
            return 'in-transit';
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('testSummary');
            const contentDiv = document.getElementById('summaryContent');
            
            let content = '';
            
            if (testResults.server !== null) {
                content += `<p><strong>Server Status:</strong> ${testResults.server ? '✅ Working' : '❌ Failed'}</p>`;
            }
            
            if (testResults.single.length > 0) {
                const successful = testResults.single.filter(r => r.success).length;
                const total = testResults.single.length;
                content += `<p><strong>Single Tests:</strong> ${successful}/${total} successful (${((successful/total)*100).toFixed(1)}%)</p>`;
            }
            
            if (testResults.bulk) {
                const { successful, total } = testResults.bulk;
                content += `<p><strong>Bulk Test:</strong> ${successful}/${total} successful (${((successful/total)*100).toFixed(1)}%)</p>`;
            }
            
            if (content) {
                contentDiv.innerHTML = content;
                summaryDiv.style.display = 'block';
            }
        }

        // Auto-test server status on load
        window.addEventListener('load', function() {
            setTimeout(testServerStatus, 1000);
        });
    </script>
</body>
</html>
