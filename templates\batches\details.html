{% extends 'base.html' %}

{% block title %}Batch Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Batch Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> View All Batches
                            </a>
                            <a href="{{ url_for('batches', view='by_product') }}" class="btn btn-secondary">
                                <i class="fas fa-pills"></i> View Batches by Product
                            </a>
                            <a href="{{ url_for('batches', view='movements') }}" class="btn btn-secondary">
                                <i class="fas fa-exchange-alt"></i> View Batch Movements
                            </a>
                            <a href="{{ url_for('batches', view='deliveries') }}" class="btn btn-secondary">
                                <i class="fas fa-truck"></i> View Batch Deliveries
                            </a>
                            <a href="{{ url_for('batches', view='expiring') }}" class="btn btn-secondary">
                                <i class="fas fa-exclamation-triangle"></i> View Expiring Batches
                            </a>
                        </div>
                    </div>

                    {% if batch %}
                    <!-- Batch details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Batch Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>Product ID</th>
                                            <td>{{ batch.product_id }}</td>
                                        </tr>
                                        <tr>
                                            <th>Product Name</th>
                                            <td>{{ batch.product_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Strength</th>
                                            <td>{{ batch.strength }}</td>
                                        </tr>
                                        <tr>
                                            <th>Batch Number</th>
                                            <td><strong>{{ batch.batch_number }}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>Country</th>
                                            <td>{{ batch.country or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Manufacturer</th>
                                            <td>{{ batch.manufacturer or 'Generic' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Division</th>
                                            <td>{{ batch.division or 'Unassigned' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Manufacturing Date</th>
                                            <td>{{ batch.manufacturing_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>Expiry Date</th>
                                            <td>{{ batch.expiry_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>Date Received</th>
                                            <td>{{ batch.date_received }}</td>
                                        </tr>
                                        <tr>
                                            <th>Status</th>
                                            <td>
                                                <span class="badge
                                                    {% if batch.status == 'active' %}badge-success
                                                    {% elif batch.status == 'expired' %}badge-danger
                                                    {% else %}badge-secondary{% endif %}">
                                                    {{ batch.status }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Inventory Status</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>Warehouse</th>
                                            <td>{{ batch.warehouse_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Warehouse Address</th>
                                            <td>{{ batch.warehouse_address or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Stock Quantity</th>
                                            <td><strong>{{ batch.stock_quantity }} {{ batch.unit_of_measure }}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>Allocated Quantity</th>
                                            <td>{{ batch.allocated_quantity or 0 }} {{ batch.unit_of_measure }}</td>
                                        </tr>
                                        <tr>
                                            <th>Available Quantity</th>
                                            <td><strong>{{ (batch.stock_quantity - (batch.allocated_quantity or 0)) }} {{ batch.unit_of_measure }}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>Country of Origin</th>
                                            <td>{{ batch.country_of_origin or batch.country or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Last Updated</th>
                                            <td>{{ batch.last_updated }}</td>
                                        </tr>
                                        <tr>
                                            <th>Updated By</th>
                                            <td>{{ batch.updated_by or 'System' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">Batch History</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="nav nav-tabs" id="batchHistoryTab" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="movements-tab" data-toggle="tab" href="#movements" role="tab" aria-controls="movements" aria-selected="true">Movements</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="deliveries-tab" data-toggle="tab" href="#deliveries" role="tab" aria-controls="deliveries" aria-selected="false">Deliveries</a>
                                        </li>
                                    </ul>
                                    <div class="tab-content" id="batchHistoryTabContent">
                                        <div class="tab-pane fade show active" id="movements" role="tabpanel" aria-labelledby="movements-tab">
                                            <div class="table-responsive mt-3">
                                                <table class="table table-striped table-hover">
                                                    <thead class="thead-dark">
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>From</th>
                                                            <th>To</th>
                                                            <th>Quantity</th>
                                                            <th>Moved By</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% if movements %}
                                                            {% for movement in movements %}
                                                            <tr>
                                                                <td>{{ movement.movement_date }}</td>
                                                                <td>{{ movement.from_location }}</td>
                                                                <td>{{ movement.to_location }}</td>
                                                                <td>{{ movement.quantity }}</td>
                                                                <td>{{ movement.moved_by }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        {% else %}
                                                            <tr>
                                                                <td colspan="5" class="text-center">No movement history found</td>
                                                            </tr>
                                                        {% endif %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="deliveries" role="tabpanel" aria-labelledby="deliveries-tab">
                                            <div class="table-responsive mt-3">
                                                <table class="table table-striped table-hover">
                                                    <thead class="thead-dark">
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>Order ID</th>
                                                            <th>Customer</th>
                                                            <th>Quantity</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% if deliveries %}
                                                            {% for delivery in deliveries %}
                                                            <tr>
                                                                <td>{{ delivery.delivery_date }}</td>
                                                                <td>{{ delivery.order_id }}</td>
                                                                <td>{{ delivery.customer_name }}</td>
                                                                <td>{{ delivery.quantity }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        {% else %}
                                                            <tr>
                                                                <td colspan="4" class="text-center">No delivery history found</td>
                                                            </tr>
                                                        {% endif %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <!-- Batch selection form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Select Batch</h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" action="{{ url_for('batches', view='details') }}">
                                        <div class="form-group">
                                            <label for="batch_id">Batch</label>
                                            <select class="form-control" id="batch_id" name="batch_id" required onchange="this.form.submit()">
                                                <option value="">-- Select Batch --</option>
                                                {% for batch in batches %}
                                                <option value="{{ batch.batch_id }}">{{ batch.product_name }} - {{ batch.batch_number }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">View Details</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
