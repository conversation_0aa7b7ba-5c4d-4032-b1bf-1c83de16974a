"""
Simplified TCS Express Tracking Scraper
Uses Playwright for robust web scraping with simplified approach
"""

import asyncio
import json
import re
import time
from typing import Dict, Any, Optional
from playwright.async_api import async_playwright, Page, Browser
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TCSScraperSimple:
    """Simplified TCS Express tracking scraper"""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.base_url = "https://www.tcsexpress.com/track"
        
    async def track_shipment(self, tracking_number: str) -> Dict[str, Any]:
        """Track a shipment by tracking number"""
        playwright = None
        browser = None
        
        try:
            # Start Playwright
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            # Create a new page
            page = await browser.new_page()
            
            # Set user agent
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            # Navigate to tracking page
            url = f"{self.base_url}?tracking_number={tracking_number}"
            logger.info(f"Navigating to: {url}")
            
            await page.goto(url, wait_until='networkidle', timeout=30000)
            
            # Wait for content to load
            await page.wait_for_selector('#main-content-app', timeout=10000)
            
            # Wait a bit more for dynamic content
            await asyncio.sleep(2)
            
            # Take screenshot for debugging if not headless
            if not self.headless:
                await page.screenshot(path=f"tcs_{tracking_number}.png")
            
            # Extract tracking data
            tracking_data = await self._extract_data(page, tracking_number)
            
            return tracking_data
            
        except Exception as e:
            logger.error(f"Error tracking shipment {tracking_number}: {e}")
            return self._create_error_response(tracking_number, str(e))
            
        finally:
            # Clean up
            if browser:
                await browser.close()
            if playwright:
                await playwright.stop()
                
    async def _extract_data(self, page: Page, tracking_number: str) -> Dict[str, Any]:
        """Extract tracking data using multiple methods"""
        try:
            # Get page content
            content = await page.content()
            
            # Check for error messages
            error_patterns = [
                "No tracking information found",
                "Invalid tracking number",
                "not found in our system"
            ]
            
            page_text = await page.text_content('body')
            for pattern in error_patterns:
                if pattern.lower() in page_text.lower():
                    return self._create_error_response(tracking_number, f"TCS reports: {pattern}")
            
            # Initialize result
            result = {
                'success': True,
                'tracking_number': tracking_number,
                'track_history': []
            }
            
            # Method 1: Extract using CSS selectors
            await self._extract_using_css(page, result)
            
            # Method 2: Extract using text content
            await self._extract_using_text(page, result)
            
            # Method 3: Extract using regex patterns on HTML
            self._extract_using_regex(content, result)
            
            # Set default values for missing fields
            self._set_default_values(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting data: {e}")
            return self._create_error_response(tracking_number, f"Data extraction error: {str(e)}")
            
    async def _extract_using_css(self, page: Page, result: Dict[str, Any]):
        """Extract data using CSS selectors"""
        try:
            # Try to find tracking number
            tracking_element = await page.query_selector('span.text-lg.text-\\[\\#f0575d\\].font-bold')
            if tracking_element:
                text = await tracking_element.text_content()
                if text:
                    result['tracking_number'] = text.strip()
                    
            # Try to find origin/destination
            flex_divs = await page.query_selector_all('div.flex')
            for div in flex_divs:
                text = await div.text_content()
                if 'Origin:' in text:
                    # Extract text after "Origin:"
                    match = re.search(r'Origin:(.*?)(?:$|Destination:|Booking)', text)
                    if match:
                        result['origin'] = match.group(1).strip()
                if 'Destination:' in text:
                    # Extract text after "Destination:"
                    match = re.search(r'Destination:(.*?)(?:$|Origin:|Booking)', text)
                    if match:
                        result['destination'] = match.group(1).strip()
                if 'Booking Date:' in text:
                    # Extract text after "Booking Date:"
                    match = re.search(r'Booking Date:(.*?)(?:$|Origin:|Destination:)', text)
                    if match:
                        result['booking_date'] = match.group(1).strip()
                        
            # Try to find current status
            status_element = await page.query_selector('span.text-md.text-\\[\\#f0575d\\].font-bold')
            if status_element:
                text = await status_element.text_content()
                if text:
                    result['current_status'] = text.strip()
                    
            # Try to find delivery info
            delivery_divs = await page.query_selector_all('div.flex')
            for div in delivery_divs:
                text = await div.text_content()
                if 'Delivered On:' in text:
                    match = re.search(r'Delivered On:(.*?)(?:$|Received)', text)
                    if match:
                        result['delivered_on'] = match.group(1).strip()
                if 'Received by:' in text:
                    match = re.search(r'Received by:(.*?)(?:$)', text)
                    if match:
                        result['received_by'] = match.group(1).strip()
                        
            # Try to find track history
            table = await page.query_selector('table')
            if table:
                rows = await table.query_selector_all('tbody tr')
                for row in rows:
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 2:
                        date_cell = cells[0]
                        status_cell = cells[1]
                        
                        date_text = await date_cell.text_content()
                        status_text = await status_cell.text_content()
                        
                        if date_text and status_text:
                            # Clean up text
                            date_clean = re.sub(r'\s+', ' ', date_text.strip())
                            status_parts = status_text.strip().split('\n')
                            status = status_parts[0].strip() if status_parts else ''
                            location = status_parts[1].strip() if len(status_parts) > 1 else ''
                            
                            result['track_history'].append({
                                'date_time': date_clean,
                                'status': status,
                                'location': location
                            })
                            
        except Exception as e:
            logger.error(f"Error in CSS extraction: {e}")
            
    async def _extract_using_text(self, page: Page, result: Dict[str, Any]):
        """Extract data using text content"""
        try:
            # Get all text
            text = await page.text_content('body')
            
            # Extract current status
            if 'current_status' not in result or not result['current_status']:
                status_match = re.search(r'Current Status:[\s\n]*([^\n]+)', text)
                if status_match:
                    result['current_status'] = status_match.group(1).strip()
                    
            # Extract delivered info
            if 'delivered_on' not in result or not result['delivered_on']:
                delivered_match = re.search(r'Delivered On:[\s\n]*([^\n]+)', text)
                if delivered_match:
                    result['delivered_on'] = delivered_match.group(1).strip()
                    
            # Extract received by
            if 'received_by' not in result or not result['received_by']:
                received_match = re.search(r'Received by:[\s\n]*([^\n]+)', text)
                if received_match:
                    result['received_by'] = received_match.group(1).strip()
                    
            # Extract origin
            if 'origin' not in result or not result['origin']:
                origin_match = re.search(r'Origin:[\s\n]*([^\n]+)', text)
                if origin_match:
                    result['origin'] = origin_match.group(1).strip()
                    
            # Extract destination
            if 'destination' not in result or not result['destination']:
                dest_match = re.search(r'Destination:[\s\n]*([^\n]+)', text)
                if dest_match:
                    result['destination'] = dest_match.group(1).strip()
                    
            # Extract booking date
            if 'booking_date' not in result or not result['booking_date']:
                booking_match = re.search(r'Booking Date:[\s\n]*([^\n]+)', text)
                if booking_match:
                    result['booking_date'] = booking_match.group(1).strip()
                    
        except Exception as e:
            logger.error(f"Error in text extraction: {e}")
            
    def _extract_using_regex(self, html: str, result: Dict[str, Any]):
        """Extract data using regex on HTML content"""
        try:
            # Extract tracking number if not already found
            if result['tracking_number'] == 'Unknown':
                tracking_match = re.search(r'text-lg text-\[#f0575d\] font-bold[^>]*>([^<]+)', html)
                if tracking_match:
                    result['tracking_number'] = tracking_match.group(1).strip()
                    
            # Extract track history if empty
            if not result['track_history']:
                # Find table rows
                rows = re.findall(r'<tr class="border-b-2 p-2">(.*?)</tr>', html, re.DOTALL)
                for row in rows:
                    # Extract date and status
                    date_match = re.search(r'<td class="p-2">(.*?)</td>', row, re.DOTALL)
                    status_match = re.search(r'<td class="p-2 ">(.*?)</td>', row, re.DOTALL)
                    
                    if date_match and status_match:
                        date_text = re.sub(r'<[^>]+>', ' ', date_match.group(1))
                        date_text = re.sub(r'\s+', ' ', date_text).strip()
                        
                        status_text = re.sub(r'<[^>]+>', ' ', status_match.group(1))
                        status_parts = re.sub(r'\s+', ' ', status_text).strip().split(' ')
                        
                        # Extract status and location
                        status = ' '.join(status_parts[:3]) if len(status_parts) >= 3 else ' '.join(status_parts)
                        location = ' '.join(status_parts[3:]) if len(status_parts) > 3 else ''
                        
                        result['track_history'].append({
                            'date_time': date_text,
                            'status': status,
                            'location': location
                        })
                        
        except Exception as e:
            logger.error(f"Error in regex extraction: {e}")
            
    def _set_default_values(self, result: Dict[str, Any]):
        """Set default values for missing fields"""
        defaults = {
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Unknown',
            'delivered_on': 'N/A',
            'received_by': 'N/A'
        }
        
        for key, default_value in defaults.items():
            if key not in result or not result[key]:
                result[key] = default_value
                
    def _create_error_response(self, tracking_number: str, error_message: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            'success': False,
            'tracking_number': tracking_number,
            'error': error_message,
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Error',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }

# Synchronous wrapper for Flask integration
def track_tcs_shipment_simple(tracking_number: str, headless: bool = True) -> Dict[str, Any]:
    """Synchronous wrapper for tracking TCS shipments"""
    async def _track():
        scraper = TCSScraperSimple(headless=headless)
        return await scraper.track_shipment(tracking_number)
    
    try:
        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_track())
        loop.close()
        return result
    except Exception as e:
        logger.error(f"Error in synchronous wrapper: {e}")
        return {
            'success': False,
            'tracking_number': tracking_number,
            'error': f"System error: {str(e)}",
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Error',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }

# Test function
async def test_tracking():
    """Test the tracking functionality"""
    test_numbers = [
        "31442084039",  # Known working number
        "31442083394",  # Another test number
        "31442083525"   # Another test number
    ]
    
    scraper = TCSScraperSimple(headless=False)  # Set to False to see the browser
    
    for number in test_numbers:
        print(f"\n=== Testing {number} ===")
        result = await scraper.track_shipment(number)
        print(json.dumps(result, indent=2))
        time.sleep(2)  # Pause between requests

if __name__ == "__main__":
    # Run test
    asyncio.run(test_tracking())
