#!/usr/bin/env python3
"""
Clear organization-related database tables for fresh start
"""

import sqlite3
import os
from datetime import datetime

def clear_organization_tables():
    """Clear organization-related tables"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    # Create backup
    backup_path = f'instance/medivent_backup_org_clear_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    import shutil
    shutil.copy2(db_path, backup_path)
    print(f"✅ Backup created: {backup_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧹 CLEARING ORGANIZATION TABLES")
        print("=" * 50)
        
        # Organization-related tables to clear
        org_tables = [
            'employees',
            'departments', 
            'positions',
            'employee_divisions',
            'organization_structure',
            'team_members',
            'leadership'
        ]
        
        total_cleared = 0
        for table in org_tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                if count > 0:
                    cursor.execute(f'DELETE FROM {table}')
                    print(f"✅ {table}: {count} records deleted")
                    total_cleared += count
                else:
                    print(f"ℹ️  {table}: already empty")
            except sqlite3.Error as e:
                print(f"⚠️  {table}: {e}")
        
        # Reset auto-increment counters
        for table in org_tables:
            try:
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
            except sqlite3.Error:
                pass
        
        print("✅ Auto-increment counters reset")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 ORGANIZATION TABLES CLEARED!")
        print(f"✅ Total records deleted: {total_cleared}")
        print("🎯 Ready for new organization system!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing organization tables: {e}")
        return False

if __name__ == "__main__":
    success = clear_organization_tables()
    if success:
        print("\n🎉 SUCCESS! Organization tables cleared!")
    else:
        print("\n❌ FAILED to clear organization tables")
