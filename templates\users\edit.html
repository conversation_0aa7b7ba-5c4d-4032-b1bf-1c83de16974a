{% extends 'base.html' %}

{% block title %}Edit User{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit User: {{ user.username }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('users.edit', user_id=user.id) }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username">Username</label>
                                    <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                                    <small class="form-text text-muted">Username cannot be changed.</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="full_name">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" value="{{ user.full_name }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="role">Role <span class="text-danger">*</span></label>
                                    <select class="form-control" id="role" name="role" required {% if user.username == 'admin' %}disabled{% endif %}>
                                        <option value="user" {% if user.role == 'user' %}selected{% endif %}>User</option>
                                        <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
                                        <option value="manager" {% if user.role == 'manager' %}selected{% endif %}>Manager</option>
                                        <option value="sales" {% if user.role == 'sales' %}selected{% endif %}>Sales</option>
                                        <option value="warehouse" {% if user.role == 'warehouse' %}selected{% endif %}>Warehouse</option>
                                        <option value="rider" {% if user.role == 'rider' %}selected{% endif %}>Rider</option>
                                        <option value="customer" {% if user.role == 'customer' %}selected{% endif %}>Customer</option>
                                    </select>
                                    {% if user.username == 'admin' %}
                                    <input type="hidden" name="role" value="admin">
                                    <small class="form-text text-muted">The admin user's role cannot be changed.</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Status <span class="text-danger">*</span></label>
                                    <select class="form-control" id="status" name="status" required {% if user.username == 'admin' %}disabled{% endif %}>
                                        <option value="active" {% if user.status == 'active' %}selected{% endif %}>Active</option>
                                        <option value="inactive" {% if user.status == 'inactive' %}selected{% endif %}>Inactive</option>
                                    </select>
                                    {% if user.username == 'admin' %}
                                    <input type="hidden" name="status" value="active">
                                    <small class="form-text text-muted">The admin user's status cannot be changed.</small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Last Login</label>
                                    <input type="text" class="form-control" value="{{ user.last_login|default('Never', true) }}" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-4">
                            <a href="{{ url_for('users.manage') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
