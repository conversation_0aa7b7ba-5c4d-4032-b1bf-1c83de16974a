{% extends 'base.html' %}

{% block title %}Inventory Analytics - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Inventory Analytics Dashboard</h4>
                    <div>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('inventory_analytics') }}" method="get" class="form-inline">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Warehouse</span>
                                    </div>
                                    <select name="warehouse_id" id="warehouse_id" class="form-control">
                                        <option value="all" {% if warehouse_id == 'all' %}selected{% endif %}>All Warehouses</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}" {% if warehouse_id|string == warehouse.warehouse_id|string %}selected{% endif %}>
                                            {{ warehouse.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">Apply</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h2>{{ division_inventory|length }}</h2>
                                    <p class="mb-0">Total Divisions</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h2>{{ division_quantities|sum }}</h2>
                                    <p class="mb-0">Total Stock Units</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h2>{{ expiry_quantities[0] if expiry_quantities|length > 0 else 0 }}</h2>
                                    <p class="mb-0">Expiring Soon</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h2>{{ age_quantities[-1] if age_quantities|length > 0 else 0 }}</h2>
                                    <p class="mb-0">Aging Stock</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Inventory by Division</h5>
                                </div>
                                <div class="card-body">
                                    <div id="division-chart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">Expiry Breakdown</h5>
                                </div>
                                <div class="card-body">
                                    <div id="expiry-chart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Stock Age Analysis</h5>
                                </div>
                                <div class="card-body">
                                    <div id="age-chart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Risk Assessment</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <h4 class="alert-heading">Inventory Risk Factors</h4>
                                        <ul>
                                            {% if expiry_quantities and expiry_quantities|length > 0 and expiry_quantities[0] > 0 %}
                                            <li>{{ expiry_quantities[0] }} units expiring within 30 days</li>
                                            {% endif %}
                                            {% if expiry_quantities and expiry_quantities|length > 1 and expiry_quantities[1] > 0 %}
                                            <li>{{ expiry_quantities[1] }} units expiring within 90 days</li>
                                            {% endif %}
                                            {% if age_quantities and age_quantities|length > 0 and age_quantities[-1] > 0 %}
                                            <li>{{ age_quantities[-1] }} units in stock for over 180 days</li>
                                            {% endif %}
                                            {% if not expiry_quantities or expiry_quantities|length == 0 %}
                                            <li>No inventory data available for the selected warehouse</li>
                                            {% endif %}
                                        </ul>
                                        <hr>
                                        <p class="mb-0">Recommended actions: Review expiring inventory and consider promotions or discounts for aging stock.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Tables -->
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Division Inventory Details</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Division</th>
                                                    <th>Total Products</th>
                                                    <th>Total Stock</th>
                                                    <th>Expiring Soon</th>
                                                    <th>Aging Stock</th>
                                                    <th>Stock Value (Est.)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for division in division_inventory %}
                                                <tr>
                                                    <td>{{ division.division_name }}</td>
                                                    <td>{{ division.product_count }}</td>
                                                    <td>{{ division.total_quantity }}</td>
                                                    <td>{{ division.expiring_soon }}</td>
                                                    <td>{{ division.aging_stock }}</td>
                                                    <td>Rs. {{ division.stock_value|int }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    $(document).ready(function() {
        // Auto-submit form when warehouse selection changes
        $('#warehouse_id').change(function() {
            $(this).closest('form').submit();
        });

        // Division Chart
        var divisionData = [{
            type: 'pie',
            labels: {{ division_names|tojson }},
            values: {{ division_quantities|tojson }},
            textinfo: 'label+percent',
            insidetextorientation: 'radial'
        }];

        var divisionLayout = {
            margin: {t:30, b:30, l:30, r:30},
            showlegend: false
        };

        Plotly.newPlot('division-chart', divisionData, divisionLayout);

        // Expiry Chart
        var expiryData = [{
            type: 'bar',
            x: {{ expiry_categories|tojson }},
            y: {{ expiry_quantities|tojson }},
            marker: {
                color: ['#dc3545', '#ffc107', '#17a2b8', '#28a745']
            }
        }];

        var expiryLayout = {
            margin: {t:30, b:80, l:50, r:30},
            xaxis: {title: 'Expiry Period'},
            yaxis: {title: 'Units'}
        };

        Plotly.newPlot('expiry-chart', expiryData, expiryLayout);

        // Age Chart
        var ageData = [{
            type: 'bar',
            x: {{ age_categories|tojson }},
            y: {{ age_quantities|tojson }},
            marker: {
                color: ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
            }
        }];

        var ageLayout = {
            margin: {t:30, b:80, l:50, r:30},
            xaxis: {title: 'Stock Age (Days)'},
            yaxis: {title: 'Units'}
        };

        Plotly.newPlot('age-chart', ageData, ageLayout);
    });
</script>
{% endblock %}
