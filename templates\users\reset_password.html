{% extends 'base.html' %}

{% block title %}Reset Password{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Reset Password: {{ user.username }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('users.reset_password', user_id=user.id) }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username">Username</label>
                                    <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="new_password">New Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                                </div>
                                <div class="form-group">
                                    <label for="confirm_password">Confirm New Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>

                                {% if current_user.id == user.id %}
                                <div class="form-group">
                                    <label for="current_password">Current Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    <small class="form-text text-muted">Enter your current password to confirm.</small>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> Password Reset</h5>
                                    <p>You are about to reset the password for user <strong>{{ user.username }}</strong>.</p>

                                    {% if current_user.id == user.id %}
                                    <p>Since you are changing your own password, you need to enter your current password for verification.</p>
                                    {% else %}
                                    <p>As an administrator, you can reset this user's password without knowing their current password.</p>
                                    <p>The user will need to use this new password for their next login.</p>
                                    {% endif %}

                                    <p>Password requirements:</p>
                                    <ul>
                                        <li>At least 8 characters long</li>
                                        <li>Should be strong and secure</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-4">
                            <a href="{{ url_for('users.manage') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Reset Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Form validation
        $('form').submit(function(e) {
            const newPassword = $('#new_password').val();
            const confirmPassword = $('#confirm_password').val();

            // Check password length
            if (newPassword.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }

            // Check if passwords match
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match.');
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}
