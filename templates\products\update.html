{% extends 'base.html' %}

{% block title %}Update Product - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }
    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40,167,69,0.4);
    }
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
    }
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px 15px 0 0 !important;
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .required {
        color: #dc3545;
    }
    .price-input {
        position: relative;
    }
    .price-input .currency {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-weight: 600;
    }
    .price-input input {
        padding-left: 35px;
    }
    .section-divider {
        border-top: 2px solid #e9ecef;
        margin: 30px 0;
        position: relative;
    }
    .section-divider::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 4px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-radius: 2px;
    }
    .update-badge {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Enhanced Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-edit text-warning"></i> Update Product
                <span class="update-badge">EDITING</span>
            </h2>
            <p class="text-muted">Modify product information: <strong>{{ product.name }}</strong></p>
        </div>
        <div>
            <a href="{{ url_for('products') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
            <a href="{{ url_for('view_product', product_id=product.product_id) }}" class="btn btn-outline-info">
                <i class="fas fa-eye"></i> View Details
            </a>
        </div>
    </div>

    <!-- Enhanced Update Product Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Product Information</h5>
        </div>
        <div class="card-body">
                    <form method="post" action="{{ url_for('update_product', product_id=product.product_id) }}">
                        <div class="row">
                            <!-- Product Image Upload -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Product Image</label>
                                    <div class="upload-area" onclick="document.getElementById('product_image').click()">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                        <p class="mb-2"><strong>Click to upload</strong></p>
                                        <p class="text-muted small">PNG, JPG up to 5MB</p>
                                        <input type="file" id="product_image" name="product_image" class="d-none" accept="image/*">
                                    </div>
                                </div>
                            </div>

                            <!-- Product Details -->
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Product Name <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ product.name }}" placeholder="Enter product name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="strength">Strength <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="strength" name="strength"
                                                   value="{{ product.strength }}" placeholder="e.g., 500mg, 10ml, 100IU" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="category">Generic <span class="required">*</span></label>
                                            <select class="form-control" id="category" name="category" required>
                                                <option value="">Select Generic</option>
                                                <option {% if product.category == 'Analgesics' %}selected{% endif %}>Analgesics</option>
                                                <option {% if product.category == 'Antibiotics' %}selected{% endif %}>Antibiotics</option>
                                                <option {% if product.category == 'Vitamins' %}selected{% endif %}>Vitamins</option>
                                                <option {% if product.category == 'Antacids' %}selected{% endif %}>Antacids</option>
                                                <option {% if product.category == 'Cardiovascular' %}selected{% endif %}>Cardiovascular</option>
                                                <option {% if product.category == 'Respiratory' %}selected{% endif %}>Respiratory</option>
                                                {% if product.category and product.category not in ['Analgesics', 'Antibiotics', 'Vitamins', 'Antacids', 'Cardiovascular', 'Respiratory'] %}
                                                <option selected>{{ product.category }}</option>
                                                {% endif %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="manufacturer">Manufacturer <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="manufacturer" name="manufacturer"
                                                   value="{{ product.manufacturer or 'Medivent' }}" placeholder="Enter manufacturer" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="division_id">Division <span class="required">*</span></label>
                                            <select class="form-control" id="division_id" name="division_id" required>
                                                <option value="">Select Division</option>
                                                {% for division in divisions %}
                                                <option value="{{ division.division_id }}" {% if product.division_id == division.division_id %}selected{% endif %}>
                                                    {{ division.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Information -->
                        <hr class="my-4">
                        <h6 class="text-primary mb-3"><i class="fas fa-rupee-sign"></i> Pricing Information</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="mrp">MRP (Maximum Retail Price) <span class="required">*</span></label>
                                    <div class="price-input">
                                        <span class="currency">₹</span>
                                        <input type="number" class="form-control" id="mrp" name="mrp"
                                               value="{{ product.mrp or product.unit_price or 0 }}" placeholder="0.00" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="tp_rate">TP Rate (Trade Price) <span class="required">*</span></label>
                                    <div class="price-input">
                                        <span class="currency">₹</span>
                                        <input type="number" class="form-control" id="tp_rate" name="tp_rate"
                                               value="{{ product.tp_rate or (product.unit_price * 0.8) if product.unit_price else 0 }}" placeholder="0.00" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="unit_price">ASP (Average Selling Price) <span class="required">*</span></label>
                                    <div class="price-input">
                                        <span class="currency">₹</span>
                                        <input type="number" class="form-control" id="unit_price" name="unit_price"
                                               value="{{ product.unit_price or 0 }}" placeholder="0.00" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="current_stock">Current Stock</label>
                                    <input type="number" class="form-control" id="current_stock" name="current_stock"
                                           value="{{ product.stock_quantity or 0 }}" placeholder="0" min="0" readonly>
                                    <small class="text-muted">Current stock level (read-only)</small>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <hr class="my-4">
                        <h6 class="text-primary mb-3"><i class="fas fa-info"></i> Additional Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="description">Product Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              placeholder="Enter product description, usage instructions, etc.">{{ product.description or '' }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="unit_of_measure">Unit of Measure</label>
                                            <select class="form-control" id="unit_of_measure" name="unit_of_measure">
                                                <option value="Tablet" {% if product.unit_of_measure == 'Tablet' %}selected{% endif %}>Tablet</option>
                                                <option value="Capsule" {% if product.unit_of_measure == 'Capsule' %}selected{% endif %}>Capsule</option>
                                                <option value="Bottle" {% if product.unit_of_measure == 'Bottle' %}selected{% endif %}>Bottle</option>
                                                <option value="Vial" {% if product.unit_of_measure == 'Vial' %}selected{% endif %}>Vial</option>
                                                <option value="Ampule" {% if product.unit_of_measure == 'Ampule' %}selected{% endif %}>Ampule</option>
                                                <option value="Box" {% if product.unit_of_measure == 'Box' %}selected{% endif %}>Box</option>
                                                <option value="Pack" {% if product.unit_of_measure == 'Pack' %}selected{% endif %}>Pack</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="min_stock_level">Minimum Stock Level</label>
                                            <input type="number" class="form-control" id="min_stock_level" name="min_stock_level"
                                                   value="{{ product.min_stock_level or 10 }}" placeholder="10" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="product_code">Product Code/SKU</label>
                                    <input type="text" class="form-control" id="product_code" name="product_code"
                                           value="{{ product.product_id }}" placeholder="Product code" readonly>
                                    <small class="text-muted">Product ID (read-only)</small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <hr class="my-4">
                        <div class="d-flex justify-content-end">
                            <a href="{{ url_for('update_product_selection') }}" class="btn btn-outline-secondary me-3">
                                <i class="fas fa-arrow-left"></i> Back to Selection
                            </a>
                            <a href="{{ url_for('view_product', product_id=product.product_id) }}" class="btn btn-outline-info me-3">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            <button type="button" class="btn btn-outline-warning me-3" onclick="resetForm()">
                                <i class="fas fa-undo"></i> Reset Changes
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Update Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Image upload preview functionality
document.getElementById('product_image').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        const file = e.target.files[0];
        const reader = new FileReader();
        reader.onload = function(e) {
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = `
                <img src="${e.target.result}" style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                <p class="mt-2 mb-0 text-success"><i class="fas fa-check"></i> Image updated</p>
            `;
        };
        reader.readAsDataURL(file);
    }
});

// Auto-calculate margins and validate pricing
document.querySelectorAll('.price-input input').forEach(input => {
    input.addEventListener('input', function() {
        console.log('Price updated:', this.value);
        // Add validation logic here if needed
    });
});

// Reset form functionality
function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will restore the original values.')) {
        location.reload();
    }
}

// Enhanced form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields marked with *');
    }
});

// Real-time validation
document.querySelectorAll('input[required], select[required]').forEach(input => {
    input.addEventListener('blur', function() {
        if (!this.value.trim()) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
});

// Auto-save draft functionality
let autoSaveTimer;
document.querySelectorAll('input, select, textarea').forEach(element => {
    element.addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            console.log('Auto-saving changes...');
            // Auto-save logic can be implemented here
        }, 2000);
    });
});
</script>
{% endblock %}
