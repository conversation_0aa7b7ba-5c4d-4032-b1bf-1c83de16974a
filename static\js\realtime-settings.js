/**
 * Real-Time Settings Panel for Medivent Pharmaceuticals ERP
 * Allows users to control auto-refresh settings
 */

class RealTimeSettings {
    constructor() {
        this.createSettingsPanel();
        this.loadSettings();
    }
    
    createSettingsPanel() {
        // Create settings button
        const settingsBtn = document.createElement('button');
        settingsBtn.innerHTML = '<i class="fas fa-cog"></i>';
        settingsBtn.className = 'btn btn-sm btn-outline-secondary';
        settingsBtn.style.cssText = 'position: fixed; top: 90px; right: 10px; z-index: 1000;';
        settingsBtn.title = 'Real-Time Settings';
        settingsBtn.onclick = () => this.toggleSettingsPanel();
        document.body.appendChild(settingsBtn);
        
        // Create settings panel
        const panel = document.createElement('div');
        panel.id = 'realtime-settings-panel';
        panel.innerHTML = `
            <div class="card" style="width: 300px;">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-broadcast-tower"></i> Real-Time Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="form-group mb-3">
                        <label for="update-interval">Update Interval</label>
                        <select class="form-control form-control-sm" id="update-interval">
                            <option value="10000">10 seconds</option>
                            <option value="30000" selected>30 seconds</option>
                            <option value="60000">1 minute</option>
                            <option value="300000">5 minutes</option>
                            <option value="0">Disabled</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                            <label class="form-check-label" for="auto-refresh">
                                Auto Refresh
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show-notifications" checked>
                            <label class="form-check-label" for="show-notifications">
                                Show Update Notifications
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="update-charts" checked>
                            <label class="form-check-label" for="update-charts">
                                Update Charts
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="data-source">Data Source</label>
                        <select class="form-control form-control-sm" id="data-source">
                            <option value="live">Live Data</option>
                            <option value="mock" selected>Mock Data</option>
                        </select>
                    </div>
                    
                    <div class="btn-group btn-group-sm w-100">
                        <button type="button" class="btn btn-success" onclick="realtimeSettings.applySettings()">
                            <i class="fas fa-check"></i> Apply
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="realtimeSettings.resetSettings()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            Last Update: <span id="last-update-time">Never</span>
                        </small>
                    </div>
                </div>
            </div>
        `;
        panel.style.cssText = `
            position: fixed;
            top: 130px;
            right: 10px;
            z-index: 1001;
            display: none;
        `;
        document.body.appendChild(panel);
    }
    
    toggleSettingsPanel() {
        const panel = document.getElementById('realtime-settings-panel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }
    
    loadSettings() {
        // Load settings from localStorage
        const settings = JSON.parse(localStorage.getItem('realtimeSettings') || '{}');
        
        if (settings.interval) {
            document.getElementById('update-interval').value = settings.interval;
        }
        if (settings.autoRefresh !== undefined) {
            document.getElementById('auto-refresh').checked = settings.autoRefresh;
        }
        if (settings.showNotifications !== undefined) {
            document.getElementById('show-notifications').checked = settings.showNotifications;
        }
        if (settings.updateCharts !== undefined) {
            document.getElementById('update-charts').checked = settings.updateCharts;
        }
        if (settings.dataSource) {
            document.getElementById('data-source').value = settings.dataSource;
        }
    }
    
    saveSettings() {
        const settings = {
            interval: document.getElementById('update-interval').value,
            autoRefresh: document.getElementById('auto-refresh').checked,
            showNotifications: document.getElementById('show-notifications').checked,
            updateCharts: document.getElementById('update-charts').checked,
            dataSource: document.getElementById('data-source').value
        };
        
        localStorage.setItem('realtimeSettings', JSON.stringify(settings));
        return settings;
    }
    
    applySettings() {
        const settings = this.saveSettings();
        
        // Apply to real-time updater
        if (window.realtimeUpdater) {
            if (settings.autoRefresh && settings.interval > 0) {
                window.realtimeUpdater.setUpdateInterval(parseInt(settings.interval));
                window.realtimeUpdater.isActive = true;
            } else {
                window.realtimeUpdater.stopAutoUpdate();
            }
        }
        
        // Show confirmation
        this.showNotification('Settings applied successfully!', 'success');
        this.toggleSettingsPanel();
    }
    
    resetSettings() {
        // Reset to defaults
        document.getElementById('update-interval').value = '30000';
        document.getElementById('auto-refresh').checked = true;
        document.getElementById('show-notifications').checked = true;
        document.getElementById('update-charts').checked = true;
        document.getElementById('data-source').value = 'mock';
        
        this.applySettings();
    }
    
    showNotification(message, type = 'info') {
        // Create notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1050;
            min-width: 300px;
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    
    updateLastUpdateTime() {
        const timeElement = document.getElementById('last-update-time');
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleTimeString();
        }
    }
}

// Initialize settings when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.realtimeSettings = new RealTimeSettings();
    
    // Update last update time when real-time updater runs
    if (window.realtimeUpdater) {
        const originalPerformUpdate = window.realtimeUpdater.performUpdate;
        window.realtimeUpdater.performUpdate = async function() {
            await originalPerformUpdate.call(this);
            if (window.realtimeSettings) {
                window.realtimeSettings.updateLastUpdateTime();
            }
        };
    }
});
