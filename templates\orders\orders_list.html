{% extends "base.html" %}

{% block title %}Orders Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-shopping-cart"></i> Orders Management
                            </h4>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('new_order') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> New Order
                            </a>
                            <a href="{{ url_for('workflow') }}" class="btn btn-info">
                                <i class="fas fa-tasks"></i> Workflow
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ url_for('orders') }}" class="form-inline">
                                <div class="input-group" style="width: 100%;">
                                    <input type="text" name="q" class="form-control" 
                                           placeholder="Search orders by ID, customer, product..." 
                                           value="{{ search_query }}" id="search-input">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        {% if search_query %}
                                        <a href="{{ url_for('orders') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter by Status
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{{ url_for('orders') }}">All Orders</a>
                                    <a class="dropdown-item" href="{{ url_for('orders') }}?status=Placed">Placed</a>
                                    <a class="dropdown-item" href="{{ url_for('orders') }}?status=Approved">Approved</a>
                                    <a class="dropdown-item" href="{{ url_for('orders') }}?status=Processing">Processing</a>
                                    <a class="dropdown-item" href="{{ url_for('orders') }}?status=Dispatched">Dispatched</a>
                                    <a class="dropdown-item" href="{{ url_for('orders') }}?status=Delivered">Delivered</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ orders|length }}</h5>
                                    <small>Total Orders</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ orders|selectattr('status', 'equalto', 'Placed')|list|length }}</h5>
                                    <small>Pending Orders</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5>{{ orders|selectattr('status', 'equalto', 'Processing')|list|length }}</h5>
                                    <small>Processing</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>{{ orders|selectattr('status', 'equalto', 'Delivered')|list|length }}</h5>
                                    <small>Delivered</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="orders-table">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Priority</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_id }}</strong>
                                        {% if order.priority == 'high' %}
                                        <span class="badge badge-danger">HIGH</span>
                                        {% elif order.priority == 'urgent' %}
                                        <span class="badge badge-warning">URGENT</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ order.customer_name }}</strong>
                                            {% if order.customer_id %}
                                            <br><small class="text-muted">{{ order.customer_id }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {{ order.order_date | format_datetime('%Y-%m-%d') }}
                                            <br><small class="text-muted">{{ order.order_date | format_datetime('%H:%M') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>₹{{ order.order_amount | format_currency }}</strong>
                                        {% if order.discount_amount and order.discount_amount > 0 %}
                                        <br><small class="text-success">Discount: ₹{{ order.discount_amount | format_currency }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.status == 'Placed' %}
                                        <span class="badge badge-warning">{{ order.status }}</span>
                                        {% elif order.status == 'Approved' %}
                                        <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == 'Processing' %}
                                        <span class="badge badge-primary">{{ order.status }}</span>
                                        {% elif order.status == 'Dispatched' %}
                                        <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% elif order.status == 'Delivered' %}
                                        <span class="badge badge-success">{{ order.status }}</span>
                                        {% elif order.status == 'Cancelled' %}
                                        <span class="badge badge-danger">{{ order.status }}</span>
                                        {% else %}
                                        <span class="badge badge-light">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.payment_status == 'paid' %}
                                        <span class="badge badge-success">Paid</span>
                                        {% elif order.payment_status == 'partial' %}
                                        <span class="badge badge-warning">Partial</span>
                                        {% else %}
                                        <span class="badge badge-danger">Unpaid</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.priority == 'urgent' %}
                                        <span class="badge badge-danger">Urgent</span>
                                        {% elif order.priority == 'high' %}
                                        <span class="badge badge-warning">High</span>
                                        {% else %}
                                        <span class="badge badge-secondary">Normal</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-toggle="dropdown">
                                                Actions
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="{{ url_for('view_order', order_id=order.order_id) }}">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('update_order', order_id=order.order_id) }}">
                                                    <i class="fas fa-edit"></i> Edit Order
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="{{ url_for('allocate_inventory_page', order_id=order.order_id) }}">
                                                    <i class="fas fa-boxes"></i> Allocate Inventory
                                                </a>
                                                {% if order.status in ['Approved', 'Processing'] %}
                                                <a class="dropdown-item" href="{{ url_for('generate_challan', order_id=order.order_id) }}">
                                                    <i class="fas fa-file-alt"></i> Generate Challan
                                                </a>
                                                {% endif %}
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="{{ url_for('view_invoice', order_id=order.order_id) }}">
                                                    <i class="fas fa-file-invoice"></i> View Invoice
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('view_order_history', order_id=order.order_id) }}">
                                                    <i class="fas fa-history"></i> Order History
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>No orders found
                                        {% if search_query %}
                                        for "{{ search_query }}"
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination would go here if needed -->
                    {% if orders|length >= 100 %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Showing latest 100 orders. Use search to find specific orders.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced search with suggestions
    const searchInput = document.getElementById('search-input');
    
    searchInput.addEventListener('input', function() {
        // Add real-time search suggestions here if needed
        // This could call an API endpoint for suggestions
    });

    // Make table sortable
    $('#orders-table').DataTable({
        "pageLength": 25,
        "order": [[ 2, "desc" ]], // Sort by date descending
        "columnDefs": [
            { "orderable": false, "targets": 7 } // Disable sorting on Actions column
        ]
    });
});
</script>
{% endblock %}
