{% extends "base.html" %}

{% block title %}Warehouse Order Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Warehouse Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">🏭 Warehouse Order Management</h2>
                    <p class="mb-0">Pack orders and prepare for rider pickup</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouse Stats -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ pending_orders|length }}</h1>
                    <h4>📦 Pending Orders</h4>
                    <small>Need to be packed</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ ready_orders|length }}</h1>
                    <h4>✅ Ready for Pickup</h4>
                    <small>Waiting for riders</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ ready_orders|selectattr('assigned_rider_id')|list|length }}</h1>
                    <h4>🚚 Assigned to Riders</h4>
                    <small>Picked up by riders</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Pending Packing -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">📦 Orders Pending Packing</h4>
                    <small>Orders that need to be packed and prepared</small>
                </div>
                <div class="card-body">
                    {% if pending_orders %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Order Date</th>
                                    <th>Status</th>
                                    <th>Instructions</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in pending_orders %}
                                <tr>
                                    <td><strong>{{ order.order_id }}</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(order.order_amount) }}</td>
                                    <td>{{ order.order_date|format_datetime }}</td>
                                    <td>
                                        <span class="badge badge-warning">{{ order.warehouse_status|title }}</span>
                                    </td>
                                    <td>{{ order.special_instructions or 'None' }}</td>
                                    <td>
                                        <button type="button" class="btn btn-success btn-sm" 
                                                onclick="packOrder('{{ order.order_id }}')">
                                            <i class="fas fa-box"></i> Pack Order
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h3>🎉 No Pending Orders</h3>
                        <p class="text-muted">All orders are packed and ready!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Ready for Pickup -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">✅ Orders Ready for Pickup</h4>
                    <small>Packed orders waiting for riders</small>
                </div>
                <div class="card-body">
                    {% if ready_orders %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Packed At</th>
                                    <th>Packed By</th>
                                    <th>Assigned Rider</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in ready_orders %}
                                <tr>
                                    <td><strong>{{ order.order_id }}</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(order.order_amount) }}</td>
                                    <td>{{ order.packed_at|format_datetime }}</td>
                                    <td>{{ order.packed_by }}</td>
                                    <td>
                                        {% if order.assigned_rider_id %}
                                            <span class="badge badge-info">{{ order.assigned_rider_id }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">Not Assigned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.assigned_rider_id %}
                                            <span class="badge badge-primary">Picked Up</span>
                                        {% else %}
                                            <span class="badge badge-success">Ready</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-truck fa-4x text-muted mb-3"></i>
                        <h3>📦 No Orders Ready</h3>
                        <p class="text-muted">Pack some orders to see them here.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">⚡ Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary btn-block" onclick="refreshOrders()">
                                <i class="fas fa-sync"></i> Refresh Orders
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('orders') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-list"></i> All Orders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('admin_rider_tracking') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-map-marker-alt"></i> Track Riders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-home"></i> Main Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pack Order Modal -->
<div class="modal fade" id="packModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h4 class="modal-title">📦 Pack Order</h4>
            </div>
            <div class="modal-body">
                <h4>Pack Order <span id="packOrderId"></span></h4>
                <p>Mark this order as packed and ready for rider pickup.</p>
                <div class="form-group">
                    <label>Packed By:</label>
                    <input type="text" class="form-control" id="packedBy" placeholder="Enter your name" required>
                </div>
                <div class="form-group">
                    <label>Packing Notes (optional):</label>
                    <textarea class="form-control" id="packingNotes" rows="3" placeholder="Any special packing notes..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmPackBtn">Mark as Packed</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentOrderId = '';

function packOrder(orderId) {
    currentOrderId = orderId;
    document.getElementById('packOrderId').textContent = orderId;
    $('#packModal').modal('show');
}

document.getElementById('confirmPackBtn').addEventListener('click', function() {
    const packedBy = document.getElementById('packedBy').value;
    const packingNotes = document.getElementById('packingNotes').value;
    
    if (!packedBy.trim()) {
        alert('Please enter who packed the order');
        return;
    }
    
    const formData = new FormData();
    formData.append('order_id', currentOrderId);
    formData.append('packed_by', packedBy);
    formData.append('packing_notes', packingNotes);
    
    fetch('/warehouse/pack_order', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            $('#packModal').modal('hide');
            alert('✅ Order packed successfully!');
            location.reload();
        } else {
            alert('❌ Error packing order');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Error packing order');
    });
});

function refreshOrders() {
    location.reload();
}

// Auto-refresh every 3 minutes
setInterval(function() {
    location.reload();
}, 180000);
</script>
{% endblock %}
