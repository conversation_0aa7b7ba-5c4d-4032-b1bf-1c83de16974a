<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Invoice - Medivent ERP</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4 mt-4">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-file-invoice text-primary"></i>
                Generate Invoice
            </h1>
            <div class="btn-group">
                <a href="/finance" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Back to Finance
                </a>
            </div>
        </div>

        <!-- Pending Orders -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i> Pending Orders for Invoice Generation
                </h6>
            </div>
            <div class="card-body">
                {% if pending_orders %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Order Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in pending_orders %}
                            <tr>
                                <td>{{ order.order_id }}</td>
                                <td>{{ order.customer_name }}</td>
                                <td>{{ order.order_date }}</td>
                                <td>{{ "{:,.0f}".format(order.order_amount) }}</td>
                                <td>
                                    <span class="badge badge-warning">{{ order.status }}</span>
                                </td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="generateInvoice('{{ order.order_id }}')">
                                        <i class="fas fa-file-invoice"></i> Generate Invoice
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle"></i>
                    <h4>No Pending Orders</h4>
                    <p>All orders have been invoiced or are not ready for invoicing.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function generateInvoice(orderId) {
            if (confirm('Generate invoice for order ' + orderId + '?')) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/finance/generate-invoice-action';

                const orderInput = document.createElement('input');
                orderInput.type = 'hidden';
                orderInput.name = 'order_id';
                orderInput.value = orderId;

                form.appendChild(orderInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
