<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Ultra Modern Customer Ledger & Aging Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #2563eb;
            --secondary-blue: #1e40af;
            --accent-blue: #3b82f6;
            --light-blue: #dbeafe;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --shadow-light: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .glass-container {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
            padding: 2rem;
            margin: 1rem;
        }

        .ultra-modern-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .ultra-modern-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--glass-border);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .aging-chart {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--glass-border);
        }

        .filter-section {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--glass-border);
        }

        .customer-table {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
        }

        .table {
            margin: 0;
            background: transparent;
        }

        .table thead th {
            background: rgba(37, 99, 235, 0.1);
            border: none;
            color: #1e40af;
            font-weight: 600;
            padding: 1rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table tbody td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table tbody tr:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .risk-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .risk-high { background: var(--gradient-danger); color: white; }
        .risk-medium { background: var(--gradient-warning); color: white; }
        .risk-low { background: var(--gradient-success); color: white; }
        .risk-none { background: rgba(108, 117, 125, 0.2); color: #6c757d; }

        .btn-ultra-modern {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .btn-ultra-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: #333;
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
            color: #333;
        }

        .aging-bar {
            height: 8px;
            border-radius: 4px;
            background: var(--gradient-primary);
            margin: 0.5rem 0;
            position: relative;
            overflow: hidden;
        }

        .aging-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .collection-meter {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(var(--primary-blue) 0deg, var(--primary-blue) calc(var(--percentage) * 3.6deg), rgba(255,255,255,0.2) calc(var(--percentage) * 3.6deg));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1) rotate(90deg);
            color: white;
        }

        @media (max-width: 768px) {
            .glass-container { margin: 0.5rem; padding: 1rem; }
            .stats-grid { grid-template-columns: 1fr; }
            .table-responsive { border-radius: 16px; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Ultra Modern Header -->
        <div class="ultra-modern-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0"><i class="fas fa-chart-line me-3"></i>Ultra Modern Customer Ledger & Aging Analysis</h1>
                    <p class="mb-0 mt-2 opacity-75">Comprehensive financial overview with advanced aging analysis</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('new_modern_finance_dashboard') }}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Finance
                        </a>
                        <button class="btn btn-light" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Export Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-primary mb-1">{{ "{:,}".format(summary.total_customers) }}</h3>
                        <p class="text-muted mb-0">Total Customers</p>
                    </div>
                    <div class="text-primary fs-1"><i class="fas fa-users"></i></div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-success mb-1">{{ "{:,}".format(summary.active_customers) }}</h3>
                        <p class="text-muted mb-0">Active Customers</p>
                    </div>
                    <div class="text-success fs-1"><i class="fas fa-user-check"></i></div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-warning mb-1">₹{{ "{:,.0f}".format(summary.total_outstanding) }}</h3>
                        <p class="text-muted mb-0">Total Outstanding</p>
                    </div>
                    <div class="text-warning fs-1"><i class="fas fa-exclamation-triangle"></i></div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-info mb-1">{{ "{:.1f}%".format(summary.collection_percentage) }}</h3>
                        <p class="text-muted mb-0">Collection Rate</p>
                    </div>
                    <div class="collection-meter" style="--percentage: {{ summary.collection_percentage }}">
                        {{ "{:.0f}%".format(summary.collection_percentage) }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Aging Analysis Chart -->
        <div class="aging-chart">
            <h4 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Aging Analysis Overview</h4>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-success">Current (0-30 days)</h5>
                        <h3 class="text-success">₹{{ "{:,.0f}".format(aging_summary.current_0_30) }}</h3>
                        <div class="aging-bar" style="background: var(--gradient-success);"></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-info">31-60 days</h5>
                        <h3 class="text-info">₹{{ "{:,.0f}".format(aging_summary.days_31_60) }}</h3>
                        <div class="aging-bar" style="background: var(--gradient-primary);"></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-warning">61-90 days</h5>
                        <h3 class="text-warning">₹{{ "{:,.0f}".format(aging_summary.days_61_90) }}</h3>
                        <div class="aging-bar" style="background: var(--gradient-warning);"></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5 class="text-danger">Over 90 days</h5>
                        <h3 class="text-danger">₹{{ "{:,.0f}".format(aging_summary.over_90_days) }}</h3>
                        <div class="aging-bar" style="background: var(--gradient-danger);"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Search Customer</label>
                    <input type="text" class="form-control" name="search" value="{{ filters.search }}" 
                           placeholder="Name, ID, Phone...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Risk Level</label>
                    <select class="form-select" name="risk_level">
                        <option value="all" {% if filters.risk_level == 'all' %}selected{% endif %}>All Levels</option>
                        <option value="high" {% if filters.risk_level == 'high' %}selected{% endif %}>High Risk</option>
                        <option value="medium" {% if filters.risk_level == 'medium' %}selected{% endif %}>Medium Risk</option>
                        <option value="low" {% if filters.risk_level == 'low' %}selected{% endif %}>Low Risk</option>
                        <option value="none" {% if filters.risk_level == 'none' %}selected{% endif %}>No Risk</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Outstanding</label>
                    <select class="form-select" name="outstanding">
                        <option value="all" {% if filters.outstanding == 'all' %}selected{% endif %}>All Amounts</option>
                        <option value="with_outstanding" {% if filters.outstanding == 'with_outstanding' %}selected{% endif %}>With Outstanding</option>
                        <option value="no_outstanding" {% if filters.outstanding == 'no_outstanding' %}selected{% endif %}>No Outstanding</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-ultra-modern d-block w-100">
                        <i class="fas fa-filter me-2"></i>Apply Filters
                    </button>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <a href="{{ url_for('new_modern_customer_ledger') }}" class="btn btn-outline-secondary d-block w-100">
                        <i class="fas fa-times me-2"></i>Clear Filters
                    </a>
                </div>
            </form>
        </div>

        <!-- Customer Ledger Table -->
        <div class="customer-table">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Customer Details</th>
                            <th>Financial Metrics</th>
                            <th>Risk Analysis</th>
                            <th>Aging Breakdown</th>
                            <th>Collection %</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers_data %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px; font-weight: 600;">
                                            {{ customer.name[:2].upper() }}
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 fw-bold">{{ customer.name }}</h6>
                                        <small class="text-muted">{{ customer.customer_id }}</small><br>
                                        <small class="text-muted">{{ customer.phone or 'No phone' }}</small><br>
                                        <small class="text-muted">{{ customer.category or 'No category' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="small">
                                    <div class="d-flex justify-content-between">
                                        <span>Total Revenue:</span>
                                        <strong class="text-success">₹{{ "{:,.0f}".format(customer.total_revenue) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Outstanding:</span>
                                        <strong class="text-warning">₹{{ "{:,.0f}".format(customer.total_outstanding) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Orders:</span>
                                        <strong>{{ customer.total_orders }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Avg Order:</span>
                                        <strong>₹{{ "{:,.0f}".format(customer.avg_order_value) }}</strong>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="risk-badge risk-{{ customer.risk_level }}">
                                        {{ customer.risk_level.upper() }}
                                    </span>
                                    <div class="mt-2 small">
                                        {% if customer.days_since_last_order %}
                                            <div class="text-muted">
                                                Last order: {{ "{:.0f}".format(customer.days_since_last_order) }} days ago
                                            </div>
                                        {% else %}
                                            <div class="text-muted">No orders yet</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="small">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-success">0-30:</span>
                                        <strong>₹{{ "{:,.0f}".format(customer.current_0_30) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-info">31-60:</span>
                                        <strong>₹{{ "{:,.0f}".format(customer.days_31_60) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-warning">61-90:</span>
                                        <strong>₹{{ "{:,.0f}".format(customer.days_61_90) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-danger">90+:</span>
                                        <strong>₹{{ "{:,.0f}".format(customer.over_90_days) }}</strong>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="collection-meter" style="--percentage: {{ customer.collection_percentage }}">
                                        {{ "{:.0f}%".format(customer.collection_percentage) }}
                                    </div>
                                    <div class="mt-2 small text-muted">
                                        Collection Rate
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column gap-1">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewCustomerDetails('{{ customer.customer_id }}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="recordPayment('{{ customer.customer_id }}')">
                                        <i class="fas fa-money-bill"></i> Payment
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="sendReminder('{{ customer.customer_id }}')">
                                        <i class="fas fa-bell"></i> Remind
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if not customers_data %}
            <div class="text-center py-5">
                <i class="fas fa-search fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">No customers found</h4>
                <p class="text-muted">Try adjusting your filters or search criteria</p>
            </div>
            {% endif %}
        </div>

        <!-- Risk Summary Cards -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="text-center">
                        <h3 class="text-danger">{{ summary.high_risk_count }}</h3>
                        <p class="text-muted mb-0">High Risk Customers</p>
                        <div class="risk-badge risk-high mt-2">Immediate Attention</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="text-center">
                        <h3 class="text-warning">{{ summary.medium_risk_count }}</h3>
                        <p class="text-muted mb-0">Medium Risk Customers</p>
                        <div class="risk-badge risk-medium mt-2">Monitor Closely</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="text-center">
                        <h3 class="text-success">{{ summary.low_risk_count }}</h3>
                        <p class="text-muted mb-0">Low Risk Customers</p>
                        <div class="risk-badge risk-low mt-2">Good Standing</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="text-center">
                        <h3 class="text-secondary">{{ summary.no_risk_count }}</h3>
                        <p class="text-muted mb-0">No Risk Customers</p>
                        <div class="risk-badge risk-none mt-2">Excellent</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="floating-action" onclick="refreshData()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCustomerDetails(customerId) {
            // Implement customer details modal or redirect
            alert('View customer details for: ' + customerId);
        }

        function recordPayment(customerId) {
            // Implement payment recording functionality
            alert('Record payment for customer: ' + customerId);
        }

        function sendReminder(customerId) {
            // Implement reminder sending functionality
            if (confirm('Send payment reminder to customer ' + customerId + '?')) {
                alert('Reminder sent successfully!');
            }
        }

        function exportData() {
            // Implement data export functionality
            alert('Exporting customer ledger data...');
        }

        function refreshData() {
            location.reload();
        }

        // Auto-refresh every 5 minutes
        setInterval(refreshData, 300000);

        // Add loading animation
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .customer-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
