{% extends 'base.html' %}

{% block title %}Daily Sales Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Daily Sales Report</h4>
                    <div>
                        <button class="btn btn-light" id="printDailyBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('daily_sales_report_new') }}" method="get" class="form-inline">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Date</span>
                                    </div>
                                    <input type="date" name="date" class="form-control" value="{{ selected_date }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Apply</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-day"></i> Showing sales for {{ selected_date }}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ summary.order_count or 0 }}</h1>
                                    <p class="mb-0">Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ "%.2f"|format(summary.total_sales or 0) }}</h1>
                                    <p class="mb-0">Total Sales (PKR)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ "%.2f"|format(summary.average_order or 0) }}</h1>
                                    <p class="mb-0">Average Order (PKR)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ summary.customer_count or 0 }}</h1>
                                    <p class="mb-0">Customers</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <h5>Orders</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Time</th>
                                            <th>Amount</th>
                                            <th>Items</th>
                                            <th>Status</th>
                                            <th>Sales Agent</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if sales_data %}
                                            {% for order in sales_data %}
                                            <tr>
                                                <td>{{ order.order_id }}</td>
                                                <td>{{ order.customer_name }}</td>
                                                <td>{{ order.order_date }}</td>
                                                <td>{{ "%.2f"|format(order.order_amount) }}</td>
                                                <td>{{ order.item_count }}</td>
                                                <td>
                                                    <span class="badge
                                                        {% if order.status == 'Delivered' %}badge-success
                                                        {% elif order.status == 'Dispatched' %}badge-info
                                                        {% elif order.status == 'Approved' %}badge-primary
                                                        {% elif order.status == 'Placed' %}badge-secondary
                                                        {% else %}badge-warning
                                                        {% endif %}">
                                                        {{ order.status }}
                                                    </span>
                                                </td>
                                                <td>{{ order.sales_agent }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center">No orders found for this date</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>Product Breakdown</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>Sales</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if product_breakdown %}
                                            {% for product in product_breakdown %}
                                            <tr>
                                                <td>{{ product.product_name }}</td>
                                                <td>{{ product.total_quantity }}</td>
                                                <td>{{ "%.2f"|format(product.total_sales) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center">No products sold on this date</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printDailyBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}