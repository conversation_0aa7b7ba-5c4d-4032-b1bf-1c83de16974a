{% extends 'base.html' %}

{% block title %}Finance - Medivent Pharmaceuticals{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Finance & Accounts</h1>
        <div class="btn-group">
            <a href="{{ url_for('finance_pending_invoices') }}" class="d-none d-sm-inline-block btn btn-sm btn-warning shadow-sm">
                <i class="fas fa-clock fa-sm text-white-50"></i> Pending Invoices
            </a>
            <a href="{{ url_for('finance_payment_collection') }}" class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm">
                <i class="fas fa-money-bill-wave fa-sm text-white-50"></i> Payment Collection
            </a>
            <a href="{{ url_for('finance_customer_ledger') }}" class="d-none d-sm-inline-block btn btn-sm btn-info shadow-sm">
                <i class="fas fa-users fa-sm text-white-50"></i> Customer Ledger
            </a>
            <a href="{{ url_for('finance_bank_details') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-university fa-sm text-white-50"></i> Bank Details
            </a>
            <a href="{{ url_for('finance_financial_reports') }}" class="d-none d-sm-inline-block btn btn-sm btn-dark shadow-sm">
                <i class="fas fa-chart-bar fa-sm text-white-50"></i> Reports
            </a>
        </div>
    </div>

    <!-- Search Bar -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Search</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('finance') }}">
                <div class="form-row">
                    <div class="col-md-4 mb-3">
                        <label for="q">Search Term</label>
                        <input type="text" class="form-control" id="q" name="q" placeholder="Invoice number, customer name, etc." value="{{ request.args.get('q', '') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="invoice_number">Invoice Number</label>
                        <input type="text" class="form-control" id="invoice_number" name="invoice_number" placeholder="e.g. INV-00001" value="{{ request.args.get('invoice_number', '') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_range">Date Range</label>
                        <select class="form-control" id="date_range" name="date_range">
                            <option value="all" {% if request.args.get('date_range') == 'all' %}selected{% endif %}>All Time</option>
                            <option value="today" {% if request.args.get('date_range') == 'today' %}selected{% endif %}>Today</option>
                            <option value="yesterday" {% if request.args.get('date_range') == 'yesterday' %}selected{% endif %}>Yesterday</option>
                            <option value="this_week" {% if request.args.get('date_range') == 'this_week' %}selected{% endif %}>This Week</option>
                            <option value="last_week" {% if request.args.get('date_range') == 'last_week' %}selected{% endif %}>Last Week</option>
                            <option value="this_month" {% if request.args.get('date_range') == 'this_month' %}selected{% endif %}>This Month</option>
                            <option value="last_month" {% if request.args.get('date_range') == 'last_month' %}selected{% endif %}>Last Month</option>
                            <option value="custom" {% if request.args.get('date_range') == 'custom' %}selected{% endif %}>Custom Range</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </div>
                <div class="form-row custom-date-range" style="display: {% if request.args.get('date_range') == 'custom' %}flex{% else %}none{% endif %};">
                    <div class="col-md-3 mb-3">
                        <label for="start_date">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', '') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', '') }}">
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- NEW Finance Overview -->
    <div class="row">
        <!-- Pending Invoices -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Invoices</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.pending_invoices }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Receivables -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Receivables</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ "{:,.0f}".format(stats.total_receivables) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Collections -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Today's Collections</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ "{:,.0f}".format(stats.total_collections_today) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overdue Invoices -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Overdue Invoices</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.overdue_invoices }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Invoices -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Recent Invoices</h6>
            <a href="{{ url_for('finance_invoices') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-list"></i> View All
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in recent_invoices %}
                        <tr>
                            <td>{{ invoice.invoice_number }}</td>
                            <td>{{ invoice.customer_name }}</td>
                            <td>{{ invoice.date_generated|format_datetime }}</td>
                            <td>{{ invoice.order_amount|format_currency }}</td>
                            <td>
                                {% if invoice.payment_status == 'paid' %}
                                <span class="badge badge-success">Paid</span>
                                {% elif invoice.payment_status == 'partial' %}
                                <span class="badge badge-warning">Partial</span>
                                {% else %}
                                <span class="badge badge-danger">Unpaid</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('finance_invoice_details', invoice_number=invoice.invoice_number) }}"
                                   class="btn btn-sm btn-info" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('view_invoice', order_id=invoice.order_id) }}"
                                   class="btn btn-sm btn-primary" target="_blank" title="View Invoice">
                                    <i class="fas fa-file-invoice"></i>
                                </a>
                                {% if invoice.pdf_path %}
                                <a href="{{ url_for('download_invoice', invoice_id=invoice.invoice_number) }}"
                                   class="btn btn-sm btn-success" title="Download PDF">
                                    <i class="fas fa-download"></i>
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#date_range').change(function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').show();
            } else {
                $('.custom-date-range').hide();
            }
        });
    });
</script>
{% endblock %}
