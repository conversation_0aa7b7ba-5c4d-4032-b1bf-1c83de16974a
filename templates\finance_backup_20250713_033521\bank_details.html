{% extends "base.html" %}

{% block title %}Bank Details - Finance{% endblock %}

{% block head %}
<style>
.bank-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s;
    margin-bottom: 15px;
}
.bank-card:hover {
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
    transform: translateY(-2px);
}
.karachi-card {
    border-left-color: #28a745;
}
.lahore-card {
    border-left-color: #ffc107;
}
.common-card {
    border-left-color: #6f42c1;
}
.bank-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.account-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">🏦 Bank Details Management</h2>
                    <p class="mb-0">Customer reference for payment processing</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Bank Detail Button -->
    <div class="row mb-4">
        <div class="col-md-12">
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addBankModal">
                <i class="fas fa-plus"></i> Add New Bank Detail
            </button>
        </div>
    </div>

    <!-- Branch Tabs -->
    <div class="row">
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="branchTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="karachi-tab" data-toggle="tab" href="#karachi" role="tab">
                        <i class="fas fa-building"></i> Karachi Branch
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="lahore-tab" data-toggle="tab" href="#lahore" role="tab">
                        <i class="fas fa-building"></i> Lahore Branch
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="common-tab" data-toggle="tab" href="#common" role="tab">
                        <i class="fas fa-globe"></i> Common Banks
                    </a>
                </li>
            </ul>

            <div class="tab-content" id="branchTabsContent">
                <!-- Karachi Banks -->
                <div class="tab-pane fade show active" id="karachi" role="tabpanel">
                    <div class="row mt-3">
                        {% if karachi_banks %}
                            {% for bank in karachi_banks %}
                            <div class="col-md-6 mb-3">
                                <div class="card bank-card karachi-card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-university"></i> {{ bank.bank_name }}
                                            <span class="badge badge-success float-right">KHI</span>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="bank-info">
                                            <p class="mb-2"><strong>Branch:</strong> {{ bank.branch_name }}</p>
                                            <p class="mb-2"><strong>Account Title:</strong> {{ bank.account_title }}</p>
                                            <p class="mb-2"><strong>Account Number:</strong> 
                                                <span class="account-number">{{ bank.account_number }}</span>
                                            </p>
                                            {% if bank.iban_number %}
                                            <p class="mb-2"><strong>IBAN:</strong> 
                                                <span class="account-number">{{ bank.iban_number }}</span>
                                            </p>
                                            {% endif %}
                                            <p class="mb-2"><strong>Account Type:</strong> {{ bank.account_type|title }}</p>
                                            {% if bank.notes %}
                                            <p class="mb-0"><strong>Notes:</strong> {{ bank.notes }}</p>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">Added by {{ bank.created_by }} on {{ bank.created_at }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-md-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    No bank details available for Karachi branch
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Lahore Banks -->
                <div class="tab-pane fade" id="lahore" role="tabpanel">
                    <div class="row mt-3">
                        {% if lahore_banks %}
                            {% for bank in lahore_banks %}
                            <div class="col-md-6 mb-3">
                                <div class="card bank-card lahore-card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-university"></i> {{ bank.bank_name }}
                                            <span class="badge badge-warning float-right">LHE</span>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="bank-info">
                                            <p class="mb-2"><strong>Branch:</strong> {{ bank.branch_name }}</p>
                                            <p class="mb-2"><strong>Account Title:</strong> {{ bank.account_title }}</p>
                                            <p class="mb-2"><strong>Account Number:</strong> 
                                                <span class="account-number">{{ bank.account_number }}</span>
                                            </p>
                                            {% if bank.iban_number %}
                                            <p class="mb-2"><strong>IBAN:</strong> 
                                                <span class="account-number">{{ bank.iban_number }}</span>
                                            </p>
                                            {% endif %}
                                            <p class="mb-2"><strong>Account Type:</strong> {{ bank.account_type|title }}</p>
                                            {% if bank.notes %}
                                            <p class="mb-0"><strong>Notes:</strong> {{ bank.notes }}</p>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">Added by {{ bank.created_by }} on {{ bank.created_at }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-md-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    No bank details available for Lahore branch
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Common Banks -->
                <div class="tab-pane fade" id="common" role="tabpanel">
                    <div class="row mt-3">
                        {% if common_banks %}
                            {% for bank in common_banks %}
                            <div class="col-md-6 mb-3">
                                <div class="card bank-card common-card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-university"></i> {{ bank.bank_name }}
                                            <span class="badge badge-secondary float-right">ALL</span>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="bank-info">
                                            <p class="mb-2"><strong>Branch:</strong> {{ bank.branch_name }}</p>
                                            <p class="mb-2"><strong>Account Title:</strong> {{ bank.account_title }}</p>
                                            <p class="mb-2"><strong>Account Number:</strong> 
                                                <span class="account-number">{{ bank.account_number }}</span>
                                            </p>
                                            {% if bank.iban_number %}
                                            <p class="mb-2"><strong>IBAN:</strong> 
                                                <span class="account-number">{{ bank.iban_number }}</span>
                                            </p>
                                            {% endif %}
                                            <p class="mb-2"><strong>Account Type:</strong> {{ bank.account_type|title }}</p>
                                            {% if bank.notes %}
                                            <p class="mb-0"><strong>Notes:</strong> {{ bank.notes }}</p>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">Added by {{ bank.created_by }} on {{ bank.created_at }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-md-12">
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    No common bank details available
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Bank Detail Modal -->
<div class="modal fade" id="addBankModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Bank Detail</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ url_for('finance_add_bank_detail') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Bank Name *</label>
                                <input type="text" name="bank_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Branch Name</label>
                                <input type="text" name="branch_name" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Account Title *</label>
                                <input type="text" name="account_title" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Account Number *</label>
                                <input type="text" name="account_number" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>IBAN Number</label>
                                <input type="text" name="iban_number" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Branch Code *</label>
                                <select name="branch_code" class="form-control" required>
                                    <option value="">Select branch...</option>
                                    <option value="KHI">Karachi (KHI)</option>
                                    <option value="LHE">Lahore (LHE)</option>
                                    <option value="ALL">All Branches (ALL)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Account Type</label>
                                <select name="account_type" class="form-control">
                                    <option value="current">Current Account</option>
                                    <option value="savings">Savings Account</option>
                                    <option value="business">Business Account</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Bank Detail</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
