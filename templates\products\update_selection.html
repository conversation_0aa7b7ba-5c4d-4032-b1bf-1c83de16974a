{% extends 'base.html' %}

{% block title %}Select Product to Update - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
    .search-card {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }
    .filter-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .product-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .product-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 12px 12px 0 0;
        padding: 15px 20px;
    }
    .btn-update {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        border: none;
        color: white;
        font-weight: 600;
        border-radius: 8px;
        padding: 8px 20px;
        transition: all 0.3s ease;
    }
    .btn-update:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255,193,7,0.4);
        color: white;
    }
    .search-input {
        border-radius: 25px;
        border: 2px solid rgba(255,255,255,0.3);
        background: rgba(255,255,255,0.1);
        color: white;
        padding: 12px 20px;
    }
    .search-input::placeholder {
        color: rgba(255,255,255,0.7);
    }
    .search-input:focus {
        border-color: rgba(255,255,255,0.8);
        background: rgba(255,255,255,0.2);
        box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        color: white;
    }
    .filter-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 8px 12px;
    }
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .badge-stock {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
    }
    .stock-high { background-color: #28a745; }
    .stock-medium { background-color: #ffc107; }
    .stock-low { background-color: #fd7e14; }
    .stock-out { background-color: #dc3545; }
    .pagination-custom .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: none;
        color: #007bff;
    }
    .pagination-custom .page-item.active .page-link {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Enhanced Header -->
    <div class="search-card">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h2 class="mb-2">
                    <i class="fas fa-search"></i> Select Product to Update
                </h2>
                <p class="mb-0">Search and select a product to modify its information</p>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control search-input me-2" 
                           placeholder="Search by name, batch, manufacturer..." 
                           value="{{ request.args.get('search', '') }}">
                    <button type="submit" class="btn btn-light">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="filter-card">
        <form method="GET" id="filterForm">
            <input type="hidden" name="search" value="{{ request.args.get('search', '') }}">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label fw-bold">Category</label>
                    <select name="category" class="form-select filter-select" onchange="submitFilters()">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category }}" {% if request.args.get('category') == category %}selected{% endif %}>
                            {{ category }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Manufacturer</label>
                    <select name="manufacturer" class="form-select filter-select" onchange="submitFilters()">
                        <option value="">All Manufacturers</option>
                        {% for manufacturer in manufacturers %}
                        <option value="{{ manufacturer }}" {% if request.args.get('manufacturer') == manufacturer %}selected{% endif %}>
                            {{ manufacturer }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Stock Status</label>
                    <select name="stock_status" class="form-select filter-select" onchange="submitFilters()">
                        <option value="">All Stock Levels</option>
                        <option value="high" {% if request.args.get('stock_status') == 'high' %}selected{% endif %}>High Stock</option>
                        <option value="medium" {% if request.args.get('stock_status') == 'medium' %}selected{% endif %}>Medium Stock</option>
                        <option value="low" {% if request.args.get('stock_status') == 'low' %}selected{% endif %}>Low Stock</option>
                        <option value="out" {% if request.args.get('stock_status') == 'out' %}selected{% endif %}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Division</label>
                    <select name="division" class="form-select filter-select" onchange="submitFilters()">
                        <option value="">All Divisions</option>
                        {% for division in divisions %}
                        <option value="{{ division.name }}" {% if request.args.get('division') == division.name %}selected{% endif %}>
                            {{ division.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </form>
    </div>

    <!-- Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-primary">{{ stats.total_products }}</h4>
                <p class="mb-0">Total Products</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-success">{{ stats.in_stock }}</h4>
                <p class="mb-0">In Stock</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-warning">{{ stats.low_stock }}</h4>
                <p class="mb-0">Low Stock</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-danger">{{ stats.out_of_stock }}</h4>
                <p class="mb-0">Out of Stock</p>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="row">
        {% for product in products %}
        <div class="col-md-6 col-lg-4">
            <div class="product-card">
                <div class="product-header">
                    <h6 class="mb-1">{{ product.name }}</h6>
                    <small>{{ product.strength or 'N/A' }}</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Manufacturer</small>
                            <p class="mb-2">{{ product.manufacturer or 'N/A' }}</p>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Category</small>
                            <p class="mb-2">{{ product.category or 'N/A' }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Price</small>
                            <p class="mb-2">₨{{ product.unit_price or 0 }}</p>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Stock</small>
                            <p class="mb-2">
                                {% set stock = product.stock_quantity or 0 %}
                                {% set min_stock = product.min_stock_level or 0 %}
                                {% if stock == 0 %}
                                    <span class="badge badge-stock stock-out">Out of Stock</span>
                                {% elif stock <= min_stock %}
                                    <span class="badge badge-stock stock-low">Low ({{ stock }})</span>
                                {% elif stock <= min_stock * 2 %}
                                    <span class="badge badge-stock stock-medium">Medium ({{ stock }})</span>
                                {% else %}
                                    <span class="badge badge-stock stock-high">High ({{ stock }})</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <small class="text-muted">ID: {{ product.product_id }}</small>
                        <a href="{{ url_for('update_product', product_id=product.product_id) }}" 
                           class="btn btn-update">
                            <i class="fas fa-edit"></i> Update
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if pagination.pages > 1 %}
    <nav aria-label="Product pagination">
        <ul class="pagination pagination-custom justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('update_product_selection', page=pagination.prev_num, **request.args) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('update_product_selection', page=page_num, **request.args) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('update_product_selection', page=pagination.next_num, **request.args) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    <!-- No Results Message -->
    {% if not products %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No products found</h4>
        <p class="text-muted">Try adjusting your search criteria or filters</p>
        <a href="{{ url_for('update_product_selection') }}" class="btn btn-primary">
            <i class="fas fa-refresh"></i> Clear Filters
        </a>
    </div>
    {% endif %}
</div>

<script>
function submitFilters() {
    document.getElementById('filterForm').submit();
}

// Auto-submit search after typing stops
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
{% endblock %}
