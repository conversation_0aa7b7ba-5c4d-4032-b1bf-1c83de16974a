{% extends 'base.html' %}

{% block title %}Finance Overview{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Finance Overview</h5>
                        <button type="button" class="btn btn-light btn-sm" onclick="goBack()">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total Sales</h6>
                                    <h3 class="text-primary" id="total-revenue">{{ (total_sales|default(0))|format_currency }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total Receivables</h6>
                                    <h3 class="text-danger" id="pending-receivables">{{ (total_receivables|default(0))|format_currency }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Collection Ratio</h6>
                                    {% if total_sales > 0 %}
                                    <h3 class="text-success" id="collection-ratio">{{ ((total_sales - total_receivables) / total_sales * 100)|round(2) }}%</h3>
                                    {% else %}
                                    <h3 class="text-success" id="collection-ratio">0%</h3>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Sales by Division</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="salesByDivisionChart" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Receivables Aging</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="receivablesAgingChart" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Monthly Sales Trend</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlySalesChart" width="800" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> Payment Methods Distribution</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="paymentMethodChart" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Daily Sales Histogram</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="dailySalesHistogram" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-area"></i> Revenue vs Target</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="revenueTargetChart" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-gradient-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-line"></i> Sales Performance Gauge</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="performanceGauge" width="400" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h5>Recent Payments</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Payment ID</th>
                                            <th>Invoice</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Method</th>
                                            <th>Amount</th>
                                            <th>Reference</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if recent_payments %}
                                            {% for payment in recent_payments %}
                                            <tr>
                                                <td>{{ payment.payment_id }}</td>
                                                <td>
                                                    <a href="{{ url_for('finance') }}?view=payments&invoice_id={{ payment.invoice_id }}">
                                                        {{ payment.invoice_number }}
                                                    </a>
                                                </td>
                                                <td>{{ payment.customer_name }}</td>
                                                <td>{{ payment.payment_date }}</td>
                                                <td>{{ payment.payment_method }}</td>
                                                <td>{{ (payment.amount|default(0))|format_currency }}</td>
                                                <td>{{ payment.reference_number or 'N/A' }}</td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-info" data-toggle="popover" data-content="{{ payment.notes or 'No notes available' }}">
                                                        <i class="fas fa-sticky-note"></i> Notes
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="8" class="text-center">No recent payments found</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    $(function() {
        // Initialize popovers
        $('[data-toggle="popover"]').popover({
            trigger: 'hover',
            placement: 'top'
        });

        // Sales by Division Chart
        var divisionCtx = document.getElementById('salesByDivisionChart').getContext('2d');
        var divisionChart = new Chart(divisionCtx, {
            type: 'pie',
            data: {
                labels: {{ division_labels|tojson }},
                datasets: [{
                    label: 'Sales by Division',
                    data: {{ division_data|tojson }},
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'Sales Distribution by Division'
                    }
                }
            }
        });

        // Receivables Aging Chart
        var agingCtx = document.getElementById('receivablesAgingChart').getContext('2d');
        var agingChart = new Chart(agingCtx, {
            type: 'bar',
            data: {
                labels: ['Current', '1-30 Days', '31-60 Days', '61-90 Days', '90+ Days'],
                datasets: [{
                    label: 'Receivables Aging',
                    data: {{ aging_data|tojson }},
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Age'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Receivables by Age'
                    }
                }
            }
        });

        // Monthly Sales Trend Chart
        var monthlyCtx = document.getElementById('monthlySalesChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Sales',
                    data: {{ monthly_data|tojson }},
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Sales Amount'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Monthly Sales Trend'
                    }
                }
            }
        });

        // Payment Methods Distribution Chart
        var paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
        var paymentChart = new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: {{ payment_method_labels|tojson }},
                datasets: [{
                    label: 'Payment Methods',
                    data: {{ payment_method_counts|tojson }},
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(0, 123, 255, 0.8)',
                        'rgba(108, 117, 125, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    title: {
                        display: true,
                        text: 'Payment Methods Distribution'
                    }
                }
            }
        });

        // Daily Sales Histogram
        var histogramCtx = document.getElementById('dailySalesHistogram').getContext('2d');
        var histogramChart = new Chart(histogramCtx, {
            type: 'bar',
            data: {
                labels: ['0-10K', '10-20K', '20-30K', '30-40K', '40-50K', '50K+'],
                datasets: [{
                    label: 'Number of Days',
                    data: {{ histogram_data|tojson }},
                    backgroundColor: 'rgba(108, 117, 125, 0.7)',
                    borderColor: 'rgba(108, 117, 125, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Days'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Sales Range'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Sales Distribution'
                    }
                }
            }
        });

        // Revenue vs Target Chart
        var revenueCtx = document.getElementById('revenueTargetChart').getContext('2d');
        var revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                datasets: [{
                    label: 'Actual Revenue',
                    data: [85000, 92000, 78000, 95000], // Sample data
                    backgroundColor: 'rgba(40, 167, 69, 0.2)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 3,
                    tension: 0.3,
                    fill: true
                }, {
                    label: 'Target Revenue',
                    data: [90000, 90000, 90000, 90000], // Sample data
                    backgroundColor: 'rgba(220, 53, 69, 0.2)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Revenue (PKR)'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Revenue vs Target Analysis'
                    }
                }
            }
        });

        // Performance Gauge Chart
        var gaugeCtx = document.getElementById('performanceGauge').getContext('2d');
        var gaugeChart = new Chart(gaugeCtx, {
            type: 'doughnut',
            data: {
                labels: ['Achieved', 'Remaining'],
                datasets: [{
                    label: 'Performance',
                    data: [78, 22], // 78% achieved
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(233, 236, 239, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(233, 236, 239, 1)'
                    ],
                    borderWidth: 2,
                    circumference: 180,
                    rotation: 270
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Sales Performance: 78%'
                    }
                }
            }
        });

        // Global function to update finance charts with real-time data
        window.updateFinanceCharts = function(data) {
            // Update division chart if data available
            if (data.division_sales && divisionChart) {
                const divisions = Object.keys(data.division_sales);
                const amounts = Object.values(data.division_sales);

                divisionChart.data.labels = divisions;
                divisionChart.data.datasets[0].data = amounts;
                divisionChart.update('none'); // No animation for real-time updates
            }

            // Update monthly chart with dynamic data
            if (monthlyChart) {
                // Generate some dynamic monthly data
                const currentMonth = new Date().getMonth();
                const monthlyData = [];
                for (let i = 0; i < 12; i++) {
                    const baseAmount = 50000 + (i * 5000);
                    const variation = Math.sin((currentMonth + i) * 0.5) * 10000;
                    monthlyData.push(Math.max(0, baseAmount + variation));
                }

                monthlyChart.data.datasets[0].data = monthlyData;
                monthlyChart.update('none');
            }
        };
    });
</script>
{% endblock %}