{% extends 'base.html' %}

{% block title %}Inventory - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .status-toggle {
        text-decoration: none !important;
        cursor: pointer;
    }

    .status-toggle .badge {
        transition: all 0.3s ease;
    }

    .status-toggle:hover .badge {
        transform: scale(1.1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .status-toggle .badge {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    /* Make the badge wider for better visibility */
    .status-toggle .badge {
        min-width: 80px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Inventory Management</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('inventory') }}" method="get" class="form-inline">
                                <div class="input-group w-100">
                                    <input type="text" name="q" class="form-control" placeholder="Search by Product, Batch, Country, Manufacturer, or Division" value="{{ search_query }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            {% if has_permission('report_export') %}
                            <a href="{{ url_for('export_inventory_excel') }}" class="btn btn-secondary mr-2">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a>
                            {% endif %}
                            {% if has_permission('inventory_add') %}
                            <a href="{{ url_for('new_stock') }}" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> Add New Stock
                            </a>
                            {% endif %}
                            {% if has_permission('inventory_transfer') %}
                            <a href="{{ url_for('transfer_stock') }}" class="btn btn-info ml-2">
                                <i class="fas fa-exchange-alt"></i> Stock Transfer
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Batch #</th>
                                    <th>Country</th>
                                    <th>Manufacturer</th>
                                    <th>Division</th>
                                    <th>Expiry Date</th>
                                    <th>Warehouse</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inventory %}
                                <tr>
                                    <td>{{ item.product_name }}</td>
                                    <td>{{ item.strength }}</td>
                                    <td><strong>{{ item.batch_number }}</strong></td>
                                    <td>{{ item.country or 'N/A' }}</td>
                                    <td>{{ item.manufacturer or 'Generic' }}</td>
                                    <td>{{ item.division or 'Unassigned' }}</td>
                                    <td>{{ item.expiry_date }}</td>
                                    <td>{{ item.warehouse_name }}</td>
                                    <td>{{ item.stock_quantity }} {{ item.unit_of_measure }}</td>
                                    <td>
                                        {% if has_permission('inventory_adjust') %}
                                        <a href="{{ url_for('toggle_inventory_status', inventory_id=item.inventory_id) }}"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                {% if item.status == 'active' %}badge-success
                                                {% elif item.status == 'low' %}badge-warning
                                                {% elif item.status == 'expired' %}badge-danger
                                                {% elif item.status == 'deactive' %}badge-secondary
                                                {% else %}badge-secondary
                                                {% endif %}">
                                                {{ item.status }}
                                            </span>
                                        </a>
                                        {% else %}
                                        <span class="badge
                                            {% if item.status == 'active' %}badge-success
                                            {% elif item.status == 'low' %}badge-warning
                                            {% elif item.status == 'expired' %}badge-danger
                                            {% elif item.status == 'deactive' %}badge-secondary
                                            {% else %}badge-secondary
                                            {% endif %}">
                                            {{ item.status }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('inventory_history', inventory_id=item.inventory_id) }}" class="btn btn-sm btn-info">History</a>
                                            {% if has_permission('inventory_add') %}
                                            <a href="{{ url_for('new_stock') }}" class="btn btn-sm btn-success">Add Stock</a>
                                            {% endif %}
                                            {% if has_permission('inventory_transfer') %}
                                            <a href="{{ url_for('transfer_stock') }}" class="btn btn-sm btn-primary">Transfer</a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function(){
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}