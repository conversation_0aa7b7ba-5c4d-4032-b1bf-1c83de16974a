{% extends 'base.html' %}

{% block title %}System Settings - Medivent Pharmaceuticals{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">System Settings</h1>
        <div class="btn-group">
            <a href="{{ url_for('dashboard') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
            </a>
            <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm" data-toggle="modal" data-target="#backupModal">
                <i class="fas fa-database fa-sm text-white-50"></i> Backup Database
            </a>
        </div>
    </div>

    <!-- System Information -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <tbody>
                                <tr>
                                    <th>Database Size</th>
                                    <td>{{ system_info.database_size|round(2) }} MB</td>
                                </tr>
                                <tr>
                                    <th>Python Version</th>
                                    <td>{{ system_info.python_version }}</td>
                                </tr>
                                <tr>
                                    <th>Flask Version</th>
                                    <td>{{ system_info.flask_version }}</td>
                                </tr>
                                <tr>
                                    <th>Operating System</th>
                                    <td>{{ system_info.os_info }}</td>
                                </tr>
                                <tr>
                                    <th>Last Backup</th>
                                    <td>
                                        {% if system_info.last_backup and system_info.last_backup.value %}
                                            {{ system_info.last_backup.value }}
                                        {% else %}
                                            Never
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Role</th>
                                    <th>Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for role_count in user_counts %}
                                <tr>
                                    <td>{{ role_count.role|title }}</td>
                                    <td>{{ role_count.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Statistics -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Database Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for stat in db_stats %}
                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                {{ stat.table|title }}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stat.count }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-database fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Settings -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">System Settings</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Setting</th>
                            <th>Value</th>
                            <th>Description</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for setting in settings %}
                        <tr>
                            <td>{{ setting.setting_name }}</td>
                            <td>{{ setting.value }}</td>
                            <td>{{ setting.description }}</td>
                            <td>{{ setting.updated_at }}</td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-setting" 
                                        data-toggle="modal" 
                                        data-target="#editSettingModal"
                                        data-setting-name="{{ setting.setting_name }}"
                                        data-setting-value="{{ setting.value }}"
                                        data-setting-description="{{ setting.description }}">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Edit Setting Modal -->
<div class="modal fade" id="editSettingModal" tabindex="-1" role="dialog" aria-labelledby="editSettingModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSettingModalLabel">Edit Setting</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ url_for('update_settings') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="setting_name">Setting Name</label>
                        <input type="text" class="form-control" id="setting_name" name="setting_name" readonly>
                    </div>
                    <div class="form-group">
                        <label for="setting_value">Value</label>
                        <input type="text" class="form-control" id="setting_value" name="setting_value" required>
                    </div>
                    <div class="form-group">
                        <label for="setting_description">Description</label>
                        <textarea class="form-control" id="setting_description" readonly></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Backup Database Modal -->
<div class="modal fade" id="backupModal" tabindex="-1" role="dialog" aria-labelledby="backupModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backupModalLabel">Backup Database</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to create a backup of the database?</p>
                <p>This will create a copy of the current database in the backups directory.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="{{ url_for('backup_database') }}" class="btn btn-primary">Create Backup</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('.edit-setting').click(function() {
            var settingName = $(this).data('setting-name');
            var settingValue = $(this).data('setting-value');
            var settingDescription = $(this).data('setting-description');
            
            $('#setting_name').val(settingName);
            $('#setting_value').val(settingValue);
            $('#setting_description').val(settingDescription);
        });
    });
</script>
{% endblock %}
