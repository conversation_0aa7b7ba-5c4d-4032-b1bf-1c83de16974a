{% extends "base.html" %}

{% block title %}Warehouse Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-warehouse"></i> Warehouse Management & DC Generation
                    </h4>
                    <small>Manage warehouses, generate delivery challans, and track inventory</small>
                </div>
            </div>

            <!-- Warehouse Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Warehouses</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.total_warehouses }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active DCs</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.active_dcs }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Pending Dispatch</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.pending_dispatch }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Low Stock Items</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.low_stock_items }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-block" onclick="generateDC()">
                                        <i class="fas fa-file-alt"></i><br>
                                        Generate DC
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('inventory') }}" class="btn btn-info btn-block">
                                        <i class="fas fa-boxes"></i><br>
                                        View Inventory
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('delivery_challans') }}" class="btn btn-warning btn-block">
                                        <i class="fas fa-truck"></i><br>
                                        All DCs
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-success btn-block" onclick="addWarehouse()">
                                        <i class="fas fa-plus"></i><br>
                                        Add Warehouse
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('inventory') }}?reports=true" class="btn btn-secondary btn-block">
                                        <i class="fas fa-chart-bar"></i><br>
                                        Reports
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-dark btn-block" onclick="stockTransfer()">
                                        <i class="fas fa-exchange-alt"></i><br>
                                        Stock Transfer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warehouses List -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-list"></i> Warehouses</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Warehouse ID</th>
                                            <th>Name</th>
                                            <th>Location</th>
                                            <th>Manager</th>
                                            <th>Total Items</th>
                                            <th>Total Value</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for warehouse in warehouses %}
                                        <tr>
                                            <td><strong>{{ warehouse.warehouse_id }}</strong></td>
                                            <td>{{ warehouse.name }}</td>
                                            <td>
                                                <i class="fas fa-map-marker-alt"></i> 
                                                {{ warehouse.location }}
                                            </td>
                                            <td>{{ warehouse.manager_name or 'Not Assigned' }}</td>
                                            <td>
                                                <span class="badge badge-info">
                                                    {{ warehouse.total_items or 0 }}
                                                </span>
                                            </td>
                                            <td>₹{{ warehouse.total_value | format_currency }}</td>
                                            <td>
                                                <span class="badge badge-{% if warehouse.status == 'active' %}success{% else %}danger{% endif %}">
                                                    {{ warehouse.status|title }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-primary" onclick="viewWarehouse('{{ warehouse.warehouse_id }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-warning" onclick="editWarehouse('{{ warehouse.warehouse_id }}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-info" onclick="generateWarehouseDC('{{ warehouse.warehouse_id }}')">
                                                        <i class="fas fa-file-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Delivery Challans -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0"><i class="fas fa-truck"></i> Recent Delivery Challans</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>DC Number</th>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Warehouse</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dc in recent_dcs %}
                                        <tr>
                                            <td><strong>{{ dc.dc_number }}</strong></td>
                                            <td>{{ dc.order_id }}</td>
                                            <td>{{ dc.customer_name }}</td>
                                            <td>{{ dc.warehouse_name }}</td>
                                            <td>{{ dc.created_date | format_datetime('%Y-%m-%d') }}</td>
                                            <td>
                                                <span class="badge badge-{% if dc.status == 'delivered' %}success{% elif dc.status == 'dispatched' %}info{% elif dc.status == 'ready' %}warning{% else %}secondary{% endif %}">
                                                    {{ dc.status|title }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-primary" onclick="viewDC('{{ dc.dc_number }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-success" onclick="printDC('{{ dc.dc_number }}')">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="{{ url_for('delivery_challans') }}" class="btn btn-primary">
                                    View All Delivery Challans
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Low Stock Alert -->
            {% if low_stock_items %}
            <div class="row">
                <div class="col-12">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Low Stock Alert</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Warehouse</th>
                                            <th>Current Stock</th>
                                            <th>Reorder Level</th>
                                            <th>Action Required</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in low_stock_items %}
                                        <tr>
                                            <td>{{ item.product_name }}</td>
                                            <td>{{ item.warehouse_name }}</td>
                                            <td>
                                                <span class="badge badge-danger">
                                                    {{ item.current_stock }}
                                                </span>
                                            </td>
                                            <td>{{ item.reorder_level }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-warning" onclick="reorderStock('{{ item.product_id }}', '{{ item.warehouse_id }}')">
                                                    <i class="fas fa-shopping-cart"></i> Reorder
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Generate DC Modal -->
<div class="modal fade" id="generateDCModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Generate Delivery Challan</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="generateDCForm">
                    <div class="form-group">
                        <label for="dcOrderId">Order ID</label>
                        <select id="dcOrderId" class="form-control" required>
                            <option value="">Select Order</option>
                            {% for order in approved_orders %}
                            <option value="{{ order.order_id }}">{{ order.order_id }} - {{ order.customer_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="dcWarehouse">Warehouse</label>
                        <select id="dcWarehouse" class="form-control" required>
                            <option value="">Select Warehouse</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="dcNotes">Notes</label>
                        <textarea id="dcNotes" class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitGenerateDC()">Generate DC</button>
            </div>
        </div>
    </div>
</div>

<script>
// Generate DC
function generateDC() {
    $('#generateDCModal').modal('show');
}

function generateWarehouseDC(warehouseId) {
    document.getElementById('dcWarehouse').value = warehouseId;
    $('#generateDCModal').modal('show');
}

function submitGenerateDC() {
    const orderId = document.getElementById('dcOrderId').value;
    const warehouseId = document.getElementById('dcWarehouse').value;
    const notes = document.getElementById('dcNotes').value;
    
    if (!orderId || !warehouseId) {
        alert('Please select both order and warehouse');
        return;
    }
    
    // Submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/warehouse/generate-dc';
    
    const orderInput = document.createElement('input');
    orderInput.type = 'hidden';
    orderInput.name = 'order_id';
    orderInput.value = orderId;
    form.appendChild(orderInput);
    
    const warehouseInput = document.createElement('input');
    warehouseInput.type = 'hidden';
    warehouseInput.name = 'warehouse_id';
    warehouseInput.value = warehouseId;
    form.appendChild(warehouseInput);
    
    const notesInput = document.createElement('input');
    notesInput.type = 'hidden';
    notesInput.name = 'notes';
    notesInput.value = notes;
    form.appendChild(notesInput);
    
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token() }}';
    form.appendChild(csrfInput);
    
    document.body.appendChild(form);
    form.submit();
}

// Other functions
function viewWarehouse(warehouseId) {
    window.location.href = `/warehouse/${warehouseId}/view`;
}

function editWarehouse(warehouseId) {
    window.location.href = `/warehouse/${warehouseId}/edit`;
}

function addWarehouse() {
    window.location.href = '/warehouse/add';
}

function viewDC(dcNumber) {
    window.location.href = `/warehouse/dc/${dcNumber}/view`;
}

function printDC(dcNumber) {
    window.open(`/warehouse/dc/${dcNumber}/print`, '_blank');
}

function stockTransfer() {
    window.location.href = '/warehouse/stock-transfer';
}

function reorderStock(productId, warehouseId) {
    if (confirm('Create reorder request for this product?')) {
        window.location.href = `/warehouse/reorder/${productId}/${warehouseId}`;
    }
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);
</script>
{% endblock %}
