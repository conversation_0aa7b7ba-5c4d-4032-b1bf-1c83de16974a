"""
DateTime Helper Utilities
Formats dates and times consistently across the project
"""

from datetime import datetime

def format_datetime(dt):
    """
    Format datetime to 2025-05-03:04:35 format
    """
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        try:
            # Parse string datetime
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d:%H:%M')
    
    return str(dt)

def format_date_only(dt):
    """
    Format date to 2025-05-03 format
    """
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        try:
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d')
    
    return str(dt)

def format_time_only(dt):
    """
    Format time to 04:35 format
    """
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        try:
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%H:%M')
    
    return str(dt)

def get_current_formatted_datetime():
    """
    Get current datetime in project format
    """
    return format_datetime(datetime.now())

def get_current_formatted_date():
    """
    Get current date in project format
    """
    return format_date_only(datetime.now())

def get_current_formatted_time():
    """
    Get current time in project format
    """
    return format_time_only(datetime.now())
