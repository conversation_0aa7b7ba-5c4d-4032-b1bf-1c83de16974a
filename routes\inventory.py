#!/usr/bin/env python3
"""
Inventory Management Routes
Comprehensive inventory management with product validation
"""

from flask import Blueprint, render_template, request, jsonify, g, flash, redirect, url_for
from flask_login import login_required, current_user
from utils.inventory_validator import get_inventory_validator, get_products_with_inventory_for_forms
from utils.product_validator import get_product_validator
from utils.division_validator import get_division_validator
import sqlite3
import logging
import traceback
from datetime import datetime

inventory_bp = Blueprint('inventory', __name__, template_folder='../templates')

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
    return db


@inventory_bp.route('/')
@login_required
def index():
    """Inventory management dashboard"""
    try:
        db = get_db()
        inventory_validator = get_inventory_validator(db)
        
        # Get inventory summary
        summary = inventory_validator.get_inventory_summary()
        
        # Get low stock products
        low_stock_products = inventory_validator.get_low_stock_products()
        
        # Get recent inventory entries
        cursor = db.execute('''
            SELECT i.inventory_id, i.batch_number, i.stock_quantity, 
                   i.allocated_quantity, i.date_received, i.status,
                   p.name as product_name, p.strength,
                   d.name as division_name,
                   w.name as warehouse_name
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.status = 'active' AND d.status = 'Active'
            ORDER BY i.date_received DESC
            LIMIT 20
        ''')
        
        recent_inventory = cursor.fetchall()
        
        return render_template('inventory/index.html',
                             summary=summary,
                             low_stock_products=low_stock_products,
                             recent_inventory=recent_inventory)
        
    except Exception as e:
        flash(f'Error loading inventory dashboard: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))


@inventory_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_inventory():
    """Create new inventory entry with comprehensive validation"""
    if request.method == 'POST':
        try:
            db = get_db()
            
            # Get form data
            inventory_data = {
                'product_id': request.form.get('product_id', '').strip(),
                'batch_number': request.form.get('batch_number', '').strip(),
                'country_of_origin': request.form.get('country_of_origin', '').strip(),
                'manufacturing_date': request.form.get('manufacturing_date'),
                'expiry_date': request.form.get('expiry_date'),
                'stock_quantity': request.form.get('stock_quantity'),
                'allocated_quantity': request.form.get('allocated_quantity', 0),
                'warehouse_id': request.form.get('warehouse_id', '').strip(),
                'location_code': request.form.get('location_code', '').strip()
            }
            
            # Validate inventory data
            inventory_validator = get_inventory_validator(db)
            is_valid, errors = inventory_validator.validate_inventory_creation_data(inventory_data)
            
            if not is_valid:
                for error in errors:
                    flash(error, 'danger')
                return redirect(url_for('inventory.new_inventory'))
            
            # Generate inventory ID
            inventory_id = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # Begin transaction
            db.execute('BEGIN TRANSACTION')
            
            # Insert inventory
            db.execute('''
                INSERT INTO inventory (
                    inventory_id, product_id, batch_number, country_of_origin,
                    manufacturing_date, expiry_date, stock_quantity, allocated_quantity,
                    warehouse_id, location_code, status, date_received, received_by,
                    last_updated, updated_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                inventory_id,
                inventory_data['product_id'],
                inventory_data['batch_number'],
                inventory_data['country_of_origin'],
                inventory_data['manufacturing_date'],
                inventory_data['expiry_date'],
                int(inventory_data['stock_quantity']),
                int(inventory_data['allocated_quantity']),
                inventory_data['warehouse_id'],
                inventory_data['location_code'],
                'active',
                datetime.now(),
                current_user.username,
                datetime.now(),
                current_user.username
            ))
            
            # Log stock movement
            movement_id = f"MOV{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            db.execute('''
                INSERT INTO stock_movements (
                    movement_id, inventory_id, product_id, batch_number,
                    quantity, movement_type, movement_date, moved_by, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                movement_id,
                inventory_id,
                inventory_data['product_id'],
                inventory_data['batch_number'],
                int(inventory_data['stock_quantity']),
                'receipt',
                datetime.now(),
                current_user.username,
                f"Initial stock receipt - {inventory_data['batch_number']}"
            ))
            
            # Commit transaction
            db.execute('COMMIT')
            
            flash(f'Inventory {inventory_id} created successfully!', 'success')
            return redirect(url_for('inventory.view_inventory', inventory_id=inventory_id))
            
        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating inventory: {str(e)}', 'danger')
            return redirect(url_for('inventory.new_inventory'))
    
    # GET request - show inventory form
    try:
        db = get_db()
        product_validator = get_product_validator(db)
        
        # Get products eligible for inventory creation (with valid divisions)
        products = product_validator.get_products_for_inventory_creation()
        
        if not products:
            flash('No products with valid divisions found. Please register products first.', 'warning')
            return redirect(url_for('products.new_product'))
        
        # Get warehouses
        cursor = db.execute('SELECT * FROM warehouses WHERE status = "active" ORDER BY name')
        warehouses = cursor.fetchall()
        
        return render_template('inventory/new.html', 
                             products=products, 
                             warehouses=warehouses)
        
    except Exception as e:
        flash(f'Error loading inventory form: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/<inventory_id>')
@login_required
def view_inventory(inventory_id):
    """View inventory details"""
    try:
        db = get_db()
        
        # Get inventory details with product and division info
        cursor = db.execute('''
            SELECT i.*, p.name as product_name, p.strength, p.manufacturer,
                   d.name as division_name, d.category as division_category,
                   w.name as warehouse_name, w.address as warehouse_address
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.inventory_id = ?
        ''', (inventory_id,))
        
        inventory = cursor.fetchone()
        
        if not inventory:
            flash(f'Inventory {inventory_id} not found', 'danger')
            return redirect(url_for('inventory.index'))
        
        # Get stock movements for this inventory
        cursor = db.execute('''
            SELECT * FROM stock_movements
            WHERE inventory_id = ?
            ORDER BY movement_date DESC
        ''', (inventory_id,))
        
        movements = cursor.fetchall()
        
        return render_template('inventory/view.html', 
                             inventory=inventory, 
                             movements=movements)
        
    except Exception as e:
        flash(f'Error viewing inventory: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/product/<product_id>')
@login_required
def product_inventory(product_id):
    """View all inventory for a specific product"""
    try:
        db = get_db()
        product_validator = get_product_validator(db)
        inventory_validator = get_inventory_validator(db)
        
        # Validate product exists and has valid division
        is_valid, product_info = product_validator.validate_product_exists(product_id)
        
        if not is_valid:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('inventory.index'))
        
        if not product_info.get('division_valid', False):
            flash(f'Product {product_id} does not have a valid division assignment', 'warning')
            return redirect(url_for('inventory.index'))
        
        # Get inventory for this product
        inventory_entries = inventory_validator.get_inventory_by_product(product_id)
        
        # Get available stock
        available_stock = inventory_validator.get_available_stock(product_id)
        
        return render_template('inventory/product_inventory.html',
                             product_info=product_info,
                             inventory_entries=inventory_entries,
                             available_stock=available_stock)
        
    except Exception as e:
        flash(f'Error viewing product inventory: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/low-stock')
@login_required
def low_stock():
    """View low stock products"""
    try:
        db = get_db()
        inventory_validator = get_inventory_validator(db)
        
        # Get low stock products
        low_stock_products = inventory_validator.get_low_stock_products()
        
        return render_template('inventory/low_stock.html',
                             low_stock_products=low_stock_products)
        
    except Exception as e:
        flash(f'Error loading low stock report: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/api/products-for-inventory')
@login_required
def api_products_for_inventory():
    """API endpoint to get products eligible for inventory creation"""
    try:
        db = get_db()
        products = get_products_with_inventory_for_forms(db)
        
        return jsonify({
            'success': True,
            'products': products
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@inventory_bp.route('/api/validate-product/<product_id>')
@login_required
def api_validate_product(product_id):
    """API endpoint to validate product for inventory creation"""
    try:
        db = get_db()
        product_validator = get_product_validator(db)
        
        is_valid, product_info = product_validator.validate_product_exists(product_id)
        
        return jsonify({
            'success': True,
            'valid': is_valid,
            'product_info': product_info,
            'can_create_inventory': product_info.get('division_valid', False) if product_info else False
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
