{% extends "base.html" %}

{% block title %}Aging Analysis - Finance{% endblock %}

{% block content %}
<style>
    .finance-dashboard {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .finance-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .finance-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .finance-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        margin-bottom: 0;
    }
    
    .aging-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }
    
    .aging-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }
    
    .aging-current::before {
        background: linear-gradient(90deg, #28a745, #20c997);
    }
    
    .aging-30::before {
        background: linear-gradient(90deg, #ffc107, #fd7e14);
    }
    
    .aging-60::before {
        background: linear-gradient(90deg, #fd7e14, #e83e8c);
    }
    
    .aging-90::before {
        background: linear-gradient(90deg, #dc3545, #6f42c1);
    }
    
    .aging-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .aging-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .customer-info {
        flex: 1;
    }
    
    .customer-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 5px;
    }
    
    .customer-details {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .aging-summary {
        text-align: right;
    }
    
    .total-outstanding {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .days-overdue {
        font-size: 0.9rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
    }
    
    .overdue-current {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }
    
    .overdue-30 {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
    }
    
    .overdue-60 {
        background: rgba(255, 152, 0, 0.2);
        color: #e65100;
    }
    
    .overdue-90 {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
    }
    
    .aging-breakdown {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin: 20px 0;
    }
    
    .aging-bucket {
        text-align: center;
        padding: 15px;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .bucket-amount {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .bucket-label {
        font-size: 0.8rem;
        color: var(--muted);
        font-weight: 600;
    }
    
    .bucket-current .bucket-amount {
        color: #28a745;
    }
    
    .bucket-30 .bucket-amount {
        color: #ffc107;
    }
    
    .bucket-60 .bucket-amount {
        color: #fd7e14;
    }
    
    .bucket-90 .bucket-amount {
        color: #dc3545;
    }
    
    .invoice-list {
        margin-top: 15px;
    }
    
    .invoice-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .invoice-item:last-child {
        border-bottom: none;
    }
    
    .invoice-info {
        flex: 1;
    }
    
    .invoice-number {
        font-weight: 600;
        color: var(--primary);
    }
    
    .invoice-date {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .invoice-amount {
        font-weight: 600;
        color: #dc3545;
    }
    
    .filter-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        text-align: center;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin: 0 auto 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: var(--dark);
    }
    
    .stat-label {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .btn-modern {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .btn-modern:hover {
        transform: translateY(-1px);
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-top: 15px;
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-chart-bar me-3"></i>Aging Analysis
                    </h1>
                    <p class="finance-subtitle">Outstanding amount analysis with aging-wise categorization and visual indicators</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="backToLedger()">
                        <i class="fas fa-arrow-left me-2"></i>Back to Ledger
                    </button>
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-modern" onclick="exportAging()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <button class="btn btn-light btn-modern" onclick="generateAgingReport()">
                        <i class="fas fa-file-pdf me-2"></i>Generate Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Aging Filters
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" name="customer" 
                           value="{{ filters.customer if filters else '' }}" placeholder="Customer name">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Aging Bucket</label>
                    <select class="form-control" name="aging_bucket">
                        <option value="all" {{ 'selected' if filters and filters.aging_bucket == 'all' else '' }}>All Buckets</option>
                        <option value="current" {{ 'selected' if filters and filters.aging_bucket == 'current' else '' }}>0-30 Days</option>
                        <option value="30_days" {{ 'selected' if filters and filters.aging_bucket == '30_days' else '' }}>31-60 Days</option>
                        <option value="60_days" {{ 'selected' if filters and filters.aging_bucket == '60_days' else '' }}>61-90 Days</option>
                        <option value="90_plus" {{ 'selected' if filters and filters.aging_bucket == '90_plus' else '' }}>90+ Days</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Min Amount</label>
                    <input type="number" class="form-control" name="min_amount" 
                           value="{{ filters.min_amount if filters else '' }}" placeholder="0">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Salesperson</label>
                    <input type="text" class="form-control" name="salesperson" 
                           value="{{ filters.salesperson if filters else '' }}" placeholder="Salesperson">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Division</label>
                    <input type="text" class="form-control" name="division" 
                           value="{{ filters.division if filters else '' }}" placeholder="Division">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-modern d-block w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.current_amount if summary and summary.current_amount else 0) }}</div>
                    <div class="stat-label">0-30 Days</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.days_30_amount if summary and summary.days_30_amount else 0) }}</div>
                    <div class="stat-label">31-60 Days</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14, #e83e8c);">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.days_60_amount if summary and summary.days_60_amount else 0) }}</div>
                    <div class="stat-label">61-90 Days</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #6f42c1);">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.days_90_plus_amount if summary and summary.days_90_plus_amount else 0) }}</div>
                    <div class="stat-label">90+ Days</div>
                </div>
            </div>
        </div>

        <!-- Aging Analysis List -->
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4 text-white">
                    <i class="fas fa-list me-2"></i>Customer Aging Analysis
                    <span class="badge bg-light text-dark ms-2">{{ aging_data|length if aging_data else 0 }} Customers</span>
                </h4>

                {% if aging_data %}
                    {% for customer in aging_data %}
                    <div class="aging-card aging-{{ customer.primary_bucket or 'current' }}">
                        <div class="aging-header">
                            <div class="customer-info">
                                <div class="customer-name">{{ customer.customer_name }}</div>
                                <div class="customer-details">
                                    <i class="fas fa-id-card me-1"></i>{{ customer.customer_code or 'N/A' }}
                                    {% if customer.salesperson %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-user-tie me-1"></i>{{ customer.salesperson }}
                                    {% endif %}
                                    {% if customer.division %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-building me-1"></i>{{ customer.division }}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="aging-summary">
                                <div class="total-outstanding text-danger">₹{{ "{:,.0f}".format(customer.total_outstanding or 0) }}</div>
                                <div class="days-overdue overdue-{{ customer.primary_bucket or 'current' }}">
                                    {{ customer.max_days_overdue or 0 }} days overdue
                                </div>
                            </div>
                        </div>

                        <!-- Aging Breakdown -->
                        <div class="aging-breakdown">
                            <div class="aging-bucket bucket-current">
                                <div class="bucket-amount">₹{{ "{:,.0f}".format(customer.current_amount or 0) }}</div>
                                <div class="bucket-label">0-30 Days</div>
                            </div>
                            <div class="aging-bucket bucket-30">
                                <div class="bucket-amount">₹{{ "{:,.0f}".format(customer.days_30_amount or 0) }}</div>
                                <div class="bucket-label">31-60 Days</div>
                            </div>
                            <div class="aging-bucket bucket-60">
                                <div class="bucket-amount">₹{{ "{:,.0f}".format(customer.days_60_amount or 0) }}</div>
                                <div class="bucket-label">61-90 Days</div>
                            </div>
                            <div class="aging-bucket bucket-90">
                                <div class="bucket-amount">₹{{ "{:,.0f}".format(customer.days_90_plus_amount or 0) }}</div>
                                <div class="bucket-label">90+ Days</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-modern btn-sm" onclick="viewCustomerDetails('{{ customer.customer_id }}')">
                                <i class="fas fa-user me-1"></i>Customer Details
                            </button>

                            <button class="btn btn-success btn-modern btn-sm" onclick="collectPayment('{{ customer.customer_id }}', '{{ customer.customer_name }}', {{ customer.total_outstanding }})">
                                <i class="fas fa-money-bill-wave me-1"></i>Collect Payment
                            </button>

                            <button class="btn btn-warning btn-modern btn-sm" onclick="sendReminder('{{ customer.customer_id }}')">
                                <i class="fas fa-bell me-1"></i>Send Reminder
                            </button>

                            {% if customer.max_days_overdue > 90 %}
                            <button class="btn btn-danger btn-modern btn-sm" onclick="escalateAccount('{{ customer.customer_id }}')">
                                <i class="fas fa-exclamation-triangle me-1"></i>Escalate
                            </button>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <div class="stat-card">
                            <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No aging data found</h5>
                            <p class="text-muted">No customers have outstanding amounts or no data matches your filters.</p>
                            <button class="btn btn-primary btn-modern" onclick="location.href='{{ url_for('finance_aging_analysis') }}'">
                                <i class="fas fa-refresh me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Aging Analysis Functions
function viewCustomerDetails(customerId) {
    window.open(`/finance/customer-ledger?customer_id=${customerId}`, '_blank');
}

function collectPayment(customerId, customerName, amount) {
    window.location.href = `/finance/payment-collection?customer=${encodeURIComponent(customerName)}&amount=${amount}`;
}

function sendReminder(customerId) {
    if (confirm('Send payment reminder to this customer?')) {
        alert('Payment reminder functionality will be implemented');
    }
}

function escalateAccount(customerId) {
    if (confirm('Escalate this account for collection?')) {
        alert('Account escalation functionality will be implemented');
    }
}

function backToLedger() {
    const params = new URLSearchParams(window.location.search);
    params.delete('view');  // Remove aging view parameter
    window.location.href = '/finance/customer-ledger?' + params.toString();
}

function exportAging() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '/finance/export-aging-analysis?' + params.toString();
}

function generateAgingReport() {
    const params = new URLSearchParams(window.location.search);
    window.open('/finance/generate-aging-report?' + params.toString(), '_blank');
}
</script>

{% endblock %}
