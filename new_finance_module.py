#!/usr/bin/env python3
"""
🚀 BRAND NEW MODERN 2025 FINANCE MODULE 🚀
Complete rebuild of finance functionality with modern design and proper data integration
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import sqlite3
from utils.db import get_db

# Create finance blueprint
finance_bp = Blueprint('finance', __name__, url_prefix='/finance')

class FinanceManager:
    """Modern Finance Management Class"""
    
    def __init__(self, db):
        self.db = db
    
    def get_dashboard_stats(self):
        """Get comprehensive dashboard statistics"""
        try:
            # Total revenue from orders
            total_revenue = self.db.execute(
                "SELECT COALESCE(SUM(order_amount), 0) FROM orders WHERE status != 'Cancelled'"
            ).fetchone()[0]
            
            # Monthly revenue
            current_month = datetime.now().strftime('%Y-%m')
            monthly_revenue = self.db.execute(
                "SELECT COALESCE(SUM(order_amount), 0) FROM orders WHERE strftime('%Y-%m', order_date) = ? AND status != 'Cancelled'",
                (current_month,)
            ).fetchone()[0]
            
            # Pending payments
            pending_payments = self.db.execute(
                "SELECT COALESCE(SUM(order_amount), 0) FROM orders WHERE payment_status = 'pending' AND status != 'Cancelled'"
            ).fetchone()[0]
            
            # Paid orders
            paid_orders = self.db.execute(
                "SELECT COUNT(*) FROM orders WHERE payment_status = 'paid'"
            ).fetchone()[0]
            
            # Recent orders for cash flow
            recent_orders = self.db.execute("""
                SELECT order_id, customer_name, order_amount, order_date, payment_status
                FROM orders 
                WHERE order_date >= date('now', '-30 days')
                ORDER BY order_date DESC
                LIMIT 10
            """).fetchall()
            
            return {
                'total_revenue': total_revenue,
                'monthly_revenue': monthly_revenue,
                'pending_payments': pending_payments,
                'paid_orders': paid_orders,
                'recent_orders': recent_orders
            }
            
        except Exception as e:
            print(f"Error getting dashboard stats: {e}")
            return {
                'total_revenue': 0,
                'monthly_revenue': 0,
                'pending_payments': 0,
                'paid_orders': 0,
                'recent_orders': []
            }
    
    def get_pending_invoices(self):
        """Get pending invoices with proper filtering"""
        try:
            pending_invoices = self.db.execute("""
                SELECT order_id, customer_name, order_amount, order_date, payment_status, status
                FROM orders 
                WHERE payment_status = 'pending' AND status != 'Cancelled'
                ORDER BY order_date DESC
            """).fetchall()
            
            return pending_invoices
            
        except Exception as e:
            print(f"Error getting pending invoices: {e}")
            return []
    
    def get_customer_ledger(self):
        """Get customer ledger with balances"""
        try:
            customer_ledger = self.db.execute("""
                SELECT 
                    customer_name,
                    COUNT(*) as total_orders,
                    SUM(order_amount) as total_amount,
                    SUM(CASE WHEN payment_status = 'paid' THEN order_amount ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN payment_status = 'pending' THEN order_amount ELSE 0 END) as pending_amount
                FROM orders 
                WHERE status != 'Cancelled'
                GROUP BY customer_name
                ORDER BY total_amount DESC
            """).fetchall()
            
            return customer_ledger
            
        except Exception as e:
            print(f"Error getting customer ledger: {e}")
            return []
    
    def get_payment_collection_data(self):
        """Get payment collection data"""
        try:
            payment_data = self.db.execute("""
                SELECT 
                    order_id, customer_name, order_amount, order_date, 
                    payment_status, customer_phone
                FROM orders 
                WHERE payment_status = 'pending' AND status != 'Cancelled'
                ORDER BY order_date ASC
            """).fetchall()
            
            return payment_data
            
        except Exception as e:
            print(f"Error getting payment collection data: {e}")
            return []

# Routes
@finance_bp.route('/')
@finance_bp.route('/dashboard')
@login_required
def dashboard():
    """🚀 Modern Finance Dashboard"""
    try:
        db = get_db()
        finance_manager = FinanceManager(db)
        
        # Get dashboard statistics
        stats = finance_manager.get_dashboard_stats()
        
        return render_template('finance/modern_dashboard.html', 
                             stats=stats,
                             current_user=current_user)
        
    except Exception as e:
        flash(f'Error loading finance dashboard: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@finance_bp.route('/pending-invoices')
@login_required
def pending_invoices():
    """🚀 Modern Pending Invoices"""
    try:
        db = get_db()
        finance_manager = FinanceManager(db)
        
        # Get pending invoices
        invoices = finance_manager.get_pending_invoices()
        
        return render_template('finance/pending_invoices.html', 
                             invoices=invoices,
                             current_user=current_user)
        
    except Exception as e:
        flash(f'Error loading pending invoices: {str(e)}', 'error')
        return redirect(url_for('finance.dashboard'))

@finance_bp.route('/customer-ledger')
@login_required
def customer_ledger():
    """🚀 Modern Customer Ledger"""
    try:
        db = get_db()
        finance_manager = FinanceManager(db)
        
        # Get customer ledger
        ledger = finance_manager.get_customer_ledger()
        
        return render_template('finance/customer_ledger.html', 
                             ledger=ledger,
                             current_user=current_user)
        
    except Exception as e:
        flash(f'Error loading customer ledger: {str(e)}', 'error')
        return redirect(url_for('finance.dashboard'))

@finance_bp.route('/payment-collection')
@login_required
def payment_collection():
    """🚀 Modern Payment Collection"""
    try:
        db = get_db()
        finance_manager = FinanceManager(db)
        
        # Get payment collection data
        payments = finance_manager.get_payment_collection_data()
        
        return render_template('finance/payment_collection.html', 
                             payments=payments,
                             current_user=current_user)
        
    except Exception as e:
        flash(f'Error loading payment collection: {str(e)}', 'error')
        return redirect(url_for('finance.dashboard'))

@finance_bp.route('/process-payment', methods=['POST'])
@login_required
def process_payment():
    """Process a payment"""
    try:
        db = get_db()
        
        order_id = request.form.get('order_id')
        payment_method = request.form.get('payment_method', 'cash')
        notes = request.form.get('notes', '')
        
        # Update order payment status
        db.execute("""
            UPDATE orders 
            SET payment_status = 'paid', 
                payment_method = ?,
                last_updated = CURRENT_TIMESTAMP
            WHERE order_id = ?
        """, (payment_method, order_id))
        
        db.commit()
        
        flash(f'Payment processed successfully for order {order_id}', 'success')
        return redirect(url_for('finance.payment_collection'))
        
    except Exception as e:
        flash(f'Error processing payment: {str(e)}', 'error')
        return redirect(url_for('finance.payment_collection'))

@finance_bp.route('/api/stats')
@login_required
def api_stats():
    """API endpoint for real-time finance stats"""
    try:
        db = get_db()
        finance_manager = FinanceManager(db)
        
        stats = finance_manager.get_dashboard_stats()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
