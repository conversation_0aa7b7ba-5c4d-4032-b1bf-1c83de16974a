{% extends "base.html" %}

{% block title %}Allocate Inventory - Order {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-boxes"></i> Inventory Allocation - Order {{ order.order_id }}
                    </h4>
                    <small>Customer: {{ order.customer_name }} | Status: {{ order.status }}</small>
                </div>
                
                <div class="card-body">
                    <!-- Order Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><strong>Order Details:</strong></h6>
                            <p><strong>Order ID:</strong> {{ order.order_id }}</p>
                            <p><strong>Customer:</strong> {{ order.customer_name }}</p>
                            <p><strong>Order Date:</strong> {{ order.order_date | format_datetime }}</p>
                            <p><strong>Amount:</strong> ₹{{ order.order_amount | format_currency }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><strong>Quick Actions:</strong></h6>
                            <form method="POST" action="{{ url_for('auto_allocate_inventory', order_id=order.order_id) }}" class="mb-2">
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="fas fa-magic"></i> Auto Allocate (FIFO)
                                </button>
                            </form>
                            <form method="POST" action="{{ url_for('release_allocation', order_id=order.order_id) }}" class="mb-2">
                                <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Release all allocations?')">
                                    <i class="fas fa-undo"></i> Release All Allocations
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Existing Allocations -->
                    {% if existing_allocations %}
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Current Allocations:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Batch</th>
                                        <th>Quantity</th>
                                        <th>Warehouse</th>
                                        <th>Expiry</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for allocation in existing_allocations %}
                                    <tr>
                                        <td>{{ allocation.product_name }}</td>
                                        <td>{{ allocation.batch_number }}</td>
                                        <td>{{ allocation.allocated_quantity }}</td>
                                        <td>{{ allocation.warehouse_name }}</td>
                                        <td>{{ allocation.expiry_date | format_datetime('%Y-%m-%d') if allocation.expiry_date else 'N/A' }}</td>
                                        <td>
                                            <form method="POST" action="{{ url_for('release_allocation', order_id=order.order_id) }}" style="display: inline;">
                                                <input type="hidden" name="product_id" value="{{ allocation.product_id }}">
                                                <button type="submit" class="btn btn-sm btn-outline-warning" onclick="return confirm('Release this allocation?')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Manual Allocation Form -->
                    <form method="POST" action="{{ url_for('manual_allocate_inventory', order_id=order.order_id) }}">
                        <h5 class="mb-3"><i class="fas fa-hand-pointer"></i> Manual Allocation</h5>
                        
                        {% for item in order_items %}
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    {{ item.product_name }} 
                                    <span class="badge badge-primary">Required: {{ item.quantity }}</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                {% if inventory_data[item.product_id] %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Select</th>
                                                <th>Batch Number</th>
                                                <th>Available Qty</th>
                                                <th>Expiry Date</th>
                                                <th>Warehouse</th>
                                                <th>Allocate Qty</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for inv in inventory_data[item.product_id] %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" class="form-check-input allocation-checkbox" 
                                                           data-product="{{ item.product_id }}" 
                                                           data-inventory="{{ inv.id }}"
                                                           data-available="{{ inv.available_quantity }}">
                                                </td>
                                                <td>{{ inv.batch_number }}</td>
                                                <td>
                                                    <span class="badge badge-success">{{ inv.available_quantity }}</span>
                                                </td>
                                                <td>
                                                    {% if inv.expiry_date %}
                                                        {% set expiry_date = inv.expiry_date | format_datetime('%Y-%m-%d') %}
                                                        {% set days_to_expiry = (inv.expiry_date | format_datetime('%Y-%m-%d') | strptime('%Y-%m-%d') - now.date()).days %}
                                                        <span class="{% if days_to_expiry < 30 %}text-danger{% elif days_to_expiry < 90 %}text-warning{% else %}text-success{% endif %}">
                                                            {{ expiry_date }}
                                                        </span>
                                                    {% else %}
                                                        <span class="text-muted">No Expiry</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ inv.warehouse_name }}</td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm allocation-quantity" 
                                                           min="0" max="{{ inv.available_quantity }}" step="0.01"
                                                           data-product="{{ item.product_id }}" 
                                                           data-inventory="{{ inv.id }}"
                                                           placeholder="0" disabled>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> No available inventory for this product.
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}

                        <!-- Hidden inputs for form submission -->
                        <div id="allocation-inputs"></div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="submit-allocation" disabled>
                                <i class="fas fa-check"></i> Apply Manual Allocation
                            </button>
                            <a href="{{ url_for('orders') }}" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-arrow-left"></i> Back to Orders
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.allocation-checkbox');
    const quantityInputs = document.querySelectorAll('.allocation-quantity');
    const submitButton = document.getElementById('submit-allocation');
    const allocationInputsContainer = document.getElementById('allocation-inputs');

    // Enable/disable quantity inputs based on checkbox selection
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const productId = this.dataset.product;
            const inventoryId = this.dataset.inventory;
            const quantityInput = document.querySelector(`input[data-product="${productId}"][data-inventory="${inventoryId}"].allocation-quantity`);
            
            if (this.checked) {
                quantityInput.disabled = false;
                quantityInput.focus();
            } else {
                quantityInput.disabled = true;
                quantityInput.value = '';
            }
            
            updateSubmitButton();
            updateHiddenInputs();
        });
    });

    // Update hidden inputs when quantity changes
    quantityInputs.forEach(input => {
        input.addEventListener('input', function() {
            updateSubmitButton();
            updateHiddenInputs();
        });
    });

    function updateSubmitButton() {
        let hasValidSelection = false;
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const productId = checkbox.dataset.product;
                const inventoryId = checkbox.dataset.inventory;
                const quantityInput = document.querySelector(`input[data-product="${productId}"][data-inventory="${inventoryId}"].allocation-quantity`);
                
                if (quantityInput && parseFloat(quantityInput.value) > 0) {
                    hasValidSelection = true;
                }
            }
        });
        
        submitButton.disabled = !hasValidSelection;
    }

    function updateHiddenInputs() {
        allocationInputsContainer.innerHTML = '';
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const productId = checkbox.dataset.product;
                const inventoryId = checkbox.dataset.inventory;
                const quantityInput = document.querySelector(`input[data-product="${productId}"][data-inventory="${inventoryId}"].allocation-quantity`);
                
                if (quantityInput && parseFloat(quantityInput.value) > 0) {
                    // Create hidden inputs for form submission
                    const productInput = document.createElement('input');
                    productInput.type = 'hidden';
                    productInput.name = 'product_id[]';
                    productInput.value = productId;
                    
                    const inventoryInput = document.createElement('input');
                    inventoryInput.type = 'hidden';
                    inventoryInput.name = 'inventory_id[]';
                    inventoryInput.value = inventoryId;
                    
                    const quantityHidden = document.createElement('input');
                    quantityHidden.type = 'hidden';
                    quantityHidden.name = 'quantity[]';
                    quantityHidden.value = quantityInput.value;
                    
                    allocationInputsContainer.appendChild(productInput);
                    allocationInputsContainer.appendChild(inventoryInput);
                    allocationInputsContainer.appendChild(quantityHidden);
                }
            }
        });
    }
});
</script>
{% endblock %}
