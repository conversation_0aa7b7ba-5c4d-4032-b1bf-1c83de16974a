"""
Permissions utility for Medivent Pharmaceuticals Web Portal
This module provides functions for checking and managing user permissions
"""

import sqlite3
import functools
from flask import g, redirect, url_for, flash, current_app, request
from flask_login import current_user

def get_db():
    """Get database connection"""
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

def get_user_permissions(username):
    """Get all permissions for a user based on their role"""
    db = get_db()
    user = db.execute('SELECT role FROM users WHERE username = ?', (username,)).fetchone()

    if not user:
        return []

    # Get permissions for the user's role
    permissions = db.execute('''
        SELECT p.permission_code
        FROM permissions p
        JOIN role_permissions rp ON p.permission_id = rp.permission_id
        WHERE rp.role = ?
    ''', (user['role'],)).fetchall()

    return [p['permission_code'] for p in permissions]

def has_permission(permission_code):
    """Check if the current user has a specific permission"""
    try:
        if not hasattr(current_user, 'is_authenticated') or not current_user.is_authenticated:
            print("DEBUG: Permission check failed: User not authenticated.")
            return False

        print(f"DEBUG: Checking permission '{permission_code}' for user '{current_user.username}' with role '{current_user.role}'")

        # Admin always has all permissions
        if current_user.role == 'admin':
            print("DEBUG: User is admin, permission granted.")
            return True

        # Get user permissions
        user_permissions = get_user_permissions(current_user.username)
        print(f"DEBUG: User permissions found: {user_permissions}")

        result = permission_code in user_permissions
        print(f"DEBUG: Permission check for '{permission_code}' result: {result}")
        return result
    except Exception as e:
        print(f"ERROR in has_permission: {e}")
        import traceback
        traceback.print_exc()
        return False

def can_view_menu(menu_code):
    """Check if the current user can view a specific menu item

    Menu codes should follow a pattern like:
    - menu_dashboard: Dashboard menu
    - menu_orders: Orders menu
    - menu_products: Products menu
    - menu_inventory: Inventory menu
    - menu_sales: Sales menu
    - menu_warehouse: Warehouse menu
    - menu_reports: Reports menu
    - menu_users: User management menu
    - menu_settings: Settings menu
    """
    if not current_user.is_authenticated:
        return False

    # Admin always sees all menus
    if current_user.role == 'admin':
        return True

    # Map menu codes to required permissions
    menu_permission_map = {
        'menu_dashboard': None,  # Everyone can see dashboard
        'menu_orders': 'order_view',
        'menu_products': 'product_view',
        'menu_inventory': 'inventory_view',
        'menu_sales': 'report_sales',
        'menu_warehouse': 'inventory_view',
        'menu_reports': 'report_view',
        'menu_users': 'user_view',
        'menu_settings': 'settings_view',
        'menu_organization': 'organization_view',
        'menu_customers': 'customer_view',
        'menu_finance': 'finance_view',
        'menu_batches': 'batch_manage',
        'menu_import': 'data_import'
    }

    # If menu doesn't require permission, show it
    required_permission = menu_permission_map.get(menu_code)
    if required_permission is None:
        return True

    # Check if user has the required permission
    return has_permission(required_permission)

def permission_required(permission_code):
    """Decorator to require a specific permission to access a route"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if not has_permission(permission_code):
                flash('Access denied. You do not have permission to access this resource.', 'danger')
                return redirect(url_for('dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def multiple_permissions_required(permission_codes):
    """Decorator to require multiple permissions to access a route"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            for permission_code in permission_codes:
                if not has_permission(permission_code):
                    flash('Access denied. You do not have permission to access this resource.', 'danger')
                    return redirect(url_for('dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def permission_middleware():
    """Middleware to check permissions for all routes"""
    # Map routes to required permissions
    route_permissions = {
        # Product Management
        'products': 'product_view',
        'new_product': 'product_create',
        'update_product': 'product_edit',
        'delete_product': 'product_delete',
        'toggle_product_status': 'product_activate',
        'remove_duplicate_products': 'product_delete',

        # Order Management
        'orders': 'order_view',
        'new_order': 'order_create',
        'update_order': 'order_edit',
        'delete_order': 'order_delete',
        'workflow': 'order_view',
        'approve_order': 'order_approve',
        'process_order': 'order_process',
        'dispatch_order': 'order_dispatch',
        'deliver_order': 'order_deliver',
        'view_order_history': 'order_view',
        'view_challan': 'challan_view',
        'display_challan': 'challan_view',
        'export_orders_excel': 'report_export',

        # Inventory Management
        'inventory': 'inventory_view',
        'new_stock': 'inventory_add',
        'adjust_inventory': 'inventory_adjust',
        'transfer_stock': 'inventory_transfer',
        'inventory_history': 'inventory_view',
        'toggle_inventory_status': 'inventory_adjust',
        'export_inventory_excel': 'report_export',

        # Batch Management
        'batches': 'batch_manage',

        # Reports
        'reports': 'report_view',
        'export_report': 'report_export',
        'sales_report': 'report_sales',
        'inventory_report': 'report_inventory',
        'order_report': 'report_orders',
        'export_product_performance_pdf': 'report_export',
        'export_sales_team_pdf': 'report_export',

        # Documents
        'generate_invoice': 'invoice_generate',
        'view_invoice': 'invoice_view',
        'display_invoice': 'invoice_view',
        'generate_challan': 'challan_generate',

        # Organization
        'organization': 'organization_view',
        'update_team_member': 'organization_edit',

        # Customer Management
        'customers': 'customer_view',

        # Finance/Accounts
        'finance': 'finance_view',
        'finance_overview': 'finance_view',
        'finance_receivables': 'finance_view',
        'finance_payments': 'finance_payment',
        'finance_tax': 'finance_tax',
        'finance_ledger': 'finance_ledger',
        'record_payment': 'finance_payment',

        # Settings
        'settings': 'settings_view',
        'update_settings': 'settings_edit',
        'create_backup': 'backup_create',
        'restore_backup': 'backup_restore',
        'view_logs': 'logs_view',

        # Import Data
        'import_data': 'data_import'
    }

    # Get current endpoint
    endpoint = request.endpoint

    # Skip permission check for authentication routes, static files, and dashboard
    if endpoint and not endpoint.startswith('static') and not endpoint.startswith('auth') and endpoint != 'dashboard':
        # First check if endpoint is directly in the permissions map
        if endpoint in route_permissions:
            if not has_permission(route_permissions[endpoint]):
                flash('Access denied. You do not have permission to access this resource.', 'danger')
                return redirect(url_for('dashboard'))
        else:
            # Check if endpoint starts with any of the route prefixes
            for route, permission in route_permissions.items():
                if endpoint.startswith(f"{route}.") or endpoint.startswith(f"{route}_"):
                    if not has_permission(permission):
                        flash('Access denied. You do not have permission to access this resource.', 'danger')
                        return redirect(url_for('dashboard'))

    return None

def get_all_permissions():
    """Get all available permissions grouped by category"""
    db = get_db()
    permissions = db.execute('''
        SELECT permission_id, permission_code, permission_name, description, category
        FROM permissions
        ORDER BY category, permission_name
    ''').fetchall()

    # Group permissions by category
    grouped_permissions = {}
    for permission in permissions:
        category = permission['category']
        if category not in grouped_permissions:
            grouped_permissions[category] = []
        grouped_permissions[category].append(dict(permission))

    return grouped_permissions

def get_role_permissions(role):
    """Get all permissions assigned to a specific role"""
    db = get_db()
    permissions = db.execute('''
        SELECT p.permission_id, p.permission_code
        FROM permissions p
        JOIN role_permissions rp ON p.permission_id = rp.permission_id
        WHERE rp.role = ?
    ''', (role,)).fetchall()

    return [p['permission_code'] for p in permissions]

def update_role_permissions(role, permission_ids):
    """Update permissions for a role"""
    db = get_db()

    # Start a transaction
    db.execute('BEGIN TRANSACTION')

    try:
        # Delete all existing permissions for the role
        db.execute('DELETE FROM role_permissions WHERE role = ?', (role,))

        # Add new permissions
        for permission_id in permission_ids:
            db.execute('''
                INSERT INTO role_permissions (role, permission_id)
                VALUES (?, ?)
            ''', (role, permission_id))

        # Commit the transaction
        db.execute('COMMIT')
        return True
    except Exception as e:
        # Rollback on error
        db.execute('ROLLBACK')
        current_app.logger.error(f"Error updating role permissions: {str(e)}")
        return False

def get_default_permissions():
    """Get default permissions for all predefined roles"""
    db = get_db()

    # Define default permissions for each role
    default_permissions = {
        'admin': [p['permission_code'] for p in db.execute('SELECT permission_code FROM permissions').fetchall()],
        'manager': [
            # User Management
            'user_view',
            # Order Management
            'order_view', 'order_create', 'order_edit', 'order_approve', 'order_process', 'order_dispatch',
            # Product Management
            'product_view', 'product_create', 'product_edit',
            # Inventory Management
            'inventory_view', 'inventory_add', 'inventory_adjust', 'inventory_transfer', 'batch_manage',
            # Reports
            'report_view', 'report_export', 'report_sales', 'report_inventory', 'report_orders',
            # Documents
            'invoice_generate', 'invoice_view', 'challan_generate', 'challan_view',
            # Finance
            'finance_view', 'finance_payment', 'finance_tax', 'finance_ledger',
            # System
            'settings_view',
            # Menu Visibility - Manager sees most menus
            'menu_dashboard', 'menu_orders', 'menu_products', 'menu_inventory',
            'menu_warehouse', 'menu_reports', 'menu_settings', 'menu_finance'
        ],
        'sales': [
            # Order Management
            'order_view', 'order_create', 'order_edit',
            # Product Management
            'product_view',
            # Documents
            'invoice_view', 'challan_view',
            # Menu Visibility - Sales sees sales-related menus
            'menu_dashboard', 'menu_orders', 'menu_products', 'menu_reports'
        ],
        'warehouse': [
            # Order Management
            'order_view', 'order_process',
            # Product Management
            'product_view',
            # Inventory Management
            'inventory_view', 'inventory_add', 'inventory_adjust', 'inventory_transfer', 'batch_manage',
            # Reports
            'report_view', 'report_inventory',
            # Documents
            'challan_view',
            # Menu Visibility - Warehouse only sees warehouse-related menus
            'menu_dashboard', 'menu_orders', 'menu_products', 'menu_inventory', 'menu_warehouse', 'menu_reports'
        ],
        'rider': [
            # Order Management
            'order_view', 'order_deliver',
            # Documents
            'challan_view',
            # Menu Visibility - Rider only sees delivery-related menus
            'menu_dashboard', 'menu_orders'
        ],
        'customer': [
            # Order Management
            'order_view',
            # Documents
            'invoice_view', 'challan_view',
            # Menu Visibility - Customer only sees order-related menus
            'menu_dashboard', 'menu_orders'
        ],
        'user': [
            # Order Management
            'order_view',
            # Product Management
            'product_view',
            # Inventory Management
            'inventory_view',
            # Menu Visibility - Basic user sees limited menus
            'menu_dashboard', 'menu_orders', 'menu_products'
        ]
    }

    return default_permissions

def get_default_role_permissions(role):
    """Get default permission IDs for a specific role"""
    db = get_db()
    default_permissions = get_default_permissions()

    # Check if role exists in default permissions
    if role not in default_permissions:
        return []

    # Get permission codes for the role
    permission_codes = default_permissions[role]

    # Get permission IDs for the permission codes
    permission_ids = []
    for code in permission_codes:
        permission = db.execute('SELECT permission_id FROM permissions WHERE permission_code = ?', (code,)).fetchone()
        if permission:
            permission_ids.append(permission['permission_id'])

    return permission_ids

def sync_default_role_permissions():
    """Sync default permissions for predefined roles"""
    db = get_db()

    # Get default permissions for all roles
    default_permissions = get_default_permissions()

    # Start a transaction
    db.execute('BEGIN TRANSACTION')

    try:
        # For each role, sync permissions
        for role, permission_codes in default_permissions.items():
            # Get permission IDs for the permission codes
            permission_ids = []
            for code in permission_codes:
                permission = db.execute('SELECT permission_id FROM permissions WHERE permission_code = ?', (code,)).fetchone()
                if permission:
                    permission_ids.append(permission['permission_id'])

            # Delete existing permissions for the role
            db.execute('DELETE FROM role_permissions WHERE role = ?', (role,))

            # Add new permissions
            for permission_id in permission_ids:
                db.execute('''
                    INSERT INTO role_permissions (role, permission_id)
                    VALUES (?, ?)
                ''', (role, permission_id))

        # Commit the transaction
        db.execute('COMMIT')
        return True
    except Exception as e:
        # Rollback on error
        db.execute('ROLLBACK')
        current_app.logger.error(f"Error syncing default role permissions: {str(e)}")
        return False

