"""
TCS Express Real-Time Tracking Scraper
Uses Playwright for robust web scraping of TCS Express tracking data
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, TimeoutError as PlaywrightTimeoutError
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TCSTracker:
    """
    Real-time TCS Express tracking scraper using <PERSON><PERSON>
    """
    
    def __init__(self, headless: bool = True, timeout: int = 30000):
        self.headless = headless
        self.timeout = timeout
        self.browser: Optional[Browser] = None
        self.base_url = "https://www.tcsexpress.com/track"
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close_browser()
        
    async def start_browser(self):
        """Start the Playwright browser"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images',  # Faster loading
                    '--disable-javascript-harmony-shipping',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-ipc-flooding-protection'
                ]
            )
            logger.info("Browser started successfully")
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise
            
    async def close_browser(self):
        """Close the browser and cleanup"""
        try:
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            logger.info("Browser closed successfully")
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
            
    async def track_shipment(self, tracking_number: str) -> Dict[str, Any]:
        """
        Track a single shipment by tracking number
        
        Args:
            tracking_number: TCS tracking number
            
        Returns:
            Dictionary containing tracking information
        """
        if not self.browser:
            raise RuntimeError("Browser not started. Use async context manager or call start_browser()")
            
        try:
            # Create new page
            page = await self.browser.new_page()
            
            # Set user agent to avoid detection
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            # Navigate to tracking page
            tracking_url = f"{self.base_url}?tracking_number={tracking_number}"
            logger.info(f"Navigating to: {tracking_url}")
            
            await page.goto(tracking_url, wait_until='networkidle', timeout=self.timeout)
            
            # Wait for the main content to load
            await page.wait_for_selector('#main-content-app', timeout=self.timeout)
            
            # Wait a bit more for dynamic content
            await asyncio.sleep(2)
            
            # Extract tracking data
            tracking_data = await self._extract_tracking_data(page, tracking_number)
            
            await page.close()
            return tracking_data
            
        except PlaywrightTimeoutError:
            logger.error(f"Timeout while tracking {tracking_number}")
            return self._create_error_response(tracking_number, "Request timeout - TCS website may be slow")
        except Exception as e:
            logger.error(f"Error tracking {tracking_number}: {e}")
            return self._create_error_response(tracking_number, f"Error: {str(e)}")
            
    async def _extract_tracking_data(self, page: Page, tracking_number: str) -> Dict[str, Any]:
        """Extract tracking data from the page"""
        try:
            # Check if tracking data exists
            main_container = await page.query_selector('div.w-\\[70rem\\].shadow-card.p-8')
            
            if not main_container:
                # Try alternative selectors
                main_container = await page.query_selector('[class*="shadow-card"]')
                
            if not main_container:
                # Check for error messages
                error_element = await page.query_selector('text="No tracking information found"')
                if error_element:
                    return self._create_error_response(tracking_number, "No tracking information found")
                    
                # Check for invalid tracking number message
                invalid_element = await page.query_selector('text="Invalid tracking number"')
                if invalid_element:
                    return self._create_error_response(tracking_number, "Invalid tracking number")
                    
                return self._create_error_response(tracking_number, "Tracking data container not found")
            
            # Extract basic tracking information
            tracking_data = {
                'tracking_number': tracking_number,
                'success': True
            }
            
            # Extract tracking number from page (verification)
            tracking_span = await page.query_selector('span.text-lg.text-\\[\\#f0575d\\].font-bold, span[class*="text-lg"][class*="font-bold"]')
            if tracking_span:
                page_tracking_number = await tracking_span.text_content()
                if page_tracking_number:
                    tracking_data['tracking_number'] = page_tracking_number.strip()
            
            # Extract shipment details
            await self._extract_shipment_details(page, tracking_data)
            
            # Extract current status
            await self._extract_current_status(page, tracking_data)
            
            # Extract delivery information
            await self._extract_delivery_info(page, tracking_data)
            
            # Extract track history
            await self._extract_track_history(page, tracking_data)
            
            # Set default values for missing data
            self._set_default_values(tracking_data)
            
            return tracking_data
            
        except Exception as e:
            logger.error(f"Error extracting data for {tracking_number}: {e}")
            return self._create_error_response(tracking_number, f"Data extraction error: {str(e)}")
            
    async def _extract_shipment_details(self, page: Page, tracking_data: Dict[str, Any]):
        """Extract shipment booking details"""
        try:
            # Get page content for text-based extraction
            content = await page.content()

            # Extract using more robust selectors
            # Look for origin
            try:
                origin_element = await page.locator('text=Origin:').locator('..').locator('p').first
                if await origin_element.count() > 0:
                    origin_text = await origin_element.text_content()
                    if origin_text:
                        tracking_data['origin'] = origin_text.strip()
            except:
                pass

            # Look for destination
            try:
                dest_element = await page.locator('text=Destination:').locator('..').locator('p').first
                if await dest_element.count() > 0:
                    dest_text = await dest_element.text_content()
                    if dest_text:
                        tracking_data['destination'] = dest_text.strip()
            except:
                pass

            # Look for booking date
            try:
                booking_element = await page.locator('text=Booking Date:').locator('..').locator('p').first
                if await booking_element.count() > 0:
                    booking_text = await booking_element.text_content()
                    if booking_text:
                        tracking_data['booking_date'] = booking_text.strip()
            except:
                pass

            # Look for agent reference
            try:
                agent_element = await page.locator('text=Agent Reference Number:').locator('..').locator('p').first
                if await agent_element.count() > 0:
                    agent_text = await agent_element.text_content()
                    if agent_text:
                        tracking_data['agent_reference'] = agent_text.strip()
            except:
                pass

            # Alternative extraction using CSS selectors
            if 'origin' not in tracking_data or tracking_data['origin'] == 'N/A':
                try:
                    # Try different selector patterns
                    selectors = [
                        'div:has-text("Origin:") + p',
                        'p:has-text("Origin:") + p',
                        '[class*="flex"]:has-text("Origin:") p:last-child'
                    ]
                    for selector in selectors:
                        element = await page.query_selector(selector)
                        if element:
                            text = await element.text_content()
                            if text and text.strip() != 'Origin:':
                                tracking_data['origin'] = text.strip()
                                break
                except:
                    pass

        except Exception as e:
            logger.error(f"Error extracting shipment details: {e}")
            
    async def _extract_current_status(self, page: Page, tracking_data: Dict[str, Any]):
        """Extract current status information"""
        try:
            # Look for current status using locator
            try:
                status_element = await page.locator('text=Current Status:').locator('..').locator('span[class*="font-bold"]').first
                if await status_element.count() > 0:
                    status_text = await status_element.text_content()
                    if status_text:
                        tracking_data['current_status'] = status_text.strip()
            except:
                pass

            # Alternative extraction methods
            if 'current_status' not in tracking_data or tracking_data['current_status'] == 'Unknown':
                try:
                    # Try different patterns
                    selectors = [
                        'span[class*="text-"][class*="font-bold"]',
                        '.text-\\[\\#f0575d\\].font-bold',
                        'span.font-bold'
                    ]
                    for selector in selectors:
                        elements = await page.query_selector_all(selector)
                        for element in elements:
                            text = await element.text_content()
                            if text and text.strip().lower() in ['delivered', 'in transit', 'dispatched', 'picked up']:
                                tracking_data['current_status'] = text.strip()
                                break
                        if tracking_data.get('current_status', 'Unknown') != 'Unknown':
                            break
                except:
                    pass

        except Exception as e:
            logger.error(f"Error extracting current status: {e}")
            
    async def _extract_delivery_info(self, page: Page, tracking_data: Dict[str, Any]):
        """Extract delivery information"""
        try:
            # Look for delivered on
            delivered_elements = await page.query_selector_all('text="Delivered On:"')
            for element in delivered_elements:
                parent = await element.evaluate('el => el.parentElement')
                if parent:
                    next_element = await parent.query_selector('p')
                    if next_element:
                        delivered_text = await next_element.text_content()
                        if delivered_text:
                            tracking_data['delivered_on'] = delivered_text.strip()
                            break
            
            # Look for received by
            received_elements = await page.query_selector_all('text="Received by:"')
            for element in received_elements:
                parent = await element.evaluate('el => el.parentElement')
                if parent:
                    next_element = await parent.query_selector('p')
                    if next_element:
                        received_text = await next_element.text_content()
                        if received_text:
                            tracking_data['received_by'] = received_text.strip()
                            break
                            
        except Exception as e:
            logger.error(f"Error extracting delivery info: {e}")
            
    async def _extract_track_history(self, page: Page, tracking_data: Dict[str, Any]):
        """Extract tracking history from table"""
        try:
            track_history = []
            
            # Look for the tracking history table
            table = await page.query_selector('table')
            if table:
                rows = await table.query_selector_all('tbody tr')
                
                for row in rows:
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 2:
                        # Extract date/time
                        date_cell = cells[0]
                        date_text = await date_cell.text_content()
                        
                        # Extract status
                        status_cell = cells[1]
                        status_text = await status_cell.text_content()
                        
                        if date_text and status_text:
                            # Clean up the text
                            date_clean = re.sub(r'\s+', ' ', date_text.strip())
                            status_lines = status_text.strip().split('\n')
                            status = status_lines[0].strip() if status_lines else status_text.strip()
                            location = status_lines[1].strip() if len(status_lines) > 1 else ''
                            
                            track_history.append({
                                'date_time': date_clean,
                                'status': status,
                                'location': location
                            })
            
            tracking_data['track_history'] = track_history
            
        except Exception as e:
            logger.error(f"Error extracting track history: {e}")
            tracking_data['track_history'] = []
            
    def _set_default_values(self, tracking_data: Dict[str, Any]):
        """Set default values for missing data"""
        defaults = {
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Unknown',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }
        
        for key, default_value in defaults.items():
            if key not in tracking_data:
                tracking_data[key] = default_value
                
    def _create_error_response(self, tracking_number: str, error_message: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            'success': False,
            'tracking_number': tracking_number,
            'error': error_message,
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Error',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }

# Synchronous wrapper function for Flask integration
def track_tcs_shipment(tracking_number: str, headless: bool = True) -> Dict[str, Any]:
    """
    Synchronous wrapper for tracking TCS shipments
    
    Args:
        tracking_number: TCS tracking number
        headless: Run browser in headless mode
        
    Returns:
        Dictionary containing tracking information
    """
    async def _track():
        async with TCSTracker(headless=headless) as tracker:
            return await tracker.track_shipment(tracking_number)
    
    try:
        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_track())
        loop.close()
        return result
    except Exception as e:
        logger.error(f"Error in synchronous wrapper: {e}")
        return {
            'success': False,
            'tracking_number': tracking_number,
            'error': f"System error: {str(e)}",
            'agent_reference': 'NA',
            'origin': 'N/A',
            'destination': 'N/A',
            'booking_date': 'N/A',
            'current_status': 'Error',
            'delivered_on': 'N/A',
            'received_by': 'N/A',
            'track_history': []
        }

# Test function
async def test_tracking():
    """Test the tracking functionality"""
    test_numbers = [
        "31442084039",
        "31442083394",
        "31442083525"
    ]
    
    async with TCSTracker(headless=False) as tracker:
        for number in test_numbers:
            print(f"\n=== Testing {number} ===")
            result = await tracker.track_shipment(number)
            print(json.dumps(result, indent=2))

if __name__ == "__main__":
    # Run test
    asyncio.run(test_tracking())
