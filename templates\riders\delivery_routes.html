{% extends 'base.html' %}

{% block title %}Delivery Routes - Medivent ERP{% endblock %}

{% block content %}
<style>
/* Modern 2025 Delivery Routes UI */
.routes-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.routes-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.routes-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4e73df, #224abe, #36b9cc);
}

.routes-title {
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.modern-routes-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modern-routes-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.route-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.route-card.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1cc88a, #13855c);
    border-radius: 20px 20px 0 0;
}

.route-card.busy::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f6c23e, #dda20a);
    border-radius: 20px 20px 0 0;
}

.modern-btn-routes {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
}

.modern-btn-routes:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.modern-btn-success:hover {
    box-shadow: 0 15px 30px rgba(28, 200, 138, 0.4);
}

.order-count-badge {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

@media (max-width: 768px) {
    .routes-title {
        font-size: 2rem;
    }
}
</style>

<div class="routes-dashboard">
    <div class="container-fluid">
        <!-- Modern Header -->
        <div class="routes-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="routes-title">
                        <i class="fas fa-route mr-3"></i>Delivery Routes Management
                    </h1>
                    <p class="text-muted mb-0 mt-2">Optimize delivery routes and manage rider assignments</p>
                </div>
                <div>
                    <a href="{{ url_for('riders') }}" class="modern-btn-routes text-decoration-none">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Riders
                    </a>
                    <button class="modern-btn-routes" onclick="optimizeRoutes()">
                        <i class="fas fa-magic mr-2"></i>Optimize Routes
                    </button>
                    <button class="modern-btn-routes modern-btn-success" onclick="exportRoutes()">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Active Routes -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="modern-routes-card">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h4 class="font-weight-bold mb-0" style="color: #4e73df;">
                            <i class="fas fa-map-marked-alt mr-2"></i>Active Delivery Routes
                        </h4>
                        <span class="badge badge-primary badge-pill px-3 py-2">
                            {{ riders|length if riders else 3 }} Active Routes
                        </span>
                    </div>

                    {% if riders %}
                    <div class="row">
                        {% for rider in riders %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="route-card {% if rider.active_orders > 3 %}busy{% else %}active{% endif %}">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-motorcycle"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">{{ rider.name }}</h6>
                                            <small class="text-muted">{{ rider.rider_id }}</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="order-count-badge">{{ rider.active_orders or 0 }} Orders</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Route</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        {{ rider.current_routes or 'Karachi Main Area → Defence → Clifton' }}
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-routes" onclick="viewRoute('{{ rider.rider_id }}')">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="optimizeRoute('{{ rider.rider_id }}')">
                                        <i class="fas fa-magic mr-1"></i>Optimize
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="trackRoute('{{ rider.rider_id }}')">
                                        <i class="fas fa-satellite mr-1"></i>Track
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <!-- Demo Routes -->
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="route-card active">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-motorcycle"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">Ahmed Khan</h6>
                                            <small class="text-muted">R001</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="order-count-badge">3 Orders</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Route</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Gulshan → Defence → Clifton
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-routes" onclick="viewRoute('R001')">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="optimizeRoute('R001')">
                                        <i class="fas fa-magic mr-1"></i>Optimize
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="trackRoute('R001')">
                                        <i class="fas fa-satellite mr-1"></i>Track
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="route-card busy">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-motorcycle"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">Ali Hassan</h6>
                                            <small class="text-muted">R002</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="order-count-badge">5 Orders</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Route</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Nazimabad → North Nazimabad → FB Area
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-routes" onclick="viewRoute('R002')">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="optimizeRoute('R002')">
                                        <i class="fas fa-magic mr-1"></i>Optimize
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="trackRoute('R002')">
                                        <i class="fas fa-satellite mr-1"></i>Track
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="route-card active">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-bicycle"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">Muhammad Usman</h6>
                                            <small class="text-muted">R003</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="order-count-badge">2 Orders</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Route</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Saddar → Empress Market → Kharadar
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-routes" onclick="viewRoute('R003')">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="optimizeRoute('R003')">
                                        <i class="fas fa-magic mr-1"></i>Optimize
                                    </button>
                                    <button class="btn btn-sm modern-btn-routes" onclick="trackRoute('R003')">
                                        <i class="fas fa-satellite mr-1"></i>Track
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Modern Delivery Routes JavaScript

function optimizeRoutes() {
    showNotification('🔄 Optimizing all delivery routes...', 'info');

    setTimeout(() => {
        showNotification('✅ Routes optimized! Average delivery time reduced by 15%', 'success');
    }, 3000);
}

function exportRoutes() {
    showNotification('📊 Exporting route data...', 'info');

    setTimeout(() => {
        showNotification('✅ Route data exported successfully!', 'success');
    }, 2000);
}

function viewRoute(riderId) {
    showNotification(`🗺️ Loading route details for ${riderId}...`, 'info');

    setTimeout(() => {
        showNotification(`✅ Route details loaded for ${riderId}`, 'success');
        // Here you would show route details modal or navigate to route page
    }, 1500);
}

function optimizeRoute(riderId) {
    showNotification(`⚡ Optimizing route for ${riderId}...`, 'info');

    setTimeout(() => {
        showNotification(`✅ Route optimized for ${riderId}! Time saved: 8 minutes`, 'success');
    }, 2500);
}

function trackRoute(riderId) {
    showNotification(`📡 Starting live tracking for ${riderId}...`, 'info');

    setTimeout(() => {
        showNotification(`✅ Live tracking activated for ${riderId}`, 'success');
        // Here you would open live tracking interface
    }, 1500);
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        backdrop-filter: blur(15px);
        border: none;
        font-weight: 600;
    `;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Add animations on page load
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.route-card, .modern-routes-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    setTimeout(() => {
        showNotification('🚀 Delivery routes dashboard loaded!', 'success');
    }, 1000);
});
</script>
{% endblock %}
