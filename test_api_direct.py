"""
Direct API testing script to debug TCS tracking issues
"""

import requests
import json
import time

def test_api_with_session():
    """Test the API with a proper session (login first)"""
    
    # Create a session
    session = requests.Session()
    
    # First, login to get authentication
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    print("🔐 Logging in...")
    login_response = session.post('http://localhost:3000/login', data=login_data)
    
    if login_response.status_code == 200:
        print("✅ Login successful")
        
        # Now test the TCS tracking API
        tracking_data = {
            'tracking_number': '31442084041'
        }
        
        print(f"📦 Testing tracking number: {tracking_data['tracking_number']}")
        
        api_response = session.post(
            'http://localhost:3000/api/track-tcs',
            json=tracking_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 API Response Status: {api_response.status_code}")
        
        if api_response.status_code == 200:
            try:
                response_data = api_response.json()
                print("✅ API Response:")
                print(json.dumps(response_data, indent=2))
                return response_data
            except json.JSONDecodeError:
                print("❌ Failed to parse JSON response")
                print("Raw response:", api_response.text[:500])
        else:
            print(f"❌ API request failed with status {api_response.status_code}")
            print("Response:", api_response.text[:500])
    else:
        print(f"❌ Login failed with status {login_response.status_code}")
        print("Response:", login_response.text[:500])
    
    return None

def test_direct_scraper():
    """Test the scraper directly for comparison"""
    print("\n" + "="*50)
    print("🧪 DIRECT SCRAPER TEST")
    print("="*50)
    
    try:
        from tcs_scraper_demo import track_tcs_demo
        
        tracking_number = '31442084041'
        print(f"📦 Testing tracking number: {tracking_number}")
        
        start_time = time.time()
        result = track_tcs_demo(tracking_number, headless=True)
        end_time = time.time()
        
        print(f"⏱️  Time taken: {end_time - start_time:.2f} seconds")
        print("✅ Direct Scraper Result:")
        print(json.dumps(result, indent=2))
        
        return result
        
    except Exception as e:
        print(f"❌ Direct scraper failed: {e}")
        return None

def compare_results(api_result, scraper_result):
    """Compare API and direct scraper results"""
    print("\n" + "="*50)
    print("🔍 COMPARISON ANALYSIS")
    print("="*50)
    
    if not api_result:
        print("❌ API result is None - cannot compare")
        return
        
    if not scraper_result:
        print("❌ Scraper result is None - cannot compare")
        return
    
    # Extract data from API response
    api_data = api_result.get('data', {}) if api_result.get('success') else {}
    
    # Compare key fields
    fields_to_compare = [
        'tracking_number', 'current_status', 'origin', 'destination',
        'booking_date', 'delivered_on', 'received_by'
    ]
    
    print("📊 Field Comparison:")
    for field in fields_to_compare:
        api_value = api_data.get(field, 'N/A')
        scraper_value = scraper_result.get(field, 'N/A')
        
        if api_value == scraper_value:
            print(f"   ✅ {field}: {api_value}")
        else:
            print(f"   ❌ {field}: API='{api_value}' vs Scraper='{api_value}'")
    
    # Compare track history
    api_history = api_data.get('track_history', [])
    scraper_history = scraper_result.get('track_history', [])
    
    print(f"\n📋 Track History:")
    print(f"   API entries: {len(api_history)}")
    print(f"   Scraper entries: {len(scraper_history)}")
    
    if len(api_history) == len(scraper_history):
        print("   ✅ History length matches")
    else:
        print("   ❌ History length differs")

def main():
    """Main test function"""
    print("🚀 TCS TRACKING API DEBUGGING")
    print("="*50)
    
    # Test direct scraper first
    scraper_result = test_direct_scraper()
    
    # Test API with authentication
    print("\n" + "="*50)
    print("🌐 API TEST WITH AUTHENTICATION")
    print("="*50)
    
    api_result = test_api_with_session()
    
    # Compare results
    if api_result and scraper_result:
        compare_results(api_result, scraper_result)
    
    print("\n" + "="*50)
    print("🏁 TEST COMPLETE")
    print("="*50)

if __name__ == "__main__":
    main()
