{% extends 'base.html' %}

{% block title %}Warehouse Inventory Report{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Warehouse Inventory Report</h5>
                        <div>
                            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Reports
                            </a>
                            <a href="{{ url_for('export_warehouse_inventory', warehouse_id=selected_warehouse, format='excel') }}" class="btn btn-success btn-sm ml-2">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a>
                            <a href="{{ url_for('export_warehouse_inventory', warehouse_id=selected_warehouse, format='pdf') }}" class="btn btn-danger btn-sm ml-2">
                                <i class="fas fa-file-pdf"></i> Export to PDF
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('warehouse_inventory_report') }}" method="GET" class="form-inline">
                                <div class="form-group mr-2">
                                    <label for="warehouse_id" class="mr-2">Warehouse:</label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id">
                                        <option value="all" {% if selected_warehouse == 'all' %}selected{% endif %}>All Warehouses</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}" {% if selected_warehouse == warehouse.warehouse_id %}selected{% endif %}>{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="inventorySearch" placeholder="Search inventory...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        {% for warehouse_name, stats in summary.items() %}
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">{{ warehouse_name }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 text-center">
                                            <h3>{{ stats.total_items }}</h3>
                                            <small class="text-muted">Items</small>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <h3>{{ stats.total_quantity }}</h3>
                                            <small class="text-muted">Units</small>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <h3>{{ stats.active_batches }}</h3>
                                            <small class="text-muted">Active Batches</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Inventory Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered" id="inventoryTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Warehouse</th>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Batch Number</th>
                                    <th>Manufacturing Date</th>
                                    <th>Expiry Date</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if inventory_items %}
                                    {% for item in inventory_items %}
                                    <tr>
                                        <td>{{ item.warehouse_name }}</td>
                                        <td>{{ item.product_name }}</td>
                                        <td>{{ item.strength }}</td>
                                        <td>{{ item.batch_number }}</td>
                                        <td>{{ item.manufacturing_date }}</td>
                                        <td>{{ item.expiry_date }}</td>
                                        <td>{{ item.stock_quantity }}</td>
                                        <td>
                                            {% if item.status == 'active' %}
                                            <span class="badge badge-success">Active</span>
                                            {% else %}
                                            <span class="badge badge-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">No inventory items found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Auto-submit form when warehouse selection changes
        $('#warehouse_id').change(function() {
            $(this).closest('form').submit();
        });
        
        // Search functionality
        $('#inventorySearch').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $("#inventoryTable tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
    });
</script>
{% endblock %}
