"""
Comprehensive validation of browser-terminal consistency for TCS tracking
"""

import requests
import json
import time
from tcs_scraper_demo import track_tcs_demo

def validate_consistency():
    """Validate that browser API and terminal scraper give consistent results"""
    
    # Test tracking numbers that reportedly work in terminal
    test_numbers = [
        '31442083522',
        '31442083523', 
        '31442083525',
        '31442083524',
        '31442083393'
    ]
    
    print("🔍 VALIDATING BROWSER-TERMINAL CONSISTENCY")
    print("=" * 60)
    print(f"🧪 Testing {len(test_numbers)} tracking numbers")
    print(f"🌐 API Endpoint: http://localhost:5001/api/track-tcs-public")
    print("=" * 60)
    
    results = []
    terminal_success = 0
    api_success = 0
    consistent_results = 0
    
    for i, tracking_number in enumerate(test_numbers, 1):
        print(f"\n📦 Test {i}/{len(test_numbers)}: {tracking_number}")
        print("-" * 40)
        
        result = {
            'tracking_number': tracking_number,
            'terminal': None,
            'api': None,
            'consistent': False
        }
        
        # Test 1: Terminal scraper (direct)
        print("🖥️  TERMINAL TEST:")
        try:
            start_time = time.time()
            terminal_result = track_tcs_demo(tracking_number, headless=True)
            terminal_duration = time.time() - start_time
            
            if terminal_result.get('success'):
                terminal_success += 1
                print(f"   ✅ SUCCESS ({terminal_duration:.1f}s)")
                print(f"   Status: {terminal_result.get('current_status')}")
                print(f"   Route: {terminal_result.get('origin')} → {terminal_result.get('destination')}")
                result['terminal'] = {
                    'success': True,
                    'status': terminal_result.get('current_status'),
                    'origin': terminal_result.get('origin'),
                    'destination': terminal_result.get('destination'),
                    'duration': terminal_duration
                }
            else:
                print(f"   ❌ FAILED ({terminal_duration:.1f}s)")
                print(f"   Error: {terminal_result.get('error')}")
                result['terminal'] = {
                    'success': False,
                    'error': terminal_result.get('error'),
                    'duration': terminal_duration
                }
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
            result['terminal'] = {
                'success': False,
                'error': str(e),
                'duration': 0
            }
        
        # Test 2: Browser API
        print("\n🌐 BROWSER API TEST:")
        try:
            start_time = time.time()
            api_response = requests.post(
                'http://localhost:5001/api/track-tcs-public',
                json={'tracking_number': tracking_number},
                timeout=45
            )
            api_duration = time.time() - start_time
            
            if api_response.status_code == 200:
                api_result = api_response.json()
                
                if api_result.get('success'):
                    api_success += 1
                    api_data = api_result.get('data', {})
                    print(f"   ✅ SUCCESS ({api_duration:.1f}s)")
                    print(f"   Status: {api_data.get('current_status')}")
                    print(f"   Route: {api_data.get('origin')} → {api_data.get('destination')}")
                    result['api'] = {
                        'success': True,
                        'status': api_data.get('current_status'),
                        'origin': api_data.get('origin'),
                        'destination': api_data.get('destination'),
                        'duration': api_duration
                    }
                else:
                    print(f"   ❌ FAILED ({api_duration:.1f}s)")
                    print(f"   Error: {api_result.get('error')}")
                    result['api'] = {
                        'success': False,
                        'error': api_result.get('error'),
                        'duration': api_duration
                    }
            else:
                print(f"   ❌ HTTP ERROR ({api_duration:.1f}s)")
                print(f"   Status: {api_response.status_code}")
                result['api'] = {
                    'success': False,
                    'error': f'HTTP {api_response.status_code}',
                    'duration': api_duration
                }
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
            result['api'] = {
                'success': False,
                'error': str(e),
                'duration': 0
            }
        
        # Check consistency
        terminal_success_flag = result['terminal'] and result['terminal']['success']
        api_success_flag = result['api'] and result['api']['success']
        
        if terminal_success_flag and api_success_flag:
            # Both successful - check if data is consistent
            terminal_status = result['terminal']['status']
            api_status = result['api']['status']
            
            # Allow for minor variations in status text
            status_consistent = (terminal_status == api_status or 
                               terminal_status.lower() in api_status.lower() or
                               api_status.lower() in terminal_status.lower())
            
            if status_consistent:
                consistent_results += 1
                result['consistent'] = True
                print(f"\n   ✅ CONSISTENT: Both successful with similar status")
            else:
                print(f"\n   ⚠️  INCONSISTENT: Different status - Terminal: '{terminal_status}' vs API: '{api_status}'")
        elif not terminal_success_flag and not api_success_flag:
            # Both failed - this is consistent
            consistent_results += 1
            result['consistent'] = True
            print(f"\n   ✅ CONSISTENT: Both failed")
        else:
            print(f"\n   ❌ INCONSISTENT: Terminal {'succeeded' if terminal_success_flag else 'failed'}, API {'succeeded' if api_success_flag else 'failed'}")
        
        results.append(result)
        time.sleep(1)  # Brief pause between tests
    
    # Final Summary
    print(f"\n{'=' * 60}")
    print(f"🏁 VALIDATION COMPLETE")
    print(f"{'=' * 60}")
    print(f"📊 RESULTS SUMMARY:")
    print(f"   🖥️  Terminal Success: {terminal_success}/{len(test_numbers)} ({(terminal_success/len(test_numbers)*100):.1f}%)")
    print(f"   🌐 API Success: {api_success}/{len(test_numbers)} ({(api_success/len(test_numbers)*100):.1f}%)")
    print(f"   🎯 Consistent Results: {consistent_results}/{len(test_numbers)} ({(consistent_results/len(test_numbers)*100):.1f}%)")
    
    # Detailed analysis
    if consistent_results == len(test_numbers):
        print(f"\n✅ PERFECT CONSISTENCY: Browser and terminal results match!")
        print(f"   🎉 The browser-terminal discrepancy has been RESOLVED!")
    elif consistent_results >= len(test_numbers) * 0.8:
        print(f"\n✅ GOOD CONSISTENCY: Most results match ({(consistent_results/len(test_numbers)*100):.1f}%)")
        print(f"   🔧 Minor inconsistencies may be due to timing or data variations")
    else:
        print(f"\n❌ POOR CONSISTENCY: Significant discrepancies remain")
        print(f"   🔍 Further investigation needed")
    
    # Error analysis
    print(f"\n🔍 ERROR ANALYSIS:")
    terminal_errors = [r for r in results if r['terminal'] and not r['terminal']['success']]
    api_errors = [r for r in results if r['api'] and not r['api']['success']]
    
    if terminal_errors:
        print(f"   🖥️  Terminal Errors:")
        for error in terminal_errors:
            print(f"      {error['tracking_number']}: {error['terminal']['error']}")
    
    if api_errors:
        print(f"   🌐 API Errors:")
        for error in api_errors:
            print(f"      {error['tracking_number']}: {error['api']['error']}")
    
    if not terminal_errors and not api_errors:
        print(f"   ✅ No errors detected!")
    
    # Save detailed results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    results_file = f"validation_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'test_numbers': test_numbers,
            'terminal_success': terminal_success,
            'api_success': api_success,
            'consistent_results': consistent_results,
            'total_tests': len(test_numbers),
            'consistency_rate': (consistent_results/len(test_numbers)*100),
            'detailed_results': results
        }, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    return {
        'terminal_success_rate': terminal_success/len(test_numbers),
        'api_success_rate': api_success/len(test_numbers),
        'consistency_rate': consistent_results/len(test_numbers),
        'results': results
    }

def test_error_categorization():
    """Test error categorization for invalid numbers"""
    
    print(f"\n🔍 TESTING ERROR CATEGORIZATION")
    print("=" * 40)
    
    invalid_numbers = ['12345', '999999999', 'invalid', '']
    
    for number in invalid_numbers:
        try:
            response = requests.post(
                'http://localhost:5001/api/track-tcs-public',
                json={'tracking_number': number},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if not data.get('success'):
                    error_msg = data.get('error', 'Unknown error')
                    if error_msg == 'Incorrect Number':
                        print(f"   ✅ {number or 'empty'}: Correctly shows 'Incorrect Number'")
                    else:
                        print(f"   ⚠️  {number or 'empty'}: Shows '{error_msg}' (expected 'Incorrect Number')")
                else:
                    print(f"   ❌ {number or 'empty'}: Unexpectedly succeeded")
            else:
                print(f"   ❌ {number or 'empty'}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   💥 {number or 'empty'}: Exception - {e}")

if __name__ == "__main__":
    # Run validation
    validation_results = validate_consistency()
    
    # Test error categorization
    test_error_categorization()
    
    # Final verdict
    print(f"\n🎯 FINAL VERDICT:")
    if validation_results['consistency_rate'] >= 0.9:
        print(f"   ✅ BROWSER-TERMINAL DISCREPANCY RESOLVED!")
        print(f"   🎉 Consistency rate: {validation_results['consistency_rate']*100:.1f}%")
    else:
        print(f"   ⚠️  PARTIAL RESOLUTION")
        print(f"   📊 Consistency rate: {validation_results['consistency_rate']*100:.1f}%")
        print(f"   🔧 Further optimization may be needed")
