<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance & Accounts - Medivent ERP</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .border-left-primary { border-left: 4px solid #4e73df !important; }
        .border-left-success { border-left: 4px solid #1cc88a !important; }
        .border-left-info { border-left: 4px solid #36b9cc !important; }
        .border-left-warning { border-left: 4px solid #f6c23e !important; }
        .border-left-danger { border-left: 4px solid #e74a3b !important; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4 mt-4">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-money-bill-wave text-primary"></i>
                Finance & Accounts
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('finance_pending_invoices') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-clock"></i> Pending Invoices
                </a>
                <a href="{{ url_for('finance_payment_collection') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-money-bill-wave"></i> Payment Collection
                </a>
                <a href="{{ url_for('finance_customer_ledger') }}" class="btn btn-info btn-sm">
                    <i class="fas fa-users"></i> Customer Ledger
                </a>
                <a href="{{ url_for('finance_bank_details') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-university"></i> Bank Details
                </a>
                <a href="{{ url_for('finance_financial_reports') }}" class="btn btn-dark btn-sm">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </div>
        </div>

        <!-- Statistics Cards Row -->
        <div class="row">
            <!-- Pending Invoices -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2 stats-card">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    PENDING INVOICES
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ stats.pending_invoices }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Receivables -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2 stats-card">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    TOTAL RECEIVABLES
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    ₹{{ "{:,.0f}".format(stats.total_receivables) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Collections -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2 stats-card">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    TODAY'S COLLECTIONS
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    ₹{{ "{:,.0f}".format(stats.total_collections_today) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overdue Invoices -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2 stats-card">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    OVERDUE INVOICES
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ stats.overdue_invoices }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Invoices Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-invoice"></i> Recent Invoices
                </h6>
                <a href="{{ url_for('finance_invoices') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-list"></i> View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_invoices %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fas fa-hashtag"></i> Invoice #</th>
                                <th><i class="fas fa-user"></i> Customer</th>
                                <th><i class="fas fa-calendar"></i> Date</th>
                                <th><i class="fas fa-money-bill"></i> Amount</th>
                                <th><i class="fas fa-info-circle"></i> Status</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in recent_invoices %}
                            <tr>
                                <td><strong>{{ invoice.invoice_number }}</strong></td>
                                <td>{{ invoice.customer_name }}</td>
                                <td>{{ invoice.date_generated }}</td>
                                <td><strong>₹{{ "{:,.0f}".format(invoice.order_amount) }}</strong></td>
                                <td>
                                    {% if invoice.payment_status == 'paid' %}
                                    <span class="badge badge-success">
                                        <i class="fas fa-check"></i> Paid
                                    </span>
                                    {% elif invoice.payment_status == 'partial' %}
                                    <span class="badge badge-warning">
                                        <i class="fas fa-clock"></i> Partial
                                    </span>
                                    {% else %}
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times"></i> Unpaid
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-info btn-sm" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-primary btn-sm" title="View Invoice">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                        <a href="#" class="btn btn-success btn-sm" title="Download PDF">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Recent Invoices</h5>
                    <p class="text-muted">No invoices have been generated recently.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('finance_pending_invoices') }}" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-clock"></i><br>
                            Review Pending<br>Invoices
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('finance_payment_collection') }}" class="btn btn-outline-success btn-block">
                            <i class="fas fa-money-bill-wave"></i><br>
                            Record<br>Payment
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('finance_customer_ledger') }}" class="btn btn-outline-info btn-block">
                            <i class="fas fa-users"></i><br>
                            Customer<br>Ledger
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('finance_financial_reports') }}" class="btn btn-outline-dark btn-block">
                            <i class="fas fa-chart-bar"></i><br>
                            Financial<br>Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center py-3">
            <small class="text-muted">
                <i class="fas fa-clock"></i> Last updated: {{ now.strftime('%Y-%m-%d %H:%M:%S') }}
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
