{% extends "base.html" %}
{% block title %}Set Customer Pricing - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Set Customer Pricing</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View All Customers
                            </a>
                            <a href="{{ url_for('customers', view='by_type') }}" class="btn btn-secondary">
                                <i class="fas fa-user-tag"></i> View Customers by Type
                            </a>
                            <a href="{{ url_for('customers', action='add') }}" class="btn btn-secondary">
                                <i class="fas fa-user-plus"></i> Add New Customer
                            </a>
                            <a href="{{ url_for('customers', view='pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-tags"></i> View Customer Pricing
                            </a>
                            <a href="{{ url_for('customers', view='orders') }}" class="btn btn-secondary">
                                <i class="fas fa-history"></i> View Customer Order History
                            </a>
                        </div>
                    </div>

                    <!-- Set Pricing Form -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Set Custom Pricing</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="{{ url_for('customers', action='set_pricing') }}">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="customer_id">Select Customer:</label>
                                                    <select class="form-control" id="customer_id" name="customer_id" required>
                                                        <option value="">-- Select Customer --</option>
                                                        {% for customer in customers %}
                                                        <option value="{{ customer.customer_id }}">{{ customer.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="product_id">Select Product:</label>
                                                    <select class="form-control" id="product_id" name="product_id" required>
                                                        <option value="">-- Select Product --</option>
                                                        {% for product in products %}
                                                        <option value="{{ product.product_id }}" data-price="{{ product.standard_price }}">
                                                            {{ product.name }} - {{ product.strength }} (₨{{ product.standard_price }})
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="standard_price">Standard Price:</label>
                                                    <input type="number" class="form-control" id="standard_price" name="standard_price" step="0.01" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="customer_price">Customer Price:</label>
                                                    <input type="number" class="form-control" id="customer_price" name="customer_price" step="0.01" required>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="discount_percentage">Discount %:</label>
                                                    <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" step="0.01" readonly>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label for="notes">Notes (Optional):</label>
                                                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any special notes about this pricing..."></textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-save"></i> Set Pricing
                                                </button>
                                                <button type="reset" class="btn btn-secondary">
                                                    <i class="fas fa-undo"></i> Reset
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Pricing Preview -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Current Custom Pricing</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Customer</th>
                                                    <th>Product</th>
                                                    <th>Strength</th>
                                                    <th>Standard Price</th>
                                                    <th>Customer Price</th>
                                                    <th>Discount %</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>City Hospital</td>
                                                    <td>Paracetamol</td>
                                                    <td>500mg</td>
                                                    <td>₨250.00</td>
                                                    <td>₨225.00</td>
                                                    <td>10.0%</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> Remove
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Medicare Pharmacy</td>
                                                    <td>Amoxicillin</td>
                                                    <td>250mg</td>
                                                    <td>₨350.00</td>
                                                    <td>₨315.00</td>
                                                    <td>10.0%</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> Remove
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Health Plus Clinic</td>
                                                    <td>Ibuprofen</td>
                                                    <td>400mg</td>
                                                    <td>₨180.00</td>
                                                    <td>₨162.00</td>
                                                    <td>10.0%</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> Remove
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Update standard price when product is selected
    $('#product_id').change(function() {
        var selectedOption = $(this).find('option:selected');
        var standardPrice = selectedOption.data('price');
        $('#standard_price').val(standardPrice);
        $('#customer_price').val('');
        $('#discount_percentage').val('');
    });

    // Calculate discount percentage when customer price is entered
    $('#customer_price').on('input', function() {
        var standardPrice = parseFloat($('#standard_price').val()) || 0;
        var customerPrice = parseFloat($(this).val()) || 0;
        
        if (standardPrice > 0 && customerPrice > 0) {
            var discount = ((standardPrice - customerPrice) / standardPrice) * 100;
            $('#discount_percentage').val(discount.toFixed(2));
        } else {
            $('#discount_percentage').val('');
        }
    });
});
</script>
{% endblock %}
