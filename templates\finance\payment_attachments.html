{% extends "base.html" %}

{% block title %}Payment Attachments - {{ payment.payment_id }}{% endblock %}

{% block content %}
<style>
    .finance-dashboard {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .finance-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .finance-title {
        color: white;
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .finance-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }
    
    .content-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }
    
    .payment-info-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .attachment-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .attachment-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .attachment-preview {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 10px;
        border: 2px solid #e9ecef;
    }
    
    .attachment-info {
        flex: 1;
        margin-left: 15px;
    }
    
    .attachment-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }
    
    .attachment-meta {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .attachment-actions {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .btn-modern {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .btn-modern:hover {
        transform: translateY(-1px);
    }
    
    .upload-area {
        border: 2px dashed #ddd;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #f8f9fa;
    }
    
    .upload-area:hover {
        border-color: var(--primary);
        background: rgba(var(--primary-rgb), 0.05);
    }
    
    .upload-area.dragover {
        border-color: var(--primary);
        background: rgba(var(--primary-rgb), 0.1);
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-paperclip me-3"></i>Payment Attachments
                    </h1>
                    <p class="finance-subtitle">Payment ID: {{ payment.payment_id }} | Order: {{ payment.order_id }}</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('finance_payment_collection') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left me-2"></i>Back to Collection
                    </a>
                    <button class="btn btn-light btn-modern" onclick="downloadAllAttachments()">
                        <i class="fas fa-download me-2"></i>Download All
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <!-- Payment Information -->
                <div class="payment-info-card">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2 text-primary"></i>Payment Information
                    </h5>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Payment ID:</strong></div>
                        <div class="col-7">{{ payment.payment_id }}</div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Order ID:</strong></div>
                        <div class="col-7">{{ payment.order_id }}</div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Amount:</strong></div>
                        <div class="col-7 text-success fw-bold">₹{{ "{:,.0f}".format(payment.payment_amount) }}</div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Method:</strong></div>
                        <div class="col-7">
                            <span class="badge bg-secondary">{{ payment.payment_method.title() }}</span>
                        </div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Type:</strong></div>
                        <div class="col-7">
                            <span class="badge bg-info">{{ payment.payment_type.title() }}</span>
                        </div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Date:</strong></div>
                        <div class="col-7">{{ payment.payment_date.strftime('%d %b %Y, %I:%M %p') if payment.payment_date else 'N/A' }}</div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-5"><strong>Processed By:</strong></div>
                        <div class="col-7">{{ payment.processed_by or 'System' }}</div>
                    </div>
                    
                    {% if payment.cheque_number %}
                    <hr>
                    <h6 class="text-primary">Cheque Details</h6>
                    <div class="row mb-2">
                        <div class="col-5"><strong>Cheque No:</strong></div>
                        <div class="col-7">{{ payment.cheque_number }}</div>
                    </div>
                    {% if payment.cheque_date %}
                    <div class="row mb-2">
                        <div class="col-5"><strong>Cheque Date:</strong></div>
                        <div class="col-7">{{ payment.cheque_date }}</div>
                    </div>
                    {% endif %}
                    {% if payment.bank_name %}
                    <div class="row mb-2">
                        <div class="col-5"><strong>Bank:</strong></div>
                        <div class="col-7">{{ payment.bank_name }}</div>
                    </div>
                    {% endif %}
                    {% endif %}
                    
                    {% if payment.payment_notes %}
                    <hr>
                    <h6 class="text-primary">Notes</h6>
                    <p class="text-muted small">{{ payment.payment_notes }}</p>
                    {% endif %}
                </div>
                
                <!-- Add New Attachment -->
                <div class="content-card">
                    <h5 class="mb-3">
                        <i class="fas fa-plus me-2 text-success"></i>Add New Attachment
                    </h5>
                    
                    <form method="POST" action="{{ url_for('add_payment_attachment', payment_id=payment.payment_id) }}" enctype="multipart/form-data">
                        <div class="upload-area" onclick="document.getElementById('attachment_file').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h6>Click to upload or drag and drop</h6>
                            <p class="text-muted small mb-0">Supports: JPG, PNG, PDF (Max 5MB)</p>
                            <input type="file" id="attachment_file" name="attachment_file" 
                                   accept="image/*,.pdf" style="display: none;" required>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">Description</label>
                            <input type="text" class="form-control" name="description" 
                                   placeholder="Brief description of the attachment">
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-modern w-100 mt-3">
                            <i class="fas fa-upload me-2"></i>Upload Attachment
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="col-md-8">
                <!-- Attachments List -->
                <div class="content-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">
                            <i class="fas fa-paperclip me-2 text-primary"></i>Attachments
                            <span class="badge bg-primary ms-2">{{ attachments|length }}</span>
                        </h5>
                    </div>
                    
                    {% if attachments %}
                        {% for attachment in attachments %}
                        <div class="attachment-card">
                            <div class="d-flex align-items-center">
                                <div class="attachment-preview-container">
                                    {% if attachment.original_filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                                        <img src="{{ url_for('serve_uploaded_file', file_id=attachment.file_upload_id) }}" 
                                             class="attachment-preview" alt="Attachment preview">
                                    {% else %}
                                        <div class="attachment-preview d-flex align-items-center justify-content-center bg-light">
                                            <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="attachment-info">
                                    <div class="attachment-name">{{ attachment.original_filename }}</div>
                                    <div class="attachment-meta">
                                        <i class="fas fa-calendar me-1"></i>{{ attachment.upload_date.strftime('%d %b %Y, %I:%M %p') if attachment.upload_date else 'N/A' }}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-hdd me-1"></i>{{ "{:.2f}".format((attachment.file_size or 0) / 1024 / 1024) }} MB
                                        {% if attachment.description %}
                                            <br><small class="text-muted">{{ attachment.description }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="attachment-actions">
                                    <a href="{{ url_for('serve_uploaded_file', file_id=attachment.file_upload_id) }}" 
                                       target="_blank" class="btn btn-outline-primary btn-modern btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('serve_uploaded_file', file_id=attachment.file_upload_id) }}?download=1" 
                                       class="btn btn-outline-success btn-modern btn-sm">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <form method="POST" action="{{ url_for('remove_payment_attachment', payment_id=payment.payment_id, attachment_id=attachment.id) }}" 
                                          style="display: inline;" onsubmit="return confirm('Are you sure you want to remove this attachment?')">
                                        <button type="submit" class="btn btn-outline-danger btn-modern btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-paperclip fa-4x text-muted mb-3"></i>
                            <h6 class="text-muted">No attachments found</h6>
                            <p class="text-muted">Upload payment documents using the form on the left.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadAllAttachments() {
    // Implementation for downloading all attachments as a ZIP file
    alert('Download all attachments feature will be implemented');
}

// Drag and drop functionality
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.upload-area');
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            document.getElementById('attachment_file').files = files;
        }
    });
});
</script>

{% endblock %}
