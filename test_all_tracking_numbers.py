"""
Comprehensive test of all tracking numbers from sample.txt
"""

import asyncio
import json
import time
from tcs_scraper_final import TCSTrackerFinal

async def test_all_tracking_numbers():
    """Test all tracking numbers from the sample file"""
    
    # Read tracking numbers from sample file
    try:
        with open('tcs/sample.txt', 'r') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print("❌ sample.txt not found in tcs folder")
        return
    
    # Extract valid tracking numbers
    tracking_numbers = []
    for line in lines:
        line = line.strip()
        if line.isdigit() and len(line) >= 8:
            tracking_numbers.append(line)
    
    print(f"🔍 Found {len(tracking_numbers)} tracking numbers to test")
    print(f"📋 Numbers: {', '.join(tracking_numbers)}")
    
    # Initialize tracker
    tracker = TCSTrackerFinal(headless=True)  # Use headless for batch testing
    
    results = {}
    successful_count = 0
    failed_count = 0
    
    print(f"\n🚀 Starting comprehensive test...")
    print("=" * 60)
    
    for i, number in enumerate(tracking_numbers, 1):
        print(f"\n📦 Testing {i}/{len(tracking_numbers)}: {number}")
        
        try:
            start_time = time.time()
            result = await tracker.track_shipment(number)
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result.get('success'):
                successful_count += 1
                status = result.get('current_status', 'Unknown')
                origin = result.get('origin', 'N/A')
                destination = result.get('destination', 'N/A')
                history_count = len(result.get('track_history', []))
                
                print(f"   ✅ SUCCESS ({duration:.1f}s)")
                print(f"      Status: {status}")
                print(f"      Route: {origin} → {destination}")
                print(f"      History: {history_count} entries")
                
                # Show first history entry if available
                if result.get('track_history'):
                    first_entry = result['track_history'][0]
                    print(f"      Latest: {first_entry.get('status', 'N/A')} ({first_entry.get('date_time', 'N/A')})")
                    
            else:
                failed_count += 1
                error = result.get('error', 'Unknown error')
                print(f"   ❌ FAILED ({duration:.1f}s)")
                print(f"      Error: {error}")
            
            results[number] = result
            
        except Exception as e:
            failed_count += 1
            print(f"   💥 EXCEPTION: {e}")
            results[number] = {
                'success': False,
                'error': f'Exception: {str(e)}',
                'tracking_number': number
            }
        
        # Add delay between requests to be respectful
        if i < len(tracking_numbers):
            print(f"   ⏳ Waiting 2 seconds...")
            await asyncio.sleep(2)
    
    print("\n" + "=" * 60)
    print(f"🏁 TEST COMPLETE")
    print(f"✅ Successful: {successful_count}")
    print(f"❌ Failed: {failed_count}")
    print(f"📊 Success Rate: {(successful_count/len(tracking_numbers)*100):.1f}%")
    
    # Save detailed results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    results_file = f"tracking_test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Detailed results saved to: {results_file}")
    
    # Generate summary report
    summary_file = f"tracking_summary_{timestamp}.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("TCS TRACKING TEST SUMMARY\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total Numbers Tested: {len(tracking_numbers)}\n")
        f.write(f"Successful: {successful_count}\n")
        f.write(f"Failed: {failed_count}\n")
        f.write(f"Success Rate: {(successful_count/len(tracking_numbers)*100):.1f}%\n\n")
        
        f.write("SUCCESSFUL TRACKING NUMBERS:\n")
        f.write("-" * 30 + "\n")
        for number, result in results.items():
            if result.get('success'):
                f.write(f"{number}: {result.get('current_status', 'Unknown')} - {result.get('origin', 'N/A')} → {result.get('destination', 'N/A')}\n")
        
        f.write("\nFAILED TRACKING NUMBERS:\n")
        f.write("-" * 30 + "\n")
        for number, result in results.items():
            if not result.get('success'):
                f.write(f"{number}: {result.get('error', 'Unknown error')}\n")
    
    print(f"📄 Summary report saved to: {summary_file}")
    
    # Show recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if successful_count > 0:
        print(f"   ✅ System is working! {successful_count} numbers tracked successfully")
        print(f"   🔧 Use successful numbers for demo: {[n for n, r in results.items() if r.get('success')][:3]}")
    else:
        print(f"   ⚠️  No successful tracking - check TCS website structure")
        print(f"   🔍 Review error messages for debugging")
    
    if failed_count > 0:
        print(f"   📝 {failed_count} numbers failed - may be invalid or expired")
    
    return results

async def test_single_number(tracking_number: str):
    """Test a single tracking number with detailed output"""
    print(f"🔍 Testing single number: {tracking_number}")
    
    tracker = TCSTrackerFinal(headless=False)  # Show browser for debugging
    
    try:
        result = await tracker.track_shipment(tracking_number)
        print(f"\n📋 RESULT:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Test single number
        number = sys.argv[1]
        asyncio.run(test_single_number(number))
    else:
        # Test all numbers
        asyncio.run(test_all_tracking_numbers())
