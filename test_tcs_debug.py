"""
Debug script to analyze TCS website structure
"""

import asyncio
from playwright.async_api import async_playwright
import json

async def debug_tcs_website():
    """Debug the TCS website to understand its structure"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Run with GUI to see what's happening
        page = await browser.new_page()
        
        # Set user agent
        await page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        tracking_number = "31442084039"
        url = f"https://www.tcsexpress.com/track?tracking_number={tracking_number}"
        
        print(f"Navigating to: {url}")
        await page.goto(url, wait_until='networkidle')
        
        # Wait for content to load
        await asyncio.sleep(5)
        
        # Take a screenshot for debugging
        await page.screenshot(path="tcs_debug.png")
        
        # Get page title
        title = await page.title()
        print(f"Page title: {title}")
        
        # Check if main content exists
        main_content = await page.query_selector('#main-content-app')
        if main_content:
            print("✅ Main content app found")
        else:
            print("❌ Main content app not found")
            
        # Look for the tracking data container
        containers = [
            'div.w-\\[70rem\\].shadow-card.p-8',
            'div[class*="shadow-card"]',
            'div[class*="w-"]',
            '.shadow-card'
        ]
        
        for selector in containers:
            element = await page.query_selector(selector)
            if element:
                print(f"✅ Found container with selector: {selector}")
                # Get the HTML content
                html = await element.inner_html()
                print(f"Container HTML length: {len(html)}")
                break
        else:
            print("❌ No tracking container found")
            
        # Get all text content to see what's available
        all_text = await page.text_content('body')
        print(f"\n=== Page Text Content ===")
        print(all_text[:1000] + "..." if len(all_text) > 1000 else all_text)
        
        # Look for specific text patterns
        patterns = [
            "Tracking Number",
            "Origin",
            "Destination", 
            "Booking Date",
            "Current Status",
            "Delivered",
            "Track History"
        ]
        
        print(f"\n=== Text Pattern Analysis ===")
        for pattern in patterns:
            if pattern.lower() in all_text.lower():
                print(f"✅ Found: {pattern}")
            else:
                print(f"❌ Missing: {pattern}")
                
        # Try to find elements by text
        print(f"\n=== Element Analysis ===")
        try:
            tracking_elements = await page.locator(f'text="{tracking_number}"').all()
            print(f"Found {len(tracking_elements)} elements with tracking number")
        except:
            print("No tracking number elements found")
            
        # Check for error messages
        error_selectors = [
            'text="No tracking information found"',
            'text="Invalid tracking number"',
            'text="Error"',
            '.error',
            '.alert'
        ]
        
        for selector in error_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    error_text = await element.text_content()
                    print(f"⚠️ Error found: {error_text}")
            except:
                pass
                
        # Save the full HTML for analysis
        html_content = await page.content()
        with open("tcs_debug.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        print("✅ Full HTML saved to tcs_debug.html")
        
        # Wait for user to inspect
        print("\n=== Waiting for manual inspection ===")
        print("Check the browser window and press Enter to continue...")
        input()
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_tcs_website())
