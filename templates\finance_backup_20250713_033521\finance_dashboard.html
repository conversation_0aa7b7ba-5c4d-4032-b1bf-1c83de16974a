{% extends "base.html" %}

{% block title %}Finance Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave"></i> Finance Dashboard
                    </h4>
                    <small>Comprehensive financial overview and management</small>
                </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Revenue</h6>
                                    <h3 class="mb-0">₹{{ finance_summary.total_revenue | format_currency }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                            <small class="text-light">
                                <i class="fas fa-arrow-up"></i> 
                                {{ finance_summary.revenue_growth }}% from last month
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Paid Invoices</h6>
                                    <h3 class="mb-0">₹{{ finance_summary.paid_amount | format_currency }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                            <small class="text-light">
                                {{ finance_summary.paid_invoices_count }} invoices paid
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Pending Amount</h6>
                                    <h3 class="mb-0">₹{{ finance_summary.pending_amount | format_currency }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                            <small class="text-light">
                                {{ finance_summary.pending_invoices_count }} pending invoices
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Overdue Amount</h6>
                                    <h3 class="mb-0">₹{{ finance_summary.overdue_amount | format_currency }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                            <small class="text-light">
                                {{ finance_summary.overdue_invoices_count }} overdue invoices
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <a href="{{ url_for('finance_pending_invoices') }}" class="btn btn-warning btn-block">
                                        <i class="fas fa-file-invoice-dollar"></i><br>
                                        Pending Invoices
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('finance_payment_collection') }}" class="btn btn-success btn-block">
                                        <i class="fas fa-hand-holding-usd"></i><br>
                                        Collect Payment
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('finance_customer_ledger') }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-users"></i><br>
                                        Customer Ledger
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('finance_bank_details') }}" class="btn btn-info btn-block">
                                        <i class="fas fa-university"></i><br>
                                        Bank Details
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('finance_financial_reports') }}" class="btn btn-secondary btn-block">
                                        <i class="fas fa-chart-pie"></i><br>
                                        Financial Reports
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-dark btn-block" onclick="generateInvoice()">
                                        <i class="fas fa-file-alt"></i><br>
                                        Generate Invoice
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-history"></i> Recent Transactions</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Customer</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for transaction in recent_transactions %}
                                        <tr>
                                            <td>{{ transaction.transaction_date | format_datetime('%Y-%m-%d') }}</td>
                                            <td>{{ transaction.customer_name }}</td>
                                            <td>
                                                <span class="badge badge-{% if transaction.transaction_type == 'payment' %}success{% else %}info{% endif %}">
                                                    {{ transaction.transaction_type|title }}
                                                </span>
                                            </td>
                                            <td>₹{{ transaction.amount | format_currency }}</td>
                                            <td>
                                                <span class="badge badge-{% if transaction.status == 'completed' %}success{% elif transaction.status == 'pending' %}warning{% else %}danger{% endif %}">
                                                    {{ transaction.status|title }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="{{ url_for('finance_customer_ledger') }}" class="btn btn-primary">
                                    View All Transactions
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Top Customers by Outstanding -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0"><i class="fas fa-users"></i> Top Outstanding Customers</h5>
                        </div>
                        <div class="card-body">
                            {% for customer in top_outstanding_customers %}
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <strong>{{ customer.customer_name }}</strong><br>
                                    <small class="text-muted">{{ customer.customer_id }}</small>
                                </div>
                                <div class="text-right">
                                    <span class="badge badge-{% if customer.outstanding_amount > 100000 %}danger{% elif customer.outstanding_amount > 50000 %}warning{% else %}info{% endif %} badge-lg">
                                        ₹{{ customer.outstanding_amount | format_currency }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                            <div class="text-center mt-3">
                                <a href="{{ url_for('finance_customer_ledger') }}" class="btn btn-warning btn-sm">
                                    View All Customers
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Charts -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Monthly Revenue Trend</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Payment Status Distribution</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="paymentStatusChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aging Analysis -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-calendar-times"></i> Accounts Receivable Aging</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Customer</th>
                                            <th>Current (0-30 days)</th>
                                            <th>31-60 days</th>
                                            <th>61-90 days</th>
                                            <th>Over 90 days</th>
                                            <th>Total Outstanding</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for aging in aging_analysis %}
                                        <tr>
                                            <td>
                                                <strong>{{ aging.customer_name }}</strong><br>
                                                <small class="text-muted">{{ aging.customer_id }}</small>
                                            </td>
                                            <td>₹{{ aging.current_amount | format_currency }}</td>
                                            <td>₹{{ aging.days_31_60 | format_currency }}</td>
                                            <td>₹{{ aging.days_61_90 | format_currency }}</td>
                                            <td class="text-danger">₹{{ aging.over_90_days | format_currency }}</td>
                                            <td><strong>₹{{ aging.total_outstanding | format_currency }}</strong></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: {{ revenue_chart_labels | tojson }},
            datasets: [{
                label: 'Revenue',
                data: {{ revenue_chart_data | tojson }},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Payment Status Chart
    const paymentCtx = document.getElementById('paymentStatusChart').getContext('2d');
    new Chart(paymentCtx, {
        type: 'doughnut',
        data: {
            labels: ['Paid', 'Pending', 'Overdue'],
            datasets: [{
                data: [
                    {{ finance_summary.paid_amount }},
                    {{ finance_summary.pending_amount }},
                    {{ finance_summary.overdue_amount }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

// Generate Invoice function
function generateInvoice() {
    const customerId = prompt('Enter Customer ID for invoice generation:');
    if (customerId) {
        window.location.href = `/finance/generate-invoice/${customerId}`;
    }
}

// Auto-refresh data every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
