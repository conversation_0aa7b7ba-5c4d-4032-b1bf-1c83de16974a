{% extends 'base.html' %}

{% block title %}Batch Tracking{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Batch Tracking</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('batches') }}" method="get" class="form-inline">
                                <div class="input-group w-100">
                                    <input type="text" name="q" class="form-control" placeholder="Search by Batch Number, Product, Country, or Manufacturer" value="{{ search_query }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> View All Batches
                            </a>
                            <a href="{{ url_for('batches', view='by_product') }}" class="btn btn-secondary">
                                <i class="fas fa-pills"></i> View Batches by Product
                            </a>
                            <a href="{{ url_for('batches', view='details') }}" class="btn btn-secondary">
                                <i class="fas fa-info-circle"></i> View Batch Details
                            </a>
                            <a href="{{ url_for('batches', view='movements') }}" class="btn btn-secondary">
                                <i class="fas fa-exchange-alt"></i> View Batch Movements
                            </a>
                            <a href="{{ url_for('batches', view='deliveries') }}" class="btn btn-secondary">
                                <i class="fas fa-truck"></i> View Batch Deliveries
                            </a>
                            <a href="{{ url_for('batches', view='expiring') }}" class="btn btn-warning">
                                <i class="fas fa-exclamation-triangle"></i> View Expiring Batches
                            </a>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addBatchModal">
                                <i class="fas fa-plus-circle"></i> Add New Batch
                            </button>
                            <a href="{{ url_for('batches', action='movement') }}" class="btn btn-primary">
                                <i class="fas fa-dolly"></i> Record Batch Movement
                            </a>
                            <a href="{{ url_for('batches', action='delivery') }}" class="btn btn-primary">
                                <i class="fas fa-shipping-fast"></i> Record Batch Delivery
                            </a>
                        </div>
                    </div>

                    {% if action == 'add' %}
                    <!-- Add Batch Form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Add New Batch</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('batches', action='add') }}">
                                        <div class="form-group">
                                            <label for="product_id">Product</label>
                                            <select class="form-control" id="product_id" name="product_id" required>
                                                <option value="">-- Select Product --</option>
                                                {% for product in products %}
                                                <option value="{{ product.product_id }}">{{ product.name }} ({{ product.strength }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="batch_number">Batch Number</label>
                                            <input type="text" class="form-control" id="batch_number" name="batch_number" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="quantity">Quantity</label>
                                            <input type="number" class="form-control" id="quantity" name="quantity" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="expiry_date">Expiry Date</label>
                                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="location">Location</label>
                                            <select class="form-control" id="location" name="location" required>
                                                <option value="Karachi">Karachi</option>
                                                <option value="Lahore">Lahore</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Add Batch</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <!-- Batches List -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Batch Number</th>
                                    <th>Country</th>
                                    <th>Manufacturer</th>
                                    <th>Division</th>
                                    <th>Quantity</th>
                                    <th>Expiry Date</th>
                                    <th>Location</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if batches %}
                                    {% for batch in batches %}
                                    <tr>
                                        <td>{{ batch.product_name }}</td>
                                        <td>{{ batch.strength }}</td>
                                        <td><strong><a href="{{ url_for('batch_details_by_number', batch_number=batch.batch_number) }}">{{ batch.batch_number }}</a></strong></td>
                                        <td>{{ batch.country or 'N/A' }}</td>
                                        <td>{{ batch.manufacturer or 'Generic' }}</td>
                                        <td>{{ batch.division or 'Unassigned' }}</td>
                                        <td>{{ batch.stock_quantity }}</td>
                                        <td>{{ batch.expiry_date }}</td>
                                        <td>{{ batch.warehouse_name }}</td>
                                        <td>
                                            <a href="{{ url_for('batch_details_by_number', batch_number=batch.batch_number) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-info-circle"></i> Details
                                            </a>
                                            <a href="{{ url_for('batches', view='movements', batch_id=batch.batch_id) }}" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-exchange-alt"></i> Movements
                                            </a>
                                            <a href="{{ url_for('batches', view='deliveries', batch_id=batch.batch_id) }}" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-truck"></i> Deliveries
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">No batches found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Batch Modal -->
<div class="modal fade" id="addBatchModal" tabindex="-1" role="dialog" aria-labelledby="addBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addBatchModalLabel">Add New Batch</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{{ url_for('batches', action='add') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="product_id">Product</label>
                        <select class="form-control" id="product_id" name="product_id" required>
                            <option value="">-- Select Product --</option>
                            {% for product in products %}
                            <option value="{{ product.product_id }}">{{ product.name }} ({{ product.strength }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="batch_number">Batch Number</label>
                        <input type="text" class="form-control" id="batch_number" name="batch_number" required>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" required>
                    </div>
                    <div class="form-group">
                        <label for="expiry_date">Expiry Date</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location</label>
                        <select class="form-control" id="location" name="location" required>
                            <option value="Karachi">Karachi</option>
                            <option value="Lahore">Lahore</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Add Batch</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
