{% extends 'base.html' %}

{% block title %}Invoice Search Results{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Invoice Search Results</h5>
                        <a href="{{ url_for('finance', view='payments') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Payments
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if search_query %}
                    <div class="alert alert-info">
                        <i class="fas fa-search"></i> Search results for: <strong>"{{ search_query }}"</strong>
                        {% if results %}
                        - Found {{ results|length }} result(s)
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if results %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Total Amount</th>
                                    <th>Paid Amount</th>
                                    <th>Outstanding</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in results %}
                                <tr>
                                    <td><strong class="text-primary">{{ result.invoice_number }}</strong></td>
                                    <td>{{ result.order_id }}</td>
                                    <td>{{ result.customer_name }}</td>
                                    <td>{{ result.order_date }}</td>
                                    <td>{{ result.order_amount|format_currency }}</td>
                                    <td>{{ result.paid_amount|format_currency }}</td>
                                    <td>
                                        <span class="badge {% if result.outstanding_amount > 0 %}badge-danger{% else %}badge-success{% endif %}">
                                            {{ result.outstanding_amount|format_currency }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if result.outstanding_amount <= 0 %}
                                        <span class="badge badge-success">Paid</span>
                                        {% elif result.paid_amount > 0 %}
                                        <span class="badge badge-warning">Partial</span>
                                        {% else %}
                                        <span class="badge badge-danger">Unpaid</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if result.outstanding_amount > 0 %}
                                        <a href="{{ url_for('finance', view='payments') }}?invoice_id={{ result.invoice_number }}" 
                                           class="btn btn-sm btn-success">
                                            <i class="fas fa-money-bill"></i> Record Payment
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('finance_invoice_details', invoice_number=result.invoice_number) }}" 
                                           class="btn btn-sm btn-info ml-1">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>No results found</strong><br>
                        {% if search_query %}
                        No invoices or orders found matching "{{ search_query }}". Try searching with:
                        <ul class="list-unstyled mt-2">
                            <li>• Invoice number (e.g., INV12345)</li>
                            <li>• Order ID (e.g., ORD007963)</li>
                            <li>• Customer name</li>
                            <li>• Phone number</li>
                        </ul>
                        {% else %}
                        Please enter a search term to find invoices or orders.
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Search Again -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('search_invoice') }}" method="GET">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="q" 
                                           placeholder="Search again..." value="{{ search_query }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="text-right">
                                <a href="{{ url_for('finance', view='payments') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Payment Processing
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Auto-focus search input
        $('input[name="q"]').focus();
        
        // Handle enter key in search
        $('input[name="q"]').on('keypress', function(e) {
            if (e.which === 13) {
                $(this).closest('form').submit();
            }
        });
    });
</script>
{% endblock %}
