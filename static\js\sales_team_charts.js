// Sales Team Performance Charts

function initSalesTeamCharts() {
    // Get team data from the table
    const teamData = [];
    const table = document.querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        // Skip header rows and empty rows
        if (row.classList.contains('table-secondary') || row.querySelectorAll('td').length < 3) {
            return;
        }
        
        const cells = row.querySelectorAll('td');
        if (cells.length >= 6) {
            const agentName = cells[0].textContent.trim();
            if (agentName) {
                teamData.push({
                    agent: agentName,
                    orders: parseInt(cells[1].textContent) || 0,
                    sales: parseFloat(cells[2].textContent.replace(/[^\d.-]/g, '')) || 0,
                    avgOrder: parseFloat(cells[3].textContent.replace(/[^\d.-]/g, '')) || 0,
                    customers: parseInt(cells[4].textContent) || 0,
                    largestOrder: parseFloat(cells[5].textContent.replace(/[^\d.-]/g, '')) || 0
                });
            }
        }
    });
    
    // Get category data from the second table
    const categoryData = [];
    const tables = document.querySelectorAll('table');
    if (tables.length >= 2) {
        const categoryTable = tables[1];
        const categoryRows = categoryTable.querySelectorAll('tbody tr');
        
        let currentAgent = '';
        
        categoryRows.forEach(row => {
            const cells = row.querySelectorAll('td, th');
            
            // Check if this is a header row with agent name
            if (row.classList.contains('table-secondary') && cells.length > 0) {
                currentAgent = cells[0].textContent.trim();
                return;
            }
            
            // Regular data row
            if (cells.length >= 3 && currentAgent) {
                const category = cells[1].textContent.trim();
                if (category) {
                    categoryData.push({
                        agent: currentAgent,
                        category: category,
                        sales: parseFloat(cells[2].textContent.replace(/[^\d.-]/g, '')) || 0
                    });
                }
            }
        });
    }
    
    // Create charts
    if (teamData.length > 0) {
        // Team Performance Chart (Pie)
        const agentLabels = teamData.map(a => a.agent);
        const salesData = teamData.map(a => a.sales);
        const ordersData = teamData.map(a => a.orders);
        const colors = generateColors(teamData.length);
        
        createPieChart(
            'teamPerformanceChart',
            agentLabels,
            salesData,
            colors,
            'white'
        );
        
        // Team Orders Chart (Doughnut)
        createDoughnutChart(
            'teamOrdersChart',
            agentLabels,
            ordersData,
            colors,
            'white'
        );
        
        // Process data for nested charts
        if (categoryData.length > 0) {
            // Group categories by agent
            const agentCategories = {};
            categoryData.forEach(item => {
                if (!agentCategories[item.agent]) {
                    agentCategories[item.agent] = [];
                }
                agentCategories[item.agent].push({
                    category: item.category,
                    sales: item.sales
                });
            });
            
            // Prepare data for nested charts
            const outerLabels = Object.keys(agentCategories);
            const outerData = outerLabels.map(agent => {
                return teamData.find(a => a.agent === agent)?.sales || 0;
            });
            
            // Get top categories across all agents
            const allCategories = {};
            categoryData.forEach(item => {
                if (!allCategories[item.category]) {
                    allCategories[item.category] = 0;
                }
                allCategories[item.category] += item.sales;
            });
            
            // Sort categories by sales and get top 5
            const topCategories = Object.entries(allCategories)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5)
                .map(entry => entry[0]);
            
            // Calculate sales for top categories
            const innerData = topCategories.map(category => {
                return categoryData
                    .filter(item => item.category === category)
                    .reduce((sum, item) => sum + item.sales, 0);
            });
            
            // Create nested pie charts
            createPieChart(
                'nestedOuterChart',
                outerLabels,
                outerData,
                colors,
                'white'
            );
            
            createDoughnutChart(
                'nestedInnerChart',
                topCategories,
                innerData,
                generateColors(topCategories.length),
                'white'
            );
        }
    }
}

// Initialize charts when the DOM is loaded
document.addEventListener('DOMContentLoaded', initSalesTeamCharts);
