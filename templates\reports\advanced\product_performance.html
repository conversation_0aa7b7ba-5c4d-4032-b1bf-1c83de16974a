{% extends 'reports/reports_base.html' %}

{% block title %}Product Performance Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block head %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/python_charts.js') }}"></script>
<script src="{{ url_for('static', filename='js/python_charts_init.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Product Performance Report</h4>
                    <div>
                        <button class="btn btn-light" id="printProductPerformanceBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('export_product_performance_pdf', start_date=start_date, end_date=end_date) }}" class="btn btn-light ml-2">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </a>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form id="dateRangeForm" action="{{ url_for('product_performance_report') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" id="start_date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" id="end_date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt"></i> Showing data from {{ start_date }} to {{ end_date }}
                            </div>
                        </div>
                    </div>

                    <!-- Python-processed charts section -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Python-Powered Product Analysis</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <div class="chart-title">Top Products by Sales</div>
                                                <div id="topProductsContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="topProductsSpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="topProductsImage" src="" alt="Top Products Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <div class="chart-title">Sales by Division</div>
                                                <div id="divisionSalesContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="divisionSalesSpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="divisionSalesImage" src="" alt="Division Sales Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="chart-container">
                                                <div class="chart-title">Division-Product Hierarchy (Sunburst)</div>
                                                <div class="alert alert-info mb-3">
                                                    <i class="fas fa-info-circle"></i> Click on any division in the chart to see detailed product sales analysis.
                                                </div>
                                                <div id="hierarchyContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="hierarchySpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="hierarchyImage" src="" alt="Division-Product Hierarchy Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if charts %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Division and Product Sales Analysis</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_pie %}
                                    <div id="nested-pie-chart"></div>
                                    <script>
                                        var nestedPieData = {{ charts.nested_pie|safe }};
                                        Plotly.newPlot('nested-pie-chart', nestedPieData.data, nestedPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Sales by Division</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.division_pie %}
                                    <div id="division-pie-chart"></div>
                                    <script>
                                        var divisionPieData = {{ charts.division_pie|safe }};
                                        Plotly.newPlot('division-pie-chart', divisionPieData.data, divisionPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Top Products by Sales</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.product_pie %}
                                    <div id="product-pie-chart"></div>
                                    <script>
                                        var productPieData = {{ charts.product_pie|safe }};
                                        Plotly.newPlot('product-pie-chart', productPieData.data, productPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Division-Product Hierarchy (Nested Analysis)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.sunburst %}
                                    <div id="sunburst-chart"></div>
                                    <script>
                                        var sunburstData = {{ charts.sunburst|safe }};
                                        Plotly.newPlot('sunburst-chart', sunburstData.data, sunburstData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Sales by Division (Nested Donut)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_donut_outer %}
                                    <div id="nested-donut-outer-chart"></div>
                                    <script>
                                        var nestedDonutOuterData = {{ charts.nested_donut_outer|safe }};
                                        Plotly.newPlot('nested-donut-outer-chart', nestedDonutOuterData.data, nestedDonutOuterData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("product_performance_report") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_donut_outer) {
                                                        var chartData = JSON.parse(data.charts.nested_donut_outer);
                                                        Plotly.react('nested-donut-outer-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Top Products by Division (Nested Donut)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_donut_inner %}
                                    <div id="nested-donut-inner-chart"></div>
                                    <script>
                                        var nestedDonutInnerData = {{ charts.nested_donut_inner|safe }};
                                        Plotly.newPlot('nested-donut-inner-chart', nestedDonutInnerData.data, nestedDonutInnerData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("product_performance_report") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_donut_inner) {
                                                        var chartData = JSON.parse(data.charts.nested_donut_inner);
                                                        Plotly.react('nested-donut-inner-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Top Products Performance</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.top_products_bar %}
                                    <div id="top-products-chart"></div>
                                    <script>
                                        var topProductsData = {{ charts.top_products_bar|safe }};
                                        Plotly.newPlot('top-products-chart', topProductsData.data, topProductsData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity Sold</th>
                                    <th>Total Sales (PKR)</th>
                                    <th>Orders</th>
                                    <th>Customers</th>
                                    <th>Avg. Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if product_data %}
                                    {% for product in product_data %}
                                    <tr>
                                        <td>{{ product.product_name }}</td>
                                        <td>{{ product.strength }}</td>
                                        <td>{{ product.total_quantity }}</td>
                                        <td>{{ "%.2f"|format(product.total_sales) }}</td>
                                        <td>{{ product.order_count }}</td>
                                        <td>{{ product.customer_count }}</td>
                                        <td>{{ "%.2f"|format(product.average_price) }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center">No product data available for the selected period</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>Monthly Trends</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Month</th>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>Sales (PKR)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if monthly_trends %}
                                            {% set current_month = '' %}
                                            {% for trend in monthly_trends %}
                                                {% if trend.month != current_month %}
                                                    {% set current_month = trend.month %}
                                                    <tr class="table-secondary">
                                                        <th colspan="4">{{ trend.month }}</th>
                                                    </tr>
                                                {% endif %}
                                                <tr>
                                                    <td></td>
                                                    <td>{{ trend.product_name }}</td>
                                                    <td>{{ trend.monthly_quantity }}</td>
                                                    <td>{{ "%.2f"|format(trend.monthly_sales) }}</td>
                                                </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center">No monthly trend data available</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block report_scripts %}
<script>
    // Load Python-generated charts
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Product performance page loaded');

        // Initialize charts directly
        setTimeout(function() {
            // Get date range
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;

            console.log(`Loading Python-generated charts with date range: ${startDate} to ${endDate}`);

            // Load product performance charts
            loadPythonProductCharts(startDate, endDate);
        }, 500); // Delay to ensure DOM is fully loaded
    });

    // Function to load Python-generated product charts
    function loadPythonProductCharts(startDate, endDate) {
        console.log(`Loading product charts for date range: ${startDate} to ${endDate}`);

        // Construct URL
        const url = `/python-charts/generate?type=product_performance&start_date=${startDate}&end_date=${endDate}`;

        // Fetch chart data
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Received product chart data:', data);

                // Display top products chart
                if (data.top_products_pie) {
                    document.getElementById('topProductsSpinner').style.display = 'none';
                    const imgElement = document.getElementById('topProductsImage');
                    imgElement.src = data.top_products_pie;
                    imgElement.style.display = 'block';
                }

                // Display division sales chart
                if (data.division_sales_pie) {
                    document.getElementById('divisionSalesSpinner').style.display = 'none';
                    const imgElement = document.getElementById('divisionSalesImage');
                    imgElement.src = data.division_sales_pie;
                    imgElement.style.display = 'block';
                }

                // Display division-product hierarchy chart
                if (data.division_product_hierarchy) {
                    document.getElementById('hierarchySpinner').style.display = 'none';
                    const imgElement = document.getElementById('hierarchyImage');
                    imgElement.src = data.division_product_hierarchy;
                    imgElement.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error loading product charts:', error);

                // Show error messages
                document.getElementById('topProductsSpinner').style.display = 'none';
                document.getElementById('topProductsContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';

                document.getElementById('divisionSalesSpinner').style.display = 'none';
                document.getElementById('divisionSalesContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';

                document.getElementById('hierarchySpinner').style.display = 'none';
                document.getElementById('hierarchyContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';
            });
    }
</script>
{% endblock %}

{% block report_styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printProductPerformanceBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}