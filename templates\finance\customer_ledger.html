{% extends "base.html" %}

{% block title %}Customer Ledger - Finance{% endblock %}

{% block content %}
<style>
    .customer-ledger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .page-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .ledger-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .customer-card {
        background: white;
        border: 1px solid var(--border);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .customer-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent));
    }
    
    .customer-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0,0,0,0.15);
        border-color: var(--primary);
    }
    
    .customer-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .customer-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--text);
        margin-bottom: 5px;
    }
    
    .customer-stats {
        display: flex;
        gap: 30px;
        margin-bottom: 20px;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .total-amount {
        color: var(--primary);
    }
    
    .paid-amount {
        color: var(--success);
    }
    
    .pending-amount {
        color: var(--warning);
    }
    
    .balance-indicator {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .balance-positive {
        background: rgba(76, 175, 80, 0.1);
        color: var(--success);
        border: 1px solid rgba(76, 175, 80, 0.3);
    }
    
    .balance-negative {
        background: rgba(244, 67, 54, 0.1);
        color: var(--error);
        border: 1px solid rgba(244, 67, 54, 0.3);
    }
    
    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid var(--border);
    }
    
    .btn-modern {
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
    }
    
    .btn-primary-modern {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
    }
    
    .btn-info-modern {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }
    
    .btn-warning-modern {
        background: linear-gradient(135deg, var(--warning), #e6ac00);
        color: white;
    }
    
    .summary-cards {
        margin-bottom: 30px;
    }
    
    .summary-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
    }
    
    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin: 0 auto 15px;
    }
    
    .summary-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .summary-label {
        color: var(--text-light);
        font-size: 0.9rem;
    }
</style>

<div class="customer-ledger">
    <div class="container-fluid">
        <!-- Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-users mr-3"></i>Customer Ledger
                    </h1>
                    <p class="page-subtitle">Customer account balances and transaction history</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Summary Cards -->
        <div class="row summary-cards">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, var(--primary), var(--secondary));">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="summary-value">{{ ledger|length }}</div>
                    <div class="summary-label">Total Customers</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, var(--success), #45a049);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="summary-value">₹{{ "{:,.0f}".format(ledger|sum(attribute='total_amount') or 0) }}</div>
                    <div class="summary-label">Total Business</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, var(--warning), #e6ac00);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-value">₹{{ "{:,.0f}".format(ledger|sum(attribute='pending_amount') or 0) }}</div>
                    <div class="summary-label">Total Pending</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, #4ECDC4, #44b3ac);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="summary-value">₹{{ "{:,.0f}".format(ledger|sum(attribute='paid_amount') or 0) }}</div>
                    <div class="summary-label">Total Collected</div>
                </div>
            </div>
        </div>
        
        <!-- Ledger Container -->
        <div class="ledger-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-list mr-2"></i>Customer Accounts
                </h4>
                
                <div class="d-flex gap-2">
                    <input type="text" class="form-control" placeholder="Search customers..." 
                           id="customer-search" style="width: 250px;">
                    <button class="btn btn-outline-primary btn-modern" onclick="refreshLedger()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            
            {% if ledger %}
                <div class="row" id="customer-list">
                    {% for customer in ledger %}
                    <div class="col-lg-6 col-xl-4 customer-item" data-customer="{{ customer.customer_name.lower() }}">
                        <div class="customer-card">
                            <div class="customer-header">
                                <div>
                                    <div class="customer-name">{{ customer.customer_name }}</div>
                                    <small class="text-muted">{{ customer.total_orders }} orders</small>
                                </div>
                                
                                {% if customer.pending_amount > 0 %}
                                    <span class="balance-indicator balance-negative">
                                        Outstanding
                                    </span>
                                {% else %}
                                    <span class="balance-indicator balance-positive">
                                        Clear
                                    </span>
                                {% endif %}
                            </div>
                            
                            <div class="customer-stats">
                                <div class="stat-item">
                                    <div class="stat-value total-amount">₹{{ "{:,.0f}".format(customer.total_amount) }}</div>
                                    <div class="stat-label">Total</div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-value paid-amount">₹{{ "{:,.0f}".format(customer.paid_amount) }}</div>
                                    <div class="stat-label">Paid</div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-value pending-amount">₹{{ "{:,.0f}".format(customer.pending_amount) }}</div>
                                    <div class="stat-label">Pending</div>
                                </div>
                            </div>
                            
                            <div class="action-buttons">
                                <button class="btn btn-primary-modern btn-sm" 
                                        onclick="viewCustomerDetails('{{ customer.customer_name }}')">
                                    <i class="fas fa-eye mr-1"></i>View Details
                                </button>
                                
                                <button class="btn btn-info-modern btn-sm" 
                                        onclick="generateStatement('{{ customer.customer_name }}')">
                                    <i class="fas fa-file-alt mr-1"></i>Statement
                                </button>
                                
                                {% if customer.pending_amount > 0 %}
                                <button class="btn btn-warning-modern btn-sm" 
                                        onclick="followUpPayment('{{ customer.customer_name }}')">
                                    <i class="fas fa-phone mr-1"></i>Follow Up
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h5>No Customer Data</h5>
                    <p class="text-muted">No customer ledger data available</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('customer-search').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const customerItems = document.querySelectorAll('.customer-item');
    
    customerItems.forEach(item => {
        const customerName = item.getAttribute('data-customer');
        if (customerName.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

function viewCustomerDetails(customerName) {
    alert('View details for: ' + customerName);
}

function generateStatement(customerName) {
    alert('Generate statement for: ' + customerName);
}

function followUpPayment(customerName) {
    alert('Follow up payment for: ' + customerName);
}

function refreshLedger() {
    location.reload();
}
</script>
{% endblock %}
