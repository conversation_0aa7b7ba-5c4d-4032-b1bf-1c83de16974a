// charts.js - JavaScript for generating charts in the reports

// Function to create a bar chart
function createBarChart(canvasId, labels, data, backgroundColor, borderColor, label) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    return new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: data,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Function to create a line chart
function createLineChart(canvasId, labels, data, backgroundColor, borderColor, label) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: data,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 2,
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Function to create a pie chart
function createPieChart(canvasId, labels, data, backgroundColor, borderColor) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    return new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true
        }
    });
}

// Function to create a doughnut chart
function createDoughnutChart(canvasId, labels, data, backgroundColor, borderColor) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            cutout: '50%'
        }
    });
}

// Function to create a nested pie chart (pie chart with doughnut)
function createNestedPieChart(outerCanvasId, innerCanvasId, outerLabels, innerLabels, outerData, innerData, outerColors, innerColors) {
    // Create the outer pie chart
    createPieChart(outerCanvasId, outerLabels, outerData, outerColors, 'white');
    
    // Create the inner doughnut chart
    createDoughnutChart(innerCanvasId, innerLabels, innerData, innerColors, 'white');
}

// Function to generate random colors
function generateColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
        const r = Math.floor(Math.random() * 255);
        const g = Math.floor(Math.random() * 255);
        const b = Math.floor(Math.random() * 255);
        colors.push(`rgba(${r}, ${g}, ${b}, 0.7)`);
    }
    return colors;
}

// Function to initialize all charts on a page
function initCharts() {
    // Check if we're on the product performance page
    if (document.getElementById('productSalesChart')) {
        initProductPerformanceCharts();
    }
    
    // Check if we're on the financial summary page
    if (document.getElementById('revenueChart')) {
        initFinancialSummaryCharts();
    }
    
    // Check if we're on the sales team performance page
    if (document.getElementById('teamPerformanceChart')) {
        initSalesTeamCharts();
    }
    
    // Check if we're on the customer history page
    if (document.getElementById('customerOrdersChart')) {
        initCustomerHistoryCharts();
    }
    
    // Check if we're on the delivery performance page
    if (document.getElementById('deliveryTimeChart')) {
        initDeliveryPerformanceCharts();
    }
}

// Initialize charts when the DOM is loaded
document.addEventListener('DOMContentLoaded', initCharts);
