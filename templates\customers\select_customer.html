{% extends 'base.html' %}

{% block title %}Select Customer{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Select Customer</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Customers
                            </a>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Select a Customer</h5>
                                </div>
                                <div class="card-body">
                                    <form method="get" action="{{ url_for('customers', view='orders') }}">
                                        <div class="form-group">
                                            <label for="customer_id">Customer</label>
                                            <select class="form-control" id="customer_id" name="customer_id" required>
                                                <option value="">-- Select Customer --</option>
                                                {% for customer in customers %}
                                                <option value="{{ customer.customer_id }}">{{ customer.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">View Order History</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
