/**
 * org_chart.js
 * This file implements the organizational structure chart for Medivent Pharmaceuticals
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing organizational structure chart...');

    // Check if we're on the sales team page and the org chart container exists
    if (document.getElementById('organizationalStructureChart')) {
        console.log('Found organizational structure chart element, initializing...');
        // Add a delay to ensure DOM is fully loaded
        setTimeout(function() {
            initializeOrgChart();

            // Make global variable available for other scripts
            window.organizationalData = {
                divisions: [
                ],
                specialTeams: []
            };
        }, 1000);
    }
});

function initializeOrgChart() {
    // Define the organizational structure from database
    const orgData = {
        divisions: [],
        independentTeams: []
    };

    // Render the organizational structure chart
    renderOrgChart(orgData);
}

function renderOrgChart(orgData) {
    const container = document.getElementById('organizationalStructureChart');
    if (!container) {
        console.error('Organizational structure chart container not found');
        return;
    }

    // Create the chart HTML
    let html = '<div class="org-chart">';

    // Add divisions
    html += '<div class="divisions-container">';
    orgData.divisions.forEach(division => {
        html += `
            <div class="division" data-division="${division.name}">
                <div class="division-header clickable" onclick="toggleDivisionCharts('${division.name}')">
                    ${division.name}
                    <span class="expand-icon">+</span>
                </div>
                <div class="division-head">
                    <div class="person head">
                        <div class="title">${division.head.title}</div>
                        <div class="name">${division.head.name}</div>
                    </div>
                </div>
                <div class="division-members">`;

        division.members.forEach(member => {
            html += `
                <div class="person member">
                    <div class="title">${member.title}</div>
                    <div class="name">${member.name}</div>
                </div>`;
        });

        html += `
                </div>
            </div>`;
    });
    html += '</div>';

    // Add independent teams
    if (orgData.independentTeams && orgData.independentTeams.length > 0) {
        html += '<div class="independent-teams-container">';
        orgData.independentTeams.forEach(team => {
            html += `
                <div class="independent-team">
                    <div class="team-head">
                        <div class="person head">
                            <div class="title">${team.head.title}</div>
                            <div class="name">${team.head.name}</div>
                        </div>
                    </div>
                    <div class="team-members">`;

            team.members.forEach(member => {
                html += `
                    <div class="person member">
                        <div class="title">${member.title}</div>
                        <div class="name">${member.name}</div>
                    </div>`;
            });

            html += `
                    </div>
                </div>`;
        });
        html += '</div>';
    }

    html += '</div>';

    // Set the HTML content
    container.innerHTML = html;

    // Create division-specific chart containers
    createDivisionChartContainers(orgData.divisions);
}

// Function to create division-specific chart containers
function createDivisionChartContainers(divisions) {
    const chartsContainer = document.getElementById('divisionChartsContainer');
    if (!chartsContainer) {
        console.error('Division charts container not found');
        return;
    }

    // Clear existing content
    chartsContainer.innerHTML = '';

    // Create a container for each division
    divisions.forEach(division => {
        const divisionContainer = document.createElement('div');
        divisionContainer.className = 'division-charts';
        divisionContainer.id = `division-charts-${division.name.replace(/\s+/g, '-')}`;
        divisionContainer.style.display = 'none'; // Hidden by default

        // Create the division charts HTML
        divisionContainer.innerHTML = `
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">${division.name} Division Sales Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container" style="height: 300px;">
                                <div class="chart-title">${division.name} Sales by Product</div>
                                <canvas id="division-product-sales-${division.name.replace(/\s+/g, '-')}"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container" style="height: 300px;">
                                <div class="chart-title">${division.name} Sales by Agent</div>
                                <canvas id="division-agent-sales-${division.name.replace(/\s+/g, '-')}"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="chart-container" style="height: 300px;">
                                <div class="chart-title">${division.name} Monthly Sales Trend</div>
                                <canvas id="division-monthly-sales-${division.name.replace(/\s+/g, '-')}"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        chartsContainer.appendChild(divisionContainer);
    });
}

// Function to toggle division charts visibility
function toggleDivisionCharts(divisionName) {
    const divisionChartsId = `division-charts-${divisionName.replace(/\s+/g, '-')}`;
    const divisionCharts = document.getElementById(divisionChartsId);

    if (!divisionCharts) {
        console.error(`Division charts container not found: ${divisionChartsId}`);
        return;
    }

    // Toggle visibility
    const isVisible = divisionCharts.style.display !== 'none';

    // Hide all division charts first
    const allDivisionCharts = document.querySelectorAll('.division-charts');
    allDivisionCharts.forEach(container => {
        container.style.display = 'none';
    });

    // Update all expand icons
    const allExpandIcons = document.querySelectorAll('.expand-icon');
    allExpandIcons.forEach(icon => {
        icon.textContent = '+';
    });

    // If it was hidden, show it
    if (!isVisible) {
        divisionCharts.style.display = 'block';

        // Update the expand icon
        const divisionElement = document.querySelector(`.division[data-division="${divisionName}"]`);
        if (divisionElement) {
            const expandIcon = divisionElement.querySelector('.expand-icon');
            if (expandIcon) {
                expandIcon.textContent = '-';
            }
        }

        // Load division-specific charts
        loadDivisionCharts(divisionName);

        // Scroll to the charts
        divisionCharts.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

// Function to load division-specific charts
function loadDivisionCharts(divisionName) {
    console.log(`Loading charts for division: ${divisionName}`);

    // Get date range
    const startDate = document.querySelector('input[name="start_date"]').value;
    const endDate = document.querySelector('input[name="end_date"]').value;

    // Fetch division-specific data
    fetch(`/api/data/division-sales?division=${encodeURIComponent(divisionName)}&start_date=${startDate}&end_date=${endDate}`)
        .then(response => {
            if (!response.ok) {
                // If API endpoint doesn't exist yet, use fallback data
                console.log('Division API endpoint not available, using fallback data');
                return { json: () => generateFallbackDivisionData(divisionName) };
            }
            return response.json();
        })
        .then(data => {
            console.log(`Received data for division ${divisionName}:`, data);
            renderDivisionCharts(divisionName, data);
        })
        .catch(error => {
            console.error(`Error fetching data for division ${divisionName}:`, error);
            // Show empty state instead of fallback data
            showDivisionEmptyState(divisionName);
        });
}

// Function to show division empty state
function showDivisionEmptyState(divisionName) {
    console.log(`Showing empty state for division: ${divisionName}`);

    // Show empty state message for division charts
    const chartContainer = document.querySelector(`[data-division="${divisionName}"] .division-charts`);
    if (chartContainer) {
        chartContainer.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="fas fa-chart-line fa-3x mb-3"></i><br>
                No data available for ${divisionName}
            </div>
        `;
    }
}

// Function to render division-specific charts
function renderDivisionCharts(divisionName, data) {
    const divisionId = divisionName.replace(/\s+/g, '-');

    // Render product sales chart
    renderDivisionProductSalesChart(divisionId, data.productSales);

    // Render agent sales chart
    renderDivisionAgentSalesChart(divisionId, data.agentSales);

    // Render monthly sales chart
    renderDivisionMonthlySalesChart(divisionId, data.monthlySales);
}

// Function to render division product sales chart
function renderDivisionProductSalesChart(divisionId, productData) {
    const canvasId = `division-product-sales-${divisionId}`;
    const canvas = document.getElementById(canvasId);

    if (!canvas) {
        console.error(`Canvas not found: ${canvasId}`);
        return;
    }

    // Clear any existing chart
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Sort products by sales
    const sortedProducts = [...productData].sort((a, b) => b.total_sales - a.total_sales);

    // Create the chart
    canvas.chart = new Chart(canvas.getContext('2d'), {
        type: 'bar',
        data: {
            labels: sortedProducts.map(p => p.product_name),
            datasets: [{
                label: 'Sales (PKR)',
                data: sortedProducts.map(p => p.total_sales),
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += new Intl.NumberFormat('en-PK', {
                                style: 'currency',
                                currency: 'PKR',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(context.parsed.y);
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Sales (PKR)'
                    }
                }
            }
        }
    });
}

// Function to render division agent sales chart
function renderDivisionAgentSalesChart(divisionId, agentData) {
    const canvasId = `division-agent-sales-${divisionId}`;
    const canvas = document.getElementById(canvasId);

    if (!canvas) {
        console.error(`Canvas not found: ${canvasId}`);
        return;
    }

    // Clear any existing chart
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Create the chart
    canvas.chart = new Chart(canvas.getContext('2d'), {
        type: 'pie',
        data: {
            labels: agentData.map(a => a.agent_name),
            datasets: [{
                data: agentData.map(a => a.total_sales),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderColor: 'white',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw;
                            const percentage = ((value / agentData.reduce((sum, agent) => sum + agent.total_sales, 0)) * 100).toFixed(1);
                            return `${label}: ${new Intl.NumberFormat('en-PK', {
                                style: 'currency',
                                currency: 'PKR',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(value)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Function to render division monthly sales chart
function renderDivisionMonthlySalesChart(divisionId, monthlyData) {
    const canvasId = `division-monthly-sales-${divisionId}`;
    const canvas = document.getElementById(canvasId);

    if (!canvas) {
        console.error(`Canvas not found: ${canvasId}`);
        return;
    }

    // Clear any existing chart
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Sort monthly data by date
    const sortedMonthlyData = [...monthlyData].sort((a, b) => a.month.localeCompare(b.month));

    // Create the chart
    canvas.chart = new Chart(canvas.getContext('2d'), {
        type: 'line',
        data: {
            labels: sortedMonthlyData.map(m => {
                const [year, month] = m.month.split('-');
                const date = new Date(year, month - 1);
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            }),
            datasets: [{
                label: 'Monthly Sales',
                data: sortedMonthlyData.map(m => m.total_sales),
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += new Intl.NumberFormat('en-PK', {
                                style: 'currency',
                                currency: 'PKR',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(context.parsed.y);
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Sales (PKR)'
                    }
                }
            }
        }
    });
}
