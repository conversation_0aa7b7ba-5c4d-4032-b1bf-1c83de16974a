{% extends "base.html" %}

{% block title %}Financial Reports - Finance{% endblock %}

{% block head %}
<style>
.report-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s;
    margin-bottom: 15px;
}
.report-card:hover {
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
    transform: translateY(-2px);
}
.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.chart-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">📊 Financial Reports & Analytics</h2>
                    <p class="mb-0">Comprehensive financial analysis and reporting</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="form-inline">
                        <label class="mr-2">Date Range:</label>
                        <select name="date_range" class="form-control mr-2">
                            <option value="today" {% if date_range == 'today' %}selected{% endif %}>Today</option>
                            <option value="this_week" {% if date_range == 'this_week' %}selected{% endif %}>This Week</option>
                            <option value="this_month" {% if date_range == 'this_month' %}selected{% endif %}>This Month</option>
                            <option value="last_month" {% if date_range == 'last_month' %}selected{% endif %}>Last Month</option>
                            <option value="custom" {% if date_range == 'custom' %}selected{% endif %}>Custom Range</option>
                        </select>
                        
                        <input type="date" name="start_date" class="form-control mr-2" 
                               value="{{ start_date }}" {% if date_range != 'custom' %}style="display:none"{% endif %}>
                        <input type="date" name="end_date" class="form-control mr-2" 
                               value="{{ end_date }}" {% if date_range != 'custom' %}style="display:none"{% endif %}>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Generate Report
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h4>{{ (sales_summary.total_invoices if sales_summary else summary.total_orders if summary else 0) }}</h4>
                    <p class="mb-0">Total Invoices</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h4>₹{{ "{:,.0f}".format(sales_summary.total_sales or 0) }}</h4>
                    <p class="mb-0">Total Sales</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h4>{{ payment_summary.total_payments or 0 }}</h4>
                    <p class="mb-0">Total Payments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <h4>₹{{ "{:,.0f}".format(payment_summary.total_collected or 0) }}</h4>
                    <p class="mb-0">Total Collected</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Grid -->
    <div class="row">
        <!-- Branch-wise Sales -->
        <div class="col-md-6 mb-4">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">🏢 Branch-wise Sales</h5>
                </div>
                <div class="card-body">
                    {% if branch_sales %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Branch</th>
                                        <th>Invoices</th>
                                        <th>Sales</th>
                                        <th>Outstanding</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for branch in branch_sales %}
                                    <tr>
                                        <td>
                                            <span class="badge {% if branch.branch_code == 'KHI' %}badge-success{% else %}badge-warning{% endif %}">
                                                {{ branch.branch_code }}
                                            </span>
                                        </td>
                                        <td>{{ branch.invoice_count }}</td>
                                        <td>₹{{ "{:,.0f}".format(branch.total_sales) }}</td>
                                        <td>₹{{ "{:,.0f}".format(branch.outstanding) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No branch data available</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-md-6 mb-4">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">👥 Top Customers</h5>
                </div>
                <div class="card-body">
                    {% if top_customers %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Invoices</th>
                                        <th>Sales</th>
                                        <th>Outstanding</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in top_customers %}
                                    <tr>
                                        <td>{{ customer.customer_name }}</td>
                                        <td>{{ customer.invoice_count }}</td>
                                        <td>₹{{ "{:,.0f}".format(customer.total_sales) }}</td>
                                        <td>₹{{ "{:,.0f}".format(customer.outstanding) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No customer data available</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="col-md-6 mb-4">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">💳 Payment Methods</h5>
                </div>
                <div class="card-body">
                    {% if payment_methods %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Method</th>
                                        <th>Count</th>
                                        <th>Amount</th>
                                        <th>%</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set total_amount = payment_methods|sum(attribute='total_amount') %}
                                    {% for method in payment_methods %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-info">{{ method.payment_method|title }}</span>
                                        </td>
                                        <td>{{ method.payment_count }}</td>
                                        <td>₹{{ "{:,.0f}".format(method.total_amount) }}</td>
                                        <td>
                                            {% if total_amount > 0 %}
                                                {{ "{:.1f}".format((method.total_amount / total_amount) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No payment data available</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="col-md-6 mb-4">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">📈 Financial Summary</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div class="row">
                            <div class="col-6">
                                <h6>Sales Performance</h6>
                                <p class="mb-1">Total Sales: <strong>₹{{ "{:,.0f}".format(sales_summary.total_sales or 0) }}</strong></p>
                                <p class="mb-1">Avg Invoice: <strong>₹{{ "{:,.0f}".format(sales_summary.avg_invoice_amount or 0) }}</strong></p>
                                <p class="mb-1">Outstanding: <strong>₹{{ "{:,.0f}".format(sales_summary.total_outstanding or 0) }}</strong></p>
                            </div>
                            <div class="col-6">
                                <h6>Collection Performance</h6>
                                <p class="mb-1">Total Collected: <strong>₹{{ "{:,.0f}".format(payment_summary.total_collected or 0) }}</strong></p>
                                <p class="mb-1">Avg Payment: <strong>₹{{ "{:,.0f}".format(payment_summary.avg_payment_amount or 0) }}</strong></p>
                                {% if sales_summary.total_sales and sales_summary.total_sales > 0 %}
                                <p class="mb-1">Collection %: <strong>{{ "{:.1f}".format(((payment_summary.total_collected or 0) / sales_summary.total_sales) * 100) }}%</strong></p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📤 Export Reports</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group">
                        <button type="button" class="btn btn-success" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> Export to Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf"></i> Export to PDF
                        </button>
                        <button type="button" class="btn btn-info" onclick="printReport()">
                            <i class="fas fa-print"></i> Print Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Date range handling
document.querySelector('select[name="date_range"]').addEventListener('change', function() {
    const customInputs = document.querySelectorAll('input[name="start_date"], input[name="end_date"]');
    if (this.value === 'custom') {
        customInputs.forEach(input => input.style.display = 'inline-block');
    } else {
        customInputs.forEach(input => input.style.display = 'none');
    }
});

// Export functions
function exportToExcel() {
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    alert('PDF export functionality will be implemented');
}

function printReport() {
    window.print();
}
</script>
{% endblock %}
