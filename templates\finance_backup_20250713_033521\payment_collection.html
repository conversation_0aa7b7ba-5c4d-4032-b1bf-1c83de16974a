{% extends "base.html" %}

{% block title %}Payment Collection - Finance{% endblock %}

{% block head %}
<style>
.invoice-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s;
    margin-bottom: 15px;
}
.invoice-card:hover {
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
    transform: translateY(-2px);
}
.overdue-card {
    border-left-color: #dc3545;
    background-color: #fff5f5;
}
.partial-card {
    border-left-color: #ffc107;
    background-color: #fffbf0;
}
.unpaid-card {
    border-left-color: #6c757d;
}
.payment-form {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.collection-stats {
    font-size: 0.9em;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">💰 Payment Collection Management</h2>
                    <p class="mb-0">Manage outstanding invoices and record payments</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Collection Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ collection_stats.total_invoices }}</h4>
                    <p class="mb-0">Outstanding Invoices</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4>{{ collection_stats.overdue_count }}</h4>
                    <p class="mb-0">Overdue Invoices</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h4>₹{{ "{:,.0f}".format(collection_stats.total_outstanding) }}</h4>
                    <p class="mb-0">Total Outstanding</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>₹{{ "{:,.0f}".format(collection_stats.overdue_amount) }}</h4>
                    <p class="mb-0">Overdue Amount</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Outstanding Invoices -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📋 Outstanding Invoices</h5>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    {% if unpaid_invoices %}
                        {% for invoice in unpaid_invoices %}
                        <div class="card invoice-card 
                            {% if invoice.payment_status == 'overdue' %}overdue-card
                            {% elif invoice.payment_status == 'partial' %}partial-card
                            {% else %}unpaid-card{% endif %}">
                            <div class="card-header">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6 class="mb-0">
                                            Invoice {{ invoice.invoice_number }}
                                            <span class="badge 
                                                {% if invoice.payment_status == 'overdue' %}badge-danger
                                                {% elif invoice.payment_status == 'partial' %}badge-warning
                                                {% else %}badge-secondary{% endif %}">
                                                {{ invoice.payment_status|title }}
                                            </span>
                                        </h6>
                                        <p class="mb-0 text-muted">
                                            Customer: {{ invoice.customer_name }} | Due: {{ invoice.due_date }}
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <h5 class="text-danger">₹{{ "{:,.2f}".format(invoice.outstanding_amount) }}</h5>
                                        <small class="text-muted">Outstanding</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="collection-stats">
                                            <div class="d-flex justify-content-between">
                                                <span>Total Amount:</span>
                                                <span>₹{{ "{:,.2f}".format(invoice.total_amount) }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>Outstanding:</span>
                                                <span class="text-danger">₹{{ "{:,.2f}".format(invoice.outstanding_amount) }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>Invoice Date:</span>
                                                <span>{{ invoice.invoice_date }}</span>
                                            </div>
                                            {% if invoice.credit_rating %}
                                            <div class="d-flex justify-content-between">
                                                <span>Credit Rating:</span>
                                                <span class="badge badge-info">{{ invoice.credit_rating }}</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-success btn-sm btn-block" 
                                                data-toggle="modal" data-target="#paymentModal{{ invoice.invoice_id }}">
                                            <i class="fas fa-money-bill-wave"></i> Record Payment
                                        </button>
                                        <a href="{{ url_for('finance_customer_ledger_details', customer_id=invoice.customer_id) }}" 
                                           class="btn btn-info btn-sm btn-block">
                                            <i class="fas fa-user"></i> Customer History
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Modal -->
                        <div class="modal fade" id="paymentModal{{ invoice.invoice_id }}" tabindex="-1" role="dialog">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Record Payment - {{ invoice.invoice_number }}</h5>
                                        <button type="button" class="close" data-dismiss="modal">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                    <form method="POST" action="{{ url_for('finance_record_payment') }}">
                                        <input type="hidden" name="invoice_id" value="{{ invoice.invoice_id }}">
                                        <input type="hidden" name="customer_id" value="{{ invoice.customer_id }}">
                                        <div class="modal-body">
                                            <div class="payment-form">
                                                <p><strong>Customer:</strong> {{ invoice.customer_name }}</p>
                                                <p><strong>Invoice:</strong> {{ invoice.invoice_number }}</p>
                                                <p><strong>Outstanding:</strong> ₹{{ "{:,.2f}".format(invoice.outstanding_amount) }}</p>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Payment Amount (₹) *</label>
                                                <input type="number" name="amount" class="form-control" 
                                                       max="{{ invoice.outstanding_amount }}" min="0.01" step="0.01" required>
                                                <small class="text-muted">Maximum: ₹{{ "{:,.2f}".format(invoice.outstanding_amount) }}</small>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Payment Method *</label>
                                                <select name="payment_method" class="form-control" required>
                                                    <option value="">Select method...</option>
                                                    <option value="cash">Cash</option>
                                                    <option value="cheque">Cheque</option>
                                                    <option value="bank_transfer">Bank Transfer</option>
                                                    <option value="online">Online Payment</option>
                                                    <option value="credit_card">Credit Card</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Reference Number</label>
                                                <input type="text" name="reference_number" class="form-control" 
                                                       placeholder="Cheque number, transaction ID, etc.">
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Payment Date</label>
                                                <input type="date" name="payment_date" class="form-control" 
                                                       value="{{ now.strftime('%Y-%m-%d') }}" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Notes</label>
                                                <textarea name="notes" class="form-control" rows="3" 
                                                          placeholder="Additional notes about the payment"></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-success">Record Payment</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h4>All Invoices Paid!</h4>
                            <p class="text-muted">No outstanding invoices found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Recent Payments -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">💳 Recent Payments</h5>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    {% if recent_payments %}
                        {% for payment in recent_payments %}
                        <div class="card mb-2 border-left-success">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1">{{ payment.invoice_number or 'N/A' }}</h6>
                                        <small class="text-muted">{{ payment.payment_date }}</small>
                                    </div>
                                    <div class="text-right">
                                        <strong class="text-success">₹{{ "{:,.2f}".format(payment.amount) }}</strong><br>
                                        <small class="badge badge-info">{{ payment.payment_method|title }}</small>
                                    </div>
                                </div>
                                {% if payment.reference_number %}
                                <small class="text-muted">Ref: {{ payment.reference_number }}</small>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No recent payments</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 60 seconds to check for new payments
setTimeout(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}
