{% extends "base.html" %}

{% block title %}Universal Search{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Search Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-search"></i> Universal Search
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- Main Search Form -->
                    <form method="GET" action="{{ url_for('universal_search') }}" id="search-form">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" name="q" class="form-control form-control-lg" 
                                           placeholder="Search customers, products, orders..." 
                                           value="{{ query }}" id="search-input" autocomplete="off">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                                <!-- Search Suggestions Dropdown -->
                                <div id="search-suggestions" class="dropdown-menu" style="width: 100%; display: none;">
                                    <!-- Suggestions will be populated here -->
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select name="type" class="form-control form-control-lg">
                                    <option value="all" {% if search_type == 'all' %}selected{% endif %}>All Results</option>
                                    <option value="customers" {% if search_type == 'customers' %}selected{% endif %}>Customers Only</option>
                                    <option value="products" {% if search_type == 'products' %}selected{% endif %}>Products Only</option>
                                    <option value="orders" {% if search_type == 'orders' %}selected{% endif %}>Orders Only</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Advanced Filters -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-secondary" data-toggle="collapse" data-target="#advanced-filters">
                                    <i class="fas fa-filter"></i> Advanced Filters
                                </button>
                            </div>
                        </div>
                        
                        <div class="collapse mt-3" id="advanced-filters">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Customer Filters -->
                                        <div class="col-md-3">
                                            <h6>Customer Filters</h6>
                                            <div class="form-group">
                                                <label for="customer_city">City:</label>
                                                <select name="customer_city" id="customer_city" class="form-control">
                                                    <option value="">All Cities</option>
                                                    {% for city in cities %}
                                                    <option value="{{ city.city }}" {% if filters.customer_city == city.city %}selected{% endif %}>
                                                        {{ city.city }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <!-- Product Filters -->
                                        <div class="col-md-3">
                                            <h6>Product Filters</h6>
                                            <div class="form-group">
                                                <label for="product_category">Category:</label>
                                                <select name="product_category" id="product_category" class="form-control">
                                                    <option value="">All Categories</option>
                                                    {% for category in categories %}
                                                    <option value="{{ category.category }}" {% if filters.product_category == category.category %}selected{% endif %}>
                                                        {{ category.category }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="product_type">Type:</label>
                                                <select name="product_type" id="product_type" class="form-control">
                                                    <option value="">All Types</option>
                                                    {% for type in product_types %}
                                                    <option value="{{ type.type }}" {% if filters.product_type == type.type %}selected{% endif %}>
                                                        {{ type.type }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <!-- Order Filters -->
                                        <div class="col-md-3">
                                            <h6>Order Filters</h6>
                                            <div class="form-group">
                                                <label for="order_status">Status:</label>
                                                <select name="order_status" id="order_status" class="form-control">
                                                    <option value="">All Statuses</option>
                                                    {% for status in order_statuses %}
                                                    <option value="{{ status }}" {% if filters.order_status == status %}selected{% endif %}>
                                                        {{ status }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <!-- Date Filters -->
                                        <div class="col-md-3">
                                            <h6>Date Range</h6>
                                            <div class="form-group">
                                                <label for="date_from">From:</label>
                                                <input type="date" name="date_from" id="date_from" class="form-control" value="{{ filters.date_from }}">
                                            </div>
                                            <div class="form-group">
                                                <label for="date_to">To:</label>
                                                <input type="date" name="date_to" id="date_to" class="form-control" value="{{ filters.date_to }}">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-filter"></i> Apply Filters
                                            </button>
                                            <a href="{{ url_for('universal_search') }}" class="btn btn-secondary ml-2">
                                                <i class="fas fa-times"></i> Clear All
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Search Results -->
            {% if query %}
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Search Results for "{{ query }}"
                        <span class="badge badge-light ml-2">{{ results.total_count }} results</span>
                    </h5>
                </div>
                
                <div class="card-body">
                    {% if results.total_count > 0 %}
                    
                    <!-- Customers Results -->
                    {% if results.customers %}
                    <div class="mb-4">
                        <h6><i class="fas fa-users"></i> Customers ({{ results.customers|length }})</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer ID</th>
                                        <th>Name</th>
                                        <th>Phone</th>
                                        <th>Email</th>
                                        <th>City</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in results.customers %}
                                    <tr>
                                        <td><strong>{{ customer.customer_id }}</strong></td>
                                        <td>{{ customer.name }}</td>
                                        <td>{{ customer.phone or 'N/A' }}</td>
                                        <td>{{ customer.email or 'N/A' }}</td>
                                        <td>{{ customer.city or 'N/A' }}</td>
                                        <td>
                                            <a href="{{ url_for('customer_statement', customer_id=customer.customer_id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Products Results -->
                    {% if results.products %}
                    <div class="mb-4">
                        <h6><i class="fas fa-pills"></i> Products ({{ results.products|length }})</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Product ID</th>
                                        <th>Name</th>
                                        <th>Generic Name</th>
                                        <th>Type</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in results.products %}
                                    <tr>
                                        <td><strong>{{ product.product_id }}</strong></td>
                                        <td>{{ product.name }}</td>
                                        <td>{{ product.generic_name or 'N/A' }}</td>
                                        <td>{{ product.type }}</td>
                                        <td>{{ product.category or 'N/A' }}</td>
                                        <td>₹{{ product.selling_price | format_currency }}</td>
                                        <td>
                                            <a href="{{ url_for('products') }}/{{ product.product_id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Orders Results -->
                    {% if results.orders %}
                    <div class="mb-4">
                        <h6><i class="fas fa-shopping-cart"></i> Orders ({{ results.orders|length }})</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in results.orders %}
                                    <tr>
                                        <td><strong>{{ order.order_id }}</strong></td>
                                        <td>{{ order.customer_name }}</td>
                                        <td>{{ order.order_date | format_datetime('%Y-%m-%d') }}</td>
                                        <td>₹{{ order.order_amount | format_currency }}</td>
                                        <td>
                                            <span class="badge badge-{% if order.status == 'Delivered' %}success{% elif order.status == 'Processing' %}primary{% elif order.status == 'Placed' %}warning{% else %}secondary{% endif %}">
                                                {{ order.status }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('view_order', order_id=order.order_id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <br>No results found for "{{ query }}"
                        <br><small>Try adjusting your search terms or filters</small>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center text-muted py-5">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <br>Enter a search term to find customers, products, or orders
                    <br><small>Use the advanced filters for more specific results</small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const suggestionsContainer = document.getElementById('search-suggestions');
    let currentSuggestionIndex = -1;
    let suggestions = [];

    // Search suggestions with debouncing
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            hideSuggestions();
            return;
        }

        searchTimeout = setTimeout(() => {
            fetchSuggestions(query);
        }, 300);
    });

    // Keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateSuggestions(1);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateSuggestions(-1);
        } else if (e.key === 'Enter') {
            if (currentSuggestionIndex >= 0 && suggestions[currentSuggestionIndex]) {
                e.preventDefault();
                selectSuggestion(suggestions[currentSuggestionIndex]);
            }
        } else if (e.key === 'Escape') {
            hideSuggestions();
        }
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
            hideSuggestions();
        }
    });

    function fetchSuggestions(query) {
        const searchType = document.querySelector('select[name="type"]').value;
        
        fetch(`/api/search-suggestions?q=${encodeURIComponent(query)}&type=${searchType}&limit=8`)
            .then(response => response.json())
            .then(data => {
                if (data.suggestions) {
                    showSuggestions(data.suggestions);
                }
            })
            .catch(error => {
                console.error('Error fetching suggestions:', error);
            });
    }

    function showSuggestions(suggestionList) {
        suggestions = suggestionList;
        currentSuggestionIndex = -1;
        
        if (suggestions.length === 0) {
            hideSuggestions();
            return;
        }

        let html = '';
        suggestions.forEach((suggestion, index) => {
            const icon = getTypeIcon(suggestion.type);
            html += `
                <a href="#" class="dropdown-item suggestion-item" data-index="${index}">
                    <i class="${icon}"></i> ${suggestion.text}
                    <small class="text-muted ml-2">${suggestion.type}</small>
                </a>
            `;
        });

        suggestionsContainer.innerHTML = html;
        suggestionsContainer.style.display = 'block';

        // Add click handlers
        document.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const index = parseInt(this.dataset.index);
                selectSuggestion(suggestions[index]);
            });
        });
    }

    function hideSuggestions() {
        suggestionsContainer.style.display = 'none';
        currentSuggestionIndex = -1;
    }

    function navigateSuggestions(direction) {
        if (suggestions.length === 0) return;

        // Remove current highlight
        if (currentSuggestionIndex >= 0) {
            const currentItem = document.querySelector(`[data-index="${currentSuggestionIndex}"]`);
            if (currentItem) currentItem.classList.remove('active');
        }

        // Update index
        currentSuggestionIndex += direction;
        if (currentSuggestionIndex < 0) currentSuggestionIndex = suggestions.length - 1;
        if (currentSuggestionIndex >= suggestions.length) currentSuggestionIndex = 0;

        // Highlight new item
        const newItem = document.querySelector(`[data-index="${currentSuggestionIndex}"]`);
        if (newItem) {
            newItem.classList.add('active');
            newItem.scrollIntoView({ block: 'nearest' });
        }
    }

    function selectSuggestion(suggestion) {
        searchInput.value = suggestion.text;
        hideSuggestions();
        
        // Optionally redirect to specific page based on type
        if (suggestion.type === 'customer') {
            window.location.href = `/finance/customer-statement/${suggestion.id}`;
        } else if (suggestion.type === 'product') {
            window.location.href = `/products/${suggestion.id}`;
        } else if (suggestion.type === 'order') {
            window.location.href = `/orders/${suggestion.id}/view`;
        } else {
            // Submit search form
            document.getElementById('search-form').submit();
        }
    }

    function getTypeIcon(type) {
        switch(type) {
            case 'customer': return 'fas fa-user';
            case 'product': return 'fas fa-pills';
            case 'order': return 'fas fa-shopping-cart';
            default: return 'fas fa-search';
        }
    }
});
</script>

<style>
#search-suggestions {
    position: absolute;
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.suggestion-item.active {
    background-color: #007bff;
    color: white;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item.active:hover {
    background-color: #0056b3;
}
</style>
{% endblock %}
