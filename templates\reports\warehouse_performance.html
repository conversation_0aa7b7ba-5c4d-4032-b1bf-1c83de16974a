{% extends 'base.html' %}

{% block title %}Warehouse Performance Report{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Warehouse Performance Report</h5>
                        <div>
                            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Reports
                            </a>
                            <a href="{{ url_for('export_warehouse_performance', start_date=start_date, end_date=end_date, format='excel') }}" class="btn btn-success btn-sm ml-2">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a>
                            <a href="{{ url_for('export_warehouse_performance', start_date=start_date, end_date=end_date, format='pdf') }}" class="btn btn-danger btn-sm ml-2">
                                <i class="fas fa-file-pdf"></i> Export to PDF
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form action="{{ url_for('warehouse_performance_report') }}" method="GET">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <label for="start_date">Start Date</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <label for="end_date">End Date</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-filter"></i> Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="row mb-4">
                        {% for warehouse in performance_data %}
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">{{ warehouse.warehouse_name }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="progress" style="height: 25px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ warehouse.efficiency_score }}%;" aria-valuenow="{{ warehouse.efficiency_score }}" aria-valuemin="0" aria-valuemax="100">{{ warehouse.efficiency_score }}%</div>
                                            </div>
                                            <small class="text-muted">Efficiency Score</small>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="progress" style="height: 25px;">
                                                <div class="progress-bar bg-info" role="progressbar" style="width: {{ warehouse.space_utilization }}%;" aria-valuenow="{{ warehouse.space_utilization }}" aria-valuemin="0" aria-valuemax="100">{{ warehouse.space_utilization }}%</div>
                                            </div>
                                            <small class="text-muted">Space Utilization</small>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <h4>{{ warehouse.total_items }}</h4>
                                            <small class="text-muted">Items</small>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h4>{{ warehouse.unique_products }}</h4>
                                            <small class="text-muted">Products</small>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h4>{{ warehouse.inbound }}</h4>
                                            <small class="text-muted">Inbound</small>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h4>{{ warehouse.outbound }}</h4>
                                            <small class="text-muted">Outbound</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Performance Comparison Table -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Warehouse Performance Comparison</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Warehouse</th>
                                            <th>Efficiency Score</th>
                                            <th>Space Utilization</th>
                                            <th>Total Items</th>
                                            <th>Total Quantity</th>
                                            <th>Unique Products</th>
                                            <th>Inbound</th>
                                            <th>Outbound</th>
                                            <th>Total Movements</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for warehouse in performance_data|sort(attribute='efficiency_score', reverse=True) %}
                                        <tr>
                                            <td>{{ warehouse.warehouse_name }}</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ warehouse.efficiency_score }}%;" aria-valuenow="{{ warehouse.efficiency_score }}" aria-valuemin="0" aria-valuemax="100">{{ warehouse.efficiency_score }}%</div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ warehouse.space_utilization }}%;" aria-valuenow="{{ warehouse.space_utilization }}" aria-valuemin="0" aria-valuemax="100">{{ warehouse.space_utilization }}%</div>
                                                </div>
                                            </td>
                                            <td>{{ warehouse.total_items }}</td>
                                            <td>{{ warehouse.total_quantity }}</td>
                                            <td>{{ warehouse.unique_products }}</td>
                                            <td>{{ warehouse.inbound }}</td>
                                            <td>{{ warehouse.outbound }}</td>
                                            <td>{{ warehouse.total_movements }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Date range validation
        $('#end_date').change(function() {
            var startDate = new Date($('#start_date').val());
            var endDate = new Date($(this).val());
            
            if (endDate < startDate) {
                alert('End date cannot be earlier than start date');
                $(this).val($('#start_date').val());
            }
        });
        
        $('#start_date').change(function() {
            var startDate = new Date($(this).val());
            var endDate = new Date($('#end_date').val());
            
            if (endDate < startDate) {
                $('#end_date').val($(this).val());
            }
        });
    });
</script>
{% endblock %}
