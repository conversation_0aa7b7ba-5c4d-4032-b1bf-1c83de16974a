#!/usr/bin/env python3
"""
Inventory Validation Utilities
Comprehensive validation system for inventory management and real-time deduction
"""

import sqlite3
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from .product_validator import ProductValidator


class InventoryValidator:
    """Comprehensive inventory validation and management utilities"""
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.product_validator = ProductValidator(db_connection)
        
    def validate_inventory_creation_data(self, inventory_data: Dict) -> Tuple[bool, List[str]]:
        """Validate all inventory creation data"""
        errors = []
        
        # Required fields validation
        required_fields = ['product_id', 'batch_number', 'stock_quantity']
        for field in required_fields:
            if not inventory_data.get(field):
                errors.append(f"{field.replace('_', ' ').title()} is required")
        
        # Product validation
        product_id = inventory_data.get('product_id')
        if product_id:
            is_valid, product_info = self.product_validator.validate_product_exists(product_id)
            if not is_valid:
                errors.append(f"Product ID '{product_id}' does not exist")
            elif not product_info.get('division_valid', False):
                errors.append(f"Product '{product_id}' does not have a valid division assignment")
        
        # Stock quantity validation
        stock_quantity = inventory_data.get('stock_quantity')
        if stock_quantity is not None:
            try:
                qty = int(stock_quantity)
                if qty < 0:
                    errors.append("Stock quantity cannot be negative")
            except (ValueError, TypeError):
                errors.append("Stock quantity must be a valid integer")
        
        # Allocated quantity validation
        allocated_quantity = inventory_data.get('allocated_quantity', 0)
        if allocated_quantity is not None:
            try:
                allocated = int(allocated_quantity)
                if allocated < 0:
                    errors.append("Allocated quantity cannot be negative")
                if stock_quantity and allocated > int(stock_quantity):
                    errors.append("Allocated quantity cannot exceed stock quantity")
            except (ValueError, TypeError):
                errors.append("Allocated quantity must be a valid integer")
        
        # Batch number uniqueness for the same product
        batch_number = inventory_data.get('batch_number', '').strip()
        if batch_number and product_id:
            if self.is_batch_duplicate(product_id, batch_number, inventory_data.get('inventory_id')):
                errors.append(f"Batch number '{batch_number}' already exists for this product")
        
        # Date validations
        manufacturing_date = inventory_data.get('manufacturing_date')
        expiry_date = inventory_data.get('expiry_date')
        
        if manufacturing_date and expiry_date:
            try:
                mfg_date = datetime.strptime(manufacturing_date, '%Y-%m-%d')
                exp_date = datetime.strptime(expiry_date, '%Y-%m-%d')
                
                if exp_date <= mfg_date:
                    errors.append("Expiry date must be after manufacturing date")
                    
                if exp_date <= datetime.now():
                    errors.append("Expiry date cannot be in the past")
                    
            except ValueError:
                errors.append("Invalid date format. Use YYYY-MM-DD")
        
        return len(errors) == 0, errors
    
    def is_batch_duplicate(self, product_id: str, batch_number: str, exclude_inventory_id: Optional[str] = None) -> bool:
        """Check if batch number already exists for the product"""
        try:
            if exclude_inventory_id:
                cursor = self.db.execute('''
                    SELECT COUNT(*) FROM inventory 
                    WHERE product_id = ? AND batch_number = ? AND inventory_id != ?
                ''', (product_id, batch_number, exclude_inventory_id))
            else:
                cursor = self.db.execute('''
                    SELECT COUNT(*) FROM inventory 
                    WHERE product_id = ? AND batch_number = ?
                ''', (product_id, batch_number))
            
            return cursor.fetchone()[0] > 0
            
        except Exception as e:
            print(f"Error checking batch duplicate: {e}")
            return False
    
    def get_available_stock(self, product_id: str) -> int:
        """Get total available stock for a product"""
        try:
            cursor = self.db.execute('''
                SELECT COALESCE(SUM(stock_quantity - allocated_quantity), 0)
                FROM inventory 
                WHERE product_id = ? AND status = 'active'
            ''', (product_id,))
            
            return int(cursor.fetchone()[0])
            
        except Exception as e:
            print(f"Error getting available stock for {product_id}: {e}")
            return 0
    
    def get_inventory_by_product(self, product_id: str) -> List[Dict]:
        """Get all inventory entries for a specific product"""
        try:
            cursor = self.db.execute('''
                SELECT i.inventory_id, i.batch_number, i.country_of_origin,
                       i.manufacturing_date, i.expiry_date, i.stock_quantity,
                       i.allocated_quantity, i.warehouse_id, i.location_code,
                       i.status, i.date_received, i.received_by,
                       w.name as warehouse_name,
                       p.name as product_name
                FROM inventory i
                LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                JOIN products p ON i.product_id = p.product_id
                WHERE i.product_id = ?
                ORDER BY i.expiry_date ASC
            ''', (product_id,))
            
            inventory = []
            for row in cursor.fetchall():
                inventory.append({
                    'inventory_id': row[0],
                    'batch_number': row[1],
                    'country_of_origin': row[2] or '',
                    'manufacturing_date': row[3],
                    'expiry_date': row[4],
                    'stock_quantity': row[5],
                    'allocated_quantity': row[6],
                    'available_quantity': row[5] - row[6],
                    'warehouse_id': row[7],
                    'location_code': row[8] or '',
                    'status': row[9],
                    'date_received': row[10],
                    'received_by': row[11] or '',
                    'warehouse_name': row[12] or 'Unknown',
                    'product_name': row[13]
                })
            
            return inventory
            
        except Exception as e:
            print(f"Error fetching inventory for product {product_id}: {e}")
            return []
    
    def validate_stock_deduction(self, product_id: str, quantity_needed: int, foc_quantity: int = 0) -> Tuple[bool, str, List[Dict]]:
        """Validate if stock deduction is possible and return allocation plan"""
        try:
            total_needed = quantity_needed + foc_quantity
            
            if total_needed <= 0:
                return False, "Quantity must be greater than 0", []
            
            # Get available inventory sorted by expiry date (FIFO)
            cursor = self.db.execute('''
                SELECT inventory_id, batch_number, stock_quantity, allocated_quantity,
                       expiry_date, warehouse_id
                FROM inventory 
                WHERE product_id = ? AND status = 'active' 
                  AND (stock_quantity - allocated_quantity) > 0
                ORDER BY expiry_date ASC
            ''', (product_id,))
            
            available_inventory = cursor.fetchall()
            
            if not available_inventory:
                return False, f"No inventory available for product {product_id}", []
            
            # Calculate allocation plan
            allocation_plan = []
            remaining_needed = total_needed
            
            for inv in available_inventory:
                if remaining_needed <= 0:
                    break
                
                inventory_id = inv[0]
                batch_number = inv[1]
                available_qty = inv[2] - inv[3]  # stock - allocated
                
                if available_qty > 0:
                    allocate_qty = min(remaining_needed, available_qty)
                    
                    allocation_plan.append({
                        'inventory_id': inventory_id,
                        'batch_number': batch_number,
                        'allocated_quantity': allocate_qty,
                        'warehouse_id': inv[5],
                        'expiry_date': inv[4]
                    })
                    
                    remaining_needed -= allocate_qty
            
            if remaining_needed > 0:
                available_total = sum(inv[2] - inv[3] for inv in available_inventory)
                return False, f"Insufficient stock. Need {total_needed}, available {available_total}", []
            
            return True, "Stock allocation successful", allocation_plan
            
        except Exception as e:
            print(f"Error validating stock deduction: {e}")
            return False, f"Error validating stock: {str(e)}", []
    
    def execute_stock_deduction(self, product_id: str, quantity: int, foc_quantity: int, order_id: str, user: str) -> Tuple[bool, str]:
        """Execute real-time stock deduction with transaction safety"""
        try:
            total_quantity = quantity + foc_quantity
            
            # Validate deduction first
            is_valid, message, allocation_plan = self.validate_stock_deduction(product_id, quantity, foc_quantity)
            
            if not is_valid:
                return False, message
            
            # Begin transaction
            self.db.execute('BEGIN TRANSACTION')
            
            # Execute allocations
            for allocation in allocation_plan:
                # Update inventory allocation
                self.db.execute('''
                    UPDATE inventory 
                    SET allocated_quantity = allocated_quantity + ?,
                        last_updated = ?,
                        updated_by = ?
                    WHERE inventory_id = ?
                ''', (
                    allocation['allocated_quantity'],
                    datetime.now(),
                    user,
                    allocation['inventory_id']
                ))
                
                # Log stock movement
                movement_id = f"MOV{datetime.now().strftime('%Y%m%d%H%M%S')}{allocation['inventory_id'][-4:]}"
                
                self.db.execute('''
                    INSERT INTO stock_movements (
                        movement_id, inventory_id, product_id, batch_number,
                        quantity, movement_type, movement_date, moved_by, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    movement_id,
                    allocation['inventory_id'],
                    product_id,
                    allocation['batch_number'],
                    -allocation['allocated_quantity'],  # Negative for allocation
                    'allocation',
                    datetime.now(),
                    user,
                    f"Allocated for order {order_id}"
                ))
            
            # Commit transaction
            self.db.execute('COMMIT')
            
            return True, f"Successfully allocated {total_quantity} units from {len(allocation_plan)} batches"
            
        except Exception as e:
            # Rollback on error
            try:
                self.db.execute('ROLLBACK')
            except:
                pass
            print(f"Error executing stock deduction: {e}")
            return False, f"Error executing stock deduction: {str(e)}"
    
    def get_low_stock_products(self, threshold_multiplier: float = 1.0) -> List[Dict]:
        """Get products with stock below minimum threshold"""
        try:
            cursor = self.db.execute('''
                SELECT p.product_id, p.name, p.min_stock_level,
                       COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock,
                       d.name as division_name
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                WHERE d.status = 'Active'
                GROUP BY p.product_id, p.name, p.min_stock_level, d.name
                HAVING available_stock < (p.min_stock_level * ?)
                ORDER BY available_stock ASC
            ''', (threshold_multiplier,))
            
            low_stock = []
            for row in cursor.fetchall():
                low_stock.append({
                    'product_id': row[0],
                    'product_name': row[1],
                    'min_stock_level': row[2] or 0,
                    'available_stock': int(row[3]),
                    'division_name': row[4],
                    'shortage': max(0, (row[2] or 0) - int(row[3]))
                })
            
            return low_stock
            
        except Exception as e:
            print(f"Error getting low stock products: {e}")
            return []
    
    def get_inventory_summary(self) -> Dict:
        """Get comprehensive inventory system summary"""
        try:
            # Count total inventory entries
            cursor = self.db.execute('SELECT COUNT(*) FROM inventory WHERE status = "active"')
            total_inventory_entries = cursor.fetchone()[0]
            
            # Count products with inventory
            cursor = self.db.execute('''
                SELECT COUNT(DISTINCT product_id) FROM inventory 
                WHERE status = 'active' AND stock_quantity > 0
            ''')
            products_with_inventory = cursor.fetchone()[0]
            
            # Count low stock products
            low_stock_count = len(self.get_low_stock_products())
            
            # Total stock value
            cursor = self.db.execute('''
                SELECT COALESCE(SUM(i.stock_quantity * p.unit_price), 0)
                FROM inventory i
                JOIN products p ON i.product_id = p.product_id
                WHERE i.status = 'active'
            ''')
            total_stock_value = cursor.fetchone()[0]
            
            return {
                'total_inventory_entries': total_inventory_entries,
                'products_with_inventory': products_with_inventory,
                'low_stock_products': low_stock_count,
                'total_stock_value': float(total_stock_value or 0),
                'system_health': 'Good' if low_stock_count < 10 else 'Needs Attention'
            }
            
        except Exception as e:
            print(f"Error generating inventory summary: {e}")
            return {
                'total_inventory_entries': 0,
                'products_with_inventory': 0,
                'low_stock_products': 0,
                'total_stock_value': 0.0,
                'system_health': 'Error'
            }


def get_inventory_validator(db_connection):
    """Factory function to create inventory validator instance"""
    return InventoryValidator(db_connection)


# Utility functions for Flask routes
def validate_inventory_for_route(db, product_id, quantity):
    """Quick validation function for Flask routes"""
    validator = InventoryValidator(db)
    return validator.validate_stock_deduction(product_id, quantity)


def get_products_with_inventory_for_forms(db):
    """Get products with available inventory for form dropdowns"""
    validator = InventoryValidator(db)
    
    try:
        cursor = db.execute('''
            SELECT DISTINCT p.product_id, p.name, p.strength,
                   COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock,
                   d.name as division_name
            FROM products p
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE d.status = 'Active'
            GROUP BY p.product_id, p.name, p.strength, d.name
            HAVING available_stock > 0
            ORDER BY d.name, p.name
        ''')
        
        products = []
        for row in cursor.fetchall():
            products.append({
                'id': row[0],
                'name': row[1],
                'strength': row[2] or '',
                'available_stock': int(row[3]),
                'division_name': row[4],
                'display_name': f"{row[1]} ({row[2] or 'No Strength'}) - {row[4]} [Stock: {int(row[3])}]"
            })
        
        return products
        
    except Exception as e:
        print(f"Error getting products with inventory: {e}")
        return []
