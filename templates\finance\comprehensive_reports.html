{% extends "base.html" %}

{% block title %}Comprehensive Finance Reports - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Enhanced Comprehensive Reports Styles - Matching Reference Image */
    .reports-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .reports-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .reports-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .reports-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
        display: block;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-export {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        margin-left: 10px;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
    }

    /* Summary Cards */
    .summary-cards {
        margin-bottom: 25px;
    }

    .summary-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 120px;
        display: flex;
        align-items: center;
    }

    .summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .summary-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 20px;
    }

    .summary-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .summary-content p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .chart-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .chart-title i {
        margin-right: 10px;
        color: #3498db;
    }


    /* Responsive Design */
    @media (max-width: 768px) {
        .reports-title {
            font-size: 1.5rem;
        }

        .summary-card {
            height: auto;
            padding: 15px;
        }

        .summary-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }

        .summary-content h3 {
            font-size: 1.4rem;
        }

        .chart-container {
            height: 250px;
        }
    }
</style>

<div class="reports-dashboard">
    <div class="dashboard-container">
        <!-- Header -->
        <div class="reports-header">
            <h1 class="reports-title">
                <i class="fas fa-chart-line mr-3"></i>Comprehensive Finance Reports
            </h1>
            <p class="reports-subtitle">Generate detailed financial reports and analytics</p>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>Report Filters
            </div>
            <form id="reportFilters">
                <div class="row">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Report Type</label>
                            <select class="form-select" id="reportType">
                                <option value="all">All Reports</option>
                                <option value="revenue">Revenue Analysis</option>
                                <option value="payments">Payment Analysis</option>
                                <option value="customers">Customer Analysis</option>
                                <option value="aging">Aging Analysis</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Customer</label>
                            <select class="form-select" id="customerFilter">
                                <option value="all">All Customers</option>
                                <!-- Customer options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-filter" onclick="generateReport()">
                                    <i class="fas fa-search"></i> Generate Report
                                </button>
                                <button type="button" class="btn btn-export" onclick="exportReport('pdf')">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button type="button" class="btn btn-export" onclick="exportReport('excel')">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Date Range (Hidden by default) -->
                <div class="row" id="customDateRange" style="display: none;">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">End Date</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        <div class="row summary-cards">
            <div class="col-lg-3 col-md-6">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalRevenue">₹0</h3>
                        <p>Total Revenue</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalInvoices">0</h3>
                        <p>Total Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, #f39c12, #f1c40f);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="pendingAmount">₹0</h3>
                        <p>Pending Amount</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="summary-card">
                    <div class="summary-icon" style="background: linear-gradient(135deg, #e67e22, #f39c12);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalCustomers">0</h3>
                        <p>Active Customers</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-line"></i>Revenue Trend
                    </div>
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie"></i>Payment Status Distribution
                    </div>
                    <div class="chart-container">
                        <canvas id="paymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-bar"></i>Top Customers by Revenue
                    </div>
                    <div class="chart-container">
                        <canvas id="customerChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-area"></i>Monthly Comparison
                    </div>
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
// Initialize charts and functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date range selector
    document.getElementById('dateRange').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.style.display = 'block';
        } else {
            customRange.style.display = 'none';
        }
    });

    // Load initial data
    loadReportData();
    initializeCharts();
});

function loadReportData() {
    // Load data from database
    document.getElementById('totalRevenue').textContent = '₹0';
    document.getElementById('totalInvoices').textContent = '0';
    document.getElementById('pendingAmount').textContent = '₹0';
    document.getElementById('totalCustomers').textContent = '0';
}

function initializeCharts() {
    // Revenue Trend Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Revenue',
                data: [120000, 190000, 300000, 500000, 200000, 300000],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Payment Status Chart
    const paymentCtx = document.getElementById('paymentChart').getContext('2d');
    new Chart(paymentCtx, {
        type: 'doughnut',
        data: {
            labels: ['Paid', 'Pending', 'Overdue'],
            datasets: [{
                data: [60, 30, 10],
                backgroundColor: ['#27ae60', '#f39c12', '#e74c3c']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Customer Chart
    const customerCtx = document.getElementById('customerChart').getContext('2d');
    new Chart(customerCtx, {
        type: 'bar',
        data: {
            labels: ['Customer A', 'Customer B', 'Customer C', 'Customer D', 'Customer E'],
            datasets: [{
                label: 'Revenue',
                data: [300000, 250000, 200000, 150000, 100000],
                backgroundColor: '#3498db'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Monthly Comparison Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'area',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'This Year',
                data: [120000, 190000, 300000, 500000, 200000, 300000],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                fill: true
            }, {
                label: 'Last Year',
                data: [100000, 150000, 250000, 400000, 180000, 280000],
                borderColor: '#95a5a6',
                backgroundColor: 'rgba(149, 165, 166, 0.1)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function generateReport() {
    const dateRange = document.getElementById('dateRange').value;
    const reportType = document.getElementById('reportType').value;
    const customer = document.getElementById('customerFilter').value;

    console.log('Generating report with filters:', {
        dateRange,
        reportType,
        customer
    });

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // Simulate report generation
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Report generated successfully!');
    }, 2000);
}

function exportReport(format) {
    console.log('Exporting report as:', format);

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    // Simulate export
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert(`Report exported as ${format.toUpperCase()} successfully!`);
    }, 1500);
}
</script>

        <!-- Report Categories -->
        {% set categories = {} %}
        {% for report in report_types %}
            {% if report.category not in categories %}
                {% set _ = categories.update({report.category: []}) %}
            {% endif %}
            {% set _ = categories[report.category].append(report) %}
        {% endfor %}

        {% for category, reports in categories.items() %}
        <div class="report-category">
            <h3 class="category-title">
                <i class="fas fa-folder-open me-2"></i>{{ category }}
            </h3>
            <div class="row">
                {% for report in reports %}
                <div class="col-md-6 col-lg-4">
                    <div class="report-card" onclick="selectReport('{{ report.id }}')">
                        <div class="report-icon">
                            <i class="{{ report.icon }}"></i>
                        </div>
                        <div class="report-name">{{ report.name }}</div>
                        <div class="report-description">{{ report.description }}</div>
                        <div class="report-actions">
                            <button class="btn btn-pdf btn-report" onclick="generateReport('{{ report.id }}', 'pdf'); event.stopPropagation();">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                            <button class="btn btn-excel btn-report" onclick="generateReport('{{ report.id }}', 'excel'); event.stopPropagation();">
                                <i class="fas fa-file-excel me-1"></i>Excel
                            </button>
                            <button class="btn btn-view btn-report" onclick="viewReport('{{ report.id }}'); event.stopPropagation();">
                                <i class="fas fa-eye me-1"></i>View
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h5>Generating Report...</h5>
        <p>Please wait while we prepare your financial report.</p>
    </div>
</div>

<script>
let selectedReport = null;

function selectReport(reportId) {
    selectedReport = reportId;
    // Visual feedback for selection
    document.querySelectorAll('.report-card').forEach(card => {
        card.style.background = '#f8f9fa';
        card.style.borderColor = '#e9ecef';
    });
    event.currentTarget.style.background = '#e3f2fd';
    event.currentTarget.style.borderColor = 'var(--primary)';
}

function generateReport(reportId, format) {
    showLoading();

    const filters = {
        date_from: document.getElementById('global_date_from').value,
        date_to: document.getElementById('global_date_to').value,
        division: document.getElementById('global_division').value,
        format: format
    };

    fetch('/finance/api/generate-comprehensive-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            report_type: reportId,
            filters: filters
        })
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('Report generation failed');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const extension = format === 'excel' ? 'xlsx' : 'pdf';
        const timestamp = new Date().toISOString().split('T')[0];
        a.download = `${reportId}_report_${timestamp}.${extension}`;

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        hideLoading();
        showSuccess(`${format.toUpperCase()} report generated successfully!`);
    })
    .catch(error => {
        console.error('Error:', error);
        hideLoading();
        showError('Error generating report. Please try again.');
    });
}

function viewReport(reportId) {
    const filters = {
        date_from: document.getElementById('global_date_from').value,
        date_to: document.getElementById('global_date_to').value,
        division: document.getElementById('global_division').value
    };

    const params = new URLSearchParams({
        report_type: reportId,
        ...filters
    });

    window.open(`/finance/view-report?${params.toString()}`, '_blank');
}

function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function showSuccess(message) {
    // Create success toast
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

function showError(message) {
    // Create error toast
    const toast = document.createElement('div');
    toast.className = 'alert alert-danger position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Bulk report generation
function generateAllReports() {
    if (confirm('This will generate all 16 reports. This may take several minutes. Continue?')) {
        showLoading();

        const reportIds = [
            'profit_loss', 'cash_flow', 'balance_sheet', 'aging_analysis',
            'customer_performance', 'product_profitability', 'sales_trends',
            'payment_analysis', 'outstanding_receivables', 'agent_performance',
            'division_analysis', 'monthly_summary', 'quarterly_review',
            'tax_summary', 'inventory_valuation', 'credit_analysis'
        ];

        let completed = 0;
        const total = reportIds.length;

        reportIds.forEach((reportId, index) => {
            setTimeout(() => {
                generateReport(reportId, 'pdf');
                completed++;

                if (completed === total) {
                    hideLoading();
                    showSuccess('All reports generated successfully!');
                }
            }, index * 2000); // Stagger requests by 2 seconds
        });
    }
}
</script>

{% endblock %}
