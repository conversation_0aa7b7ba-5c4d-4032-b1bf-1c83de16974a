{% extends "base.html" %}

{% block title %}Accounts Receivable - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .receivables-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .receivables-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .receivables-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .aging-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid;
    }

    .aging-current { border-left-color: #28a745; }
    .aging-30 { border-left-color: #ffc107; }
    .aging-60 { border-left-color: #fd7e14; }
    .aging-90 { border-left-color: #dc3545; }
    .aging-90plus { border-left-color: #6f42c1; }

    .receivables-table {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .table-modern {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background: #f8f9fa;
        transform: scale(1.01);
    }

    .table-modern tbody td {
        padding: 15px;
        border: none;
        border-bottom: 1px solid #e9ecef;
    }

    .risk-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .risk-low { background: #d4edda; color: #155724; }
    .risk-medium { background: #fff3cd; color: #856404; }
    .risk-high { background: #f8d7da; color: #721c24; }

    .aging-badge {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .aging-current { background: #d4edda; color: #155724; }
    .aging-1-30 { background: #fff3cd; color: #856404; }
    .aging-31-60 { background: #ffeaa7; color: #d63031; }
    .aging-61-90 { background: #fab1a0; color: #e17055; }
    .aging-90-plus { background: #fd79a8; color: #e84393; }

    .action-btn {
        padding: 5px 12px;
        border-radius: 8px;
        font-size: 0.8rem;
        margin: 2px;
        transition: all 0.3s ease;
    }

    .btn-collect {
        background: #28a745;
        color: white;
        border: none;
    }

    .btn-collect:hover {
        background: #218838;
        color: white;
    }

    .btn-view {
        background: #007bff;
        color: white;
        border: none;
    }

    .btn-view:hover {
        background: #0056b3;
        color: white;
    }

    .filters-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>

<div class="receivables-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="receivables-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="receivables-title">
                        <i class="fas fa-file-invoice-dollar me-3"></i>Accounts Receivable
                    </h1>
                    <p class="text-white-50 mb-0">Comprehensive receivables management with aging analysis</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="exportReceivables()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <a href="/finance" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left me-2"></i>Back to Finance
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-primary">₹{{ "{:,.0f}".format(total_outstanding) }}</div>
                    <div class="stat-label">Total Outstanding</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-info">{{ total_invoices }}</div>
                    <div class="stat-label">Outstanding Invoices</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-warning">₹{{ "{:,.0f}".format(aging_summary.get('90_plus', {}).get('amount', 0)) }}</div>
                    <div class="stat-label">90+ Days Overdue</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-success">{{ "{:.1f}".format((aging_summary.get('current', {}).get('amount', 0) / total_outstanding * 100) if total_outstanding > 0 else 0) }}%</div>
                    <div class="stat-label">Current Ratio</div>
                </div>
            </div>
        </div>

        <!-- Aging Analysis -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>Aging Analysis
                    </h5>
                    <div style="position: relative; height: 300px;">
                        <canvas id="agingChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="aging-card aging-current">
                    <h6>Current</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary.current.count }} invoices</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary.current.amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-30">
                    <h6>1-30 Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['1_30'].count }} invoices</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['1_30'].amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-60">
                    <h6>31-60 Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['31_60'].count }} invoices</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['31_60'].amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-90">
                    <h6>61-90 Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['61_90'].count }} invoices</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['61_90'].amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-90plus">
                    <h6>90+ Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['90_plus'].count }} invoices</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['90_plus'].amount) }}</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Filters & Search
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" id="customerFilter" placeholder="Search customer...">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Aging Bucket</label>
                    <select class="form-control" id="agingFilter">
                        <option value="">All Ages</option>
                        <option value="Current">Current</option>
                        <option value="1-30 days">1-30 Days</option>
                        <option value="31-60 days">31-60 Days</option>
                        <option value="61-90 days">61-90 Days</option>
                        <option value="90+ days">90+ Days</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Risk Level</label>
                    <select class="form-control" id="riskFilter">
                        <option value="">All Risk Levels</option>
                        <option value="Low">Low Risk</option>
                        <option value="Medium">Medium Risk</option>
                        <option value="High">High Risk</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Amount Range</label>
                    <select class="form-control" id="amountFilter">
                        <option value="">All Amounts</option>
                        <option value="0-10000">₹0 - ₹10,000</option>
                        <option value="10000-50000">₹10,000 - ₹50,000</option>
                        <option value="50000-100000">₹50,000 - ₹1,00,000</option>
                        <option value="100000+">₹1,00,000+</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Receivables Table -->
        <div class="receivables-table">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>Outstanding Receivables
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-success btn-sm" onclick="bulkCollectionReminder()">
                        <i class="fas fa-envelope me-1"></i>Send Reminders
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="generateAgingReport()">
                        <i class="fas fa-file-pdf me-1"></i>Aging Report
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-modern" id="receivablesTable">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>Invoice #</th>
                            <th>Customer</th>
                            <th>Invoice Date</th>
                            <th>Due Date</th>
                            <th>Original Amount</th>
                            <th>Paid Amount</th>
                            <th>Outstanding</th>
                            <th>Days Overdue</th>
                            <th>Aging</th>
                            <th>Risk</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for receivable in receivables %}
                        <tr data-customer="{{ receivable.customer_name }}" 
                            data-aging="{{ receivable.aging_bucket }}" 
                            data-risk="{{ receivable.risk_level }}"
                            data-amount="{{ receivable.outstanding_amount }}">
                            <td><input type="checkbox" class="receivable-checkbox" value="{{ receivable.invoice_id }}"></td>
                            <td><strong>{{ receivable.invoice_number or receivable.invoice_id }}</strong></td>
                            <td>{{ receivable.customer_name }}</td>
                            <td>{{ receivable.invoice_date }}</td>
                            <td>{{ receivable.due_date or 'N/A' }}</td>
                            <td>₹{{ "{:,.2f}".format(receivable.total_amount) }}</td>
                            <td>₹{{ "{:,.2f}".format(receivable.paid_amount) }}</td>
                            <td><strong>₹{{ "{:,.2f}".format(receivable.outstanding_amount) }}</strong></td>
                            <td>
                                {% if receivable.days_overdue > 0 %}
                                    <span class="text-danger">{{ "{:.0f}".format(receivable.days_overdue) }} days</span>
                                {% else %}
                                    <span class="text-success">Current</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="aging-badge aging-{{ receivable.aging_bucket.replace(' ', '-').replace('+', '-plus').lower() }}">
                                    {{ receivable.aging_bucket }}
                                </span>
                            </td>
                            <td>
                                <span class="risk-badge risk-{{ receivable.risk_level.lower() }}">
                                    {{ receivable.risk_level }}
                                </span>
                            </td>
                            <td>
                                <button class="btn action-btn btn-collect" onclick="collectPayment('{{ receivable.invoice_id }}')">
                                    <i class="fas fa-money-bill-wave"></i> Collect
                                </button>
                                <button class="btn action-btn btn-view" onclick="viewInvoice('{{ receivable.invoice_id }}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize aging chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('agingChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Current', '1-30 Days', '31-60 Days', '61-90 Days', '90+ Days'],
            datasets: [{
                label: 'Outstanding Amount (₹)',
                data: [
                    {{ aging_summary.current.amount }},
                    {{ aging_summary['1_30'].amount }},
                    {{ aging_summary['31_60'].amount }},
                    {{ aging_summary['61_90'].amount }},
                    {{ aging_summary['90_plus'].amount }}
                ],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(255, 126, 20, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(111, 66, 193, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(255, 126, 20, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(111, 66, 193, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });

    // Initialize filters
    initializeFilters();
});

function initializeFilters() {
    const customerFilter = document.getElementById('customerFilter');
    const agingFilter = document.getElementById('agingFilter');
    const riskFilter = document.getElementById('riskFilter');
    const amountFilter = document.getElementById('amountFilter');

    [customerFilter, agingFilter, riskFilter, amountFilter].forEach(filter => {
        filter.addEventListener('change', applyFilters);
        filter.addEventListener('keyup', applyFilters);
    });
}

function applyFilters() {
    const customerFilter = document.getElementById('customerFilter').value.toLowerCase();
    const agingFilter = document.getElementById('agingFilter').value;
    const riskFilter = document.getElementById('riskFilter').value;
    const amountFilter = document.getElementById('amountFilter').value;

    const rows = document.querySelectorAll('#receivablesTable tbody tr');

    rows.forEach(row => {
        const customer = row.dataset.customer.toLowerCase();
        const aging = row.dataset.aging;
        const risk = row.dataset.risk;
        const amount = parseFloat(row.dataset.amount);

        let show = true;

        // Customer filter
        if (customerFilter && !customer.includes(customerFilter)) {
            show = false;
        }

        // Aging filter
        if (agingFilter && aging !== agingFilter) {
            show = false;
        }

        // Risk filter
        if (riskFilter && risk !== riskFilter) {
            show = false;
        }

        // Amount filter
        if (amountFilter) {
            const [min, max] = amountFilter.split('-').map(v => v.replace('+', ''));
            const minAmount = parseFloat(min) || 0;
            const maxAmount = max ? parseFloat(max) : Infinity;

            if (amount < minAmount || amount > maxAmount) {
                show = false;
            }
        }

        row.style.display = show ? '' : 'none';
    });
}

function collectPayment(invoiceId) {
    // Open payment collection modal/page
    window.open(`/finance/payment-collection?invoice_id=${invoiceId}`, '_blank');
}

function viewInvoice(invoiceId) {
    // Open invoice details
    window.open(`/finance/invoice/${invoiceId}`, '_blank');
}

function bulkCollectionReminder() {
    const selectedInvoices = Array.from(document.querySelectorAll('.receivable-checkbox:checked'))
        .map(cb => cb.value);

    if (selectedInvoices.length === 0) {
        alert('Please select invoices to send reminders for.');
        return;
    }

    if (confirm(`Send collection reminders for ${selectedInvoices.length} selected invoices?`)) {
        // Implement bulk reminder functionality
        fetch('/finance/api/send-collection-reminders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                invoice_ids: selectedInvoices
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Collection reminders sent for ${data.sent_count} invoices.`);
            } else {
                alert('Error sending reminders: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending reminders.');
        });
    }
}

function generateAgingReport() {
    // Generate and download aging report
    window.open('/finance/reports/aging-analysis?format=pdf', '_blank');
}

function exportReceivables() {
    // Export receivables data
    window.open('/finance/export/receivables?format=excel', '_blank');
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.receivable-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});
</script>

{% endblock %}
