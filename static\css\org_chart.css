/**
 * org_chart.css
 * Styles for the organizational structure chart
 */

.org-chart {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px 0;
    overflow-x: auto;
}

.divisions-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.division {
    width: 280px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    overflow: hidden;
    background-color: #fff;
    transition: transform 0.3s ease;
}

.division:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.division-header {
    background-color: #007bff;
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    position: relative;
}

.division-header.clickable {
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.division-header.clickable:hover {
    background-color: #0069d9;
}

.expand-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: bold;
    font-size: 18px;
}

.division-head {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.division-members {
    padding: 10px;
}

.independent-teams-container {
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.independent-team {
    width: 280px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    overflow: hidden;
    background-color: #fff;
}

.team-head {
    background-color: #28a745;
    color: white;
    padding: 15px;
}

.team-members {
    padding: 10px;
}

.person {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
}

.person.head {
    background-color: rgba(0,123,255,0.1);
}

.person.member {
    background-color: rgba(0,123,255,0.05);
    margin-bottom: 5px;
}

.person .title {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
}

.person .name {
    font-weight: bold;
    color: #343a40;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .divisions-container, .independent-teams-container {
        flex-direction: column;
        align-items: center;
    }

    .division, .independent-team {
        width: 100%;
        max-width: 320px;
    }
}

/* Division charts styles */
.division-charts {
    margin-bottom: 30px;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Chart container styles */
.chart-container {
    position: relative;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    padding: 15px;
    min-height: 350px;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

/* Plotly chart styles */
.js-plotly-plot {
    width: 100% !important;
}

.js-plotly-plot .plotly {
    width: 100% !important;
}

/* Ensure all charts are visible */
#teamPerformanceChart,
#agentSalesChart,
#salesByAgentChart,
#topAgentDivisionsChart,
#agentDivisionHierarchyChart,
#organizationalStructureChart {
    width: 100% !important;
    min-height: 300px;
}

/* Print styles */
@media print {
    .org-chart {
        page-break-inside: avoid;
    }

    .divisions-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .division, .independent-team {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    .division-charts {
        page-break-inside: avoid;
    }

    .expand-icon, .alert {
        display: none;
    }
}
