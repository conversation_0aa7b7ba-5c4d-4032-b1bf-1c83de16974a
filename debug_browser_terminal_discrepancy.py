"""
Debug the browser-terminal discrepancy for TCS tracking
"""

import requests
import json
import time
from tcs_scraper_demo import track_tcs_demo

def test_terminal_vs_api():
    """Compare terminal scraper results with API results"""
    
    # Test tracking numbers that reportedly work in terminal
    test_numbers = [
        '31442083522',
        '31442083523', 
        '31442083525',
        '31442083524',
        '31442083393'
    ]
    
    print("🔍 DEBUGGING BROWSER-TERMINAL DISCREPANCY")
    print("=" * 60)
    
    for i, tracking_number in enumerate(test_numbers, 1):
        print(f"\n📦 Test {i}/{len(test_numbers)}: {tracking_number}")
        print("-" * 40)
        
        # Test 1: Direct terminal scraper
        print("🖥️  TERMINAL TEST:")
        try:
            start_time = time.time()
            terminal_result = track_tcs_demo(tracking_number, headless=True)
            terminal_duration = time.time() - start_time
            
            if terminal_result.get('success'):
                print(f"   ✅ SUCCESS ({terminal_duration:.1f}s)")
                print(f"   Status: {terminal_result.get('current_status')}")
                print(f"   Route: {terminal_result.get('origin')} → {terminal_result.get('destination')}")
                print(f"   History: {len(terminal_result.get('track_history', []))} entries")
            else:
                print(f"   ❌ FAILED ({terminal_duration:.1f}s)")
                print(f"   Error: {terminal_result.get('error')}")
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
            terminal_result = None
        
        # Test 2: API endpoint
        print("\n🌐 API TEST:")
        try:
            start_time = time.time()
            api_response = requests.post(
                'http://localhost:5000/api/track-tcs-public',
                json={'tracking_number': tracking_number},
                timeout=30
            )
            api_duration = time.time() - start_time
            
            print(f"   HTTP Status: {api_response.status_code}")
            
            if api_response.status_code == 200:
                api_result = api_response.json()
                
                if api_result.get('success'):
                    api_data = api_result.get('data', {})
                    print(f"   ✅ SUCCESS ({api_duration:.1f}s)")
                    print(f"   Status: {api_data.get('current_status')}")
                    print(f"   Route: {api_data.get('origin')} → {api_data.get('destination')}")
                    print(f"   History: {len(api_data.get('track_history', []))} entries")
                else:
                    print(f"   ❌ FAILED ({api_duration:.1f}s)")
                    print(f"   Error: {api_result.get('error')}")
            else:
                print(f"   ❌ HTTP ERROR ({api_duration:.1f}s)")
                print(f"   Response: {api_response.text[:200]}")
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
        
        # Test 3: Browser simulation (CORS check)
        print("\n🌍 BROWSER SIMULATION:")
        try:
            # Simulate browser request with CORS headers
            headers = {
                'Content-Type': 'application/json',
                'Origin': 'file://',  # Simulate file:// origin
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            start_time = time.time()
            browser_response = requests.post(
                'http://localhost:5000/api/track-tcs-public',
                json={'tracking_number': tracking_number},
                headers=headers,
                timeout=30
            )
            browser_duration = time.time() - start_time
            
            print(f"   HTTP Status: {browser_response.status_code}")
            print(f"   CORS Headers: {browser_response.headers.get('Access-Control-Allow-Origin', 'Not Set')}")
            
            if browser_response.status_code == 200:
                browser_result = browser_response.json()
                
                if browser_result.get('success'):
                    browser_data = browser_result.get('data', {})
                    print(f"   ✅ SUCCESS ({browser_duration:.1f}s)")
                    print(f"   Status: {browser_data.get('current_status')}")
                else:
                    print(f"   ❌ FAILED ({browser_duration:.1f}s)")
                    print(f"   Error: {browser_result.get('error')}")
            else:
                print(f"   ❌ HTTP ERROR ({browser_duration:.1f}s)")
                
        except Exception as e:
            print(f"   💥 EXCEPTION: {e}")
        
        print()
        time.sleep(1)  # Brief pause between tests

def test_cors_configuration():
    """Test CORS configuration"""
    
    print("\n🔒 CORS CONFIGURATION TEST")
    print("=" * 40)
    
    try:
        # Test OPTIONS request (preflight)
        options_response = requests.options('http://localhost:5000/api/track-tcs-public')
        print(f"OPTIONS Status: {options_response.status_code}")
        print(f"CORS Headers:")
        for header, value in options_response.headers.items():
            if 'access-control' in header.lower():
                print(f"   {header}: {value}")
        
        if not any('access-control' in h.lower() for h in options_response.headers):
            print("   ❌ No CORS headers found!")
            return False
        else:
            print("   ✅ CORS headers present")
            return True
            
    except Exception as e:
        print(f"   💥 CORS test failed: {e}")
        return False

def create_cors_enabled_server():
    """Create a CORS-enabled test server"""
    
    server_code = '''
from flask import Flask, request, jsonify
from flask_cors import CORS

# Create Flask app with CORS enabled
app = Flask(__name__)
CORS(app, origins="*")  # Allow all origins for testing

@app.route('/api/track-tcs-public', methods=['POST', 'OPTIONS'])
def track_tcs():
    """CORS-enabled TCS tracking endpoint"""
    if request.method == 'OPTIONS':
        # Handle preflight request
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, OPTIONS')
        return response
    
    try:
        from tcs_scraper_demo import track_tcs_demo, validate_tracking_number
        
        data = request.get_json()
        tracking_number = data.get('tracking_number')
        
        if not tracking_number:
            return jsonify({'success': False, 'error': 'Tracking number is required'})
        
        tracking_number = str(tracking_number).strip()
        if not validate_tracking_number(tracking_number):
            return jsonify({'success': False, 'error': 'Incorrect Number'})
        
        print(f"📦 Tracking: {tracking_number}")
        
        tracking_data = track_tcs_demo(tracking_number, headless=True)
        
        if tracking_data.get('success', False):
            print(f"✅ Success: {tracking_data.get('current_status')}")
            return jsonify({'success': True, 'data': tracking_data})
        else:
            error_msg = tracking_data.get('error', 'Failed to fetch tracking information')
            print(f"❌ Failed: {error_msg}")
            
            # Categorize errors
            if 'not found' in error_msg.lower() or 'invalid' in error_msg.lower():
                error_msg = 'Incorrect Number'
            
            return jsonify({'success': False, 'error': error_msg})
    
    except ImportError as e:
        return jsonify({'success': False, 'error': 'TCS scraper module not available'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'System error: {str(e)}'})

@app.route('/test')
def test():
    return jsonify({'message': 'CORS-enabled server working!', 'status': 'ok'})

if __name__ == '__main__':
    print("🚀 Starting CORS-enabled TCS server on port 5001...")
    app.run(host='0.0.0.0', port=5001, debug=True, use_reloader=False)
'''
    
    with open('cors_server.py', 'w') as f:
        f.write(server_code)
    
    print("📄 Created cors_server.py with CORS support")
    print("🚀 Run: python cors_server.py")
    print("🌐 Test URL: http://localhost:5001/api/track-tcs-public")

if __name__ == "__main__":
    # Run diagnostic tests
    test_terminal_vs_api()
    
    # Test CORS
    cors_working = test_cors_configuration()
    
    if not cors_working:
        print("\n💡 SOLUTION: CORS issue detected!")
        print("Creating CORS-enabled server...")
        create_cors_enabled_server()
        print("\n🔧 NEXT STEPS:")
        print("1. Install flask-cors: pip install flask-cors")
        print("2. Run: python cors_server.py")
        print("3. Update browser to use port 5001")
    else:
        print("\n✅ CORS appears to be working")
        print("🔍 Issue may be elsewhere - check browser console for errors")
