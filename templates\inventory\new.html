{% extends 'base.html' %}

{% block title %}Add New Stock - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Add New Stock</h4>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('new_stock') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">Product <span class="text-danger">*</span></label>
                                    <select class="form-control" id="product_id" name="product_id" required>
                                        <option value="">Select Product</option>
                                        {% for product in products %}
                                        <option value="{{ product.product_id }}"
                                                data-division="{{ product.division_name }}"
                                                data-strength="{{ product.strength }}">
                                            {{ product.display_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">Only products with valid division assignments are shown</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse</label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">Select Warehouse</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="batch_number">Batch Number</label>
                                    <input type="text" class="form-control" id="batch_number" name="batch_number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="stock_quantity">Quantity</label>
                                    <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="manufacturing_date">Manufacturing Date</label>
                                    <input type="date" class="form-control" id="manufacturing_date" name="manufacturing_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expiry_date">Expiry Date</label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12 text-right">
                                <a href="{{ url_for('inventory') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Add Stock</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
