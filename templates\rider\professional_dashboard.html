{% extends "base.html" %}

{% block title %}Rider Dashboard - Professional{% endblock %}

{% block head %}
<style>
.map-container {
    height: 300px;
    width: 100%;
    border-radius: 8px;
    margin: 10px 0;
}
.route-button {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    transition: all 0.3s;
}
.route-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Professional Rider Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">🚚 Professional Rider Dashboard</h2>
                    <p class="mb-0">Complete Order Workflow Management</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Performance Stats -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ today_stats.completed or 0 }}</h1>
                    <h4>✅ Delivered Today</h4>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ my_orders|length }}</h1>
                    <h4>🚛 In Progress</h4>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ ready_orders|length }}</h1>
                    <h4>📦 Available for Pickup</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Orders for Pickup -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">📦 Orders Ready for Pickup</h4>
                    <small>Click "PICKUP" to claim an order</small>
                </div>
                <div class="card-body">
                    {% if ready_orders %}
                    <div class="row">
                        {% for order in ready_orders %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="text-primary">📋 {{ order.order_id }}</h5>
                                            <p><strong>👤 Customer:</strong> {{ order.customer_name }}</p>
                                            <p><strong>💰 Amount:</strong> Rs.{{ "{:,.0f}".format(order.order_amount) }}</p>
                                            <p><strong>📍 Address:</strong> {{ order.customer_address }}</p>
                                            <p><strong>📞 Phone:</strong> {{ order.customer_phone }}</p>
                                            {% if order.special_instructions %}
                                            <p><strong>📝 Instructions:</strong> {{ order.special_instructions }}</p>
                                            {% endif %}
                                            {% if order.cod_amount > 0 %}
                                            <p><strong>💵 COD:</strong> Rs.{{ "{:,.0f}".format(order.cod_amount) }}</p>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="mb-3">
                                                {% if order.priority_level > 1 %}
                                                <span class="badge badge-danger p-2">🔥 HIGH PRIORITY</span>
                                                {% else %}
                                                <span class="badge badge-info p-2">📦 NORMAL</span>
                                                {% endif %}
                                            </div>
                                            <button type="button" class="btn btn-success btn-lg btn-block mb-2"
                                                    onclick="pickupOrder('{{ order.order_id }}')">
                                                <i class="fas fa-hand-paper"></i><br>
                                                PICKUP ORDER
                                            </button>
                                            {% if order.delivery_latitude and order.delivery_longitude %}
                                            <button type="button" class="btn route-button btn-sm btn-block"
                                                    onclick="showRoute({{ order.delivery_latitude }}, {{ order.delivery_longitude }}, '{{ order.customer_address }}')">
                                                <i class="fas fa-map-marker-alt"></i> View on Map
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h3>🎉 No Orders Available</h3>
                        <p class="text-muted">All orders are either assigned or not ready for pickup yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- My Current Orders -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">🚛 My Current Orders</h4>
                    <small>Orders assigned to you</small>
                </div>
                <div class="card-body">
                    {% if my_orders %}
                    <div class="row">
                        {% for order in my_orders %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="text-warning">📋 {{ order.order_id }}</h5>
                                            <p><strong>👤 Customer:</strong> {{ order.customer_name }}</p>
                                            <p><strong>💰 Amount:</strong> Rs.{{ "{:,.0f}".format(order.order_amount) }}</p>
                                            <p><strong>📍 Address:</strong> {{ order.customer_address }}</p>
                                            <p><strong>📞 Phone:</strong> {{ order.customer_phone }}</p>
                                            {% if order.special_instructions %}
                                            <p><strong>📝 Instructions:</strong> {{ order.special_instructions }}</p>
                                            {% endif %}
                                            {% if order.cod_amount > 0 %}
                                            <p><strong>💵 COD:</strong> Rs.{{ "{:,.0f}".format(order.cod_amount) }}</p>
                                            {% endif %}
                                            {% if order.estimated_delivery_time %}
                                            <p><strong>⏰ ETA:</strong> {{ order.estimated_delivery_time|format_datetime }}</p>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="mb-3">
                                                {% if order.status == 'picked_up' %}
                                                <span class="badge badge-info p-2">🚛 PICKED UP</span>
                                                {% elif order.status == 'out_for_delivery' %}
                                                <span class="badge badge-warning p-2">🚚 OUT FOR DELIVERY</span>
                                                {% endif %}
                                            </div>
                                            
                                            {% if order.status in ['picked_up', 'out_for_delivery'] %}
                                            <button type="button" class="btn btn-success btn-lg btn-block mb-2" 
                                                    onclick="deliverOrder('{{ order.order_id }}', 'delivered')">
                                                <i class="fas fa-check-circle"></i><br>
                                                DELIVERED
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm btn-block mb-2"
                                                    onclick="deliverOrder('{{ order.order_id }}', 'failed_delivery')">
                                                <i class="fas fa-times"></i> FAILED
                                            </button>
                                            {% endif %}

                                            {% if order.google_maps_route %}
                                            <a href="{{ order.google_maps_route }}" target="_blank"
                                               class="btn route-button btn-sm btn-block mb-2">
                                                <i class="fas fa-route"></i> Google Maps Route
                                            </a>
                                            {% endif %}

                                            {% if order.delivery_latitude and order.delivery_longitude %}
                                            <button type="button" class="btn btn-outline-info btn-sm btn-block"
                                                    onclick="showRoute({{ order.delivery_latitude }}, {{ order.delivery_longitude }}, '{{ order.customer_address }}')">
                                                <i class="fas fa-map-marker-alt"></i> View Location
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-truck fa-4x text-muted mb-3"></i>
                        <h3>📋 No Current Orders</h3>
                        <p class="text-muted">Pick up orders from the "Ready for Pickup" section above.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">⚡ Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('riders_orders') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-list"></i> My Order History
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-info btn-block" onclick="refreshDashboard()">
                                <i class="fas fa-sync"></i> Refresh Orders
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="tel:************" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-phone"></i> Call Office
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-home"></i> Main Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pickup Confirmation Modal -->
<div class="modal fade" id="pickupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h4 class="modal-title">🚚 Pickup Confirmation</h4>
            </div>
            <div class="modal-body text-center">
                <h4>Pickup Order <span id="pickupOrderId"></span>?</h4>
                <p>Are you ready to pick up this order from the warehouse?</p>
                <div class="form-group">
                    <label>Your Name:</label>
                    <input type="text" class="form-control" id="riderName" placeholder="Enter your name" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmPickupBtn">Confirm Pickup</button>
            </div>
        </div>
    </div>
</div>

<!-- Delivery Confirmation Modal -->
<div class="modal fade" id="deliveryModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title">📦 Delivery Confirmation</h4>
            </div>
            <div class="modal-body">
                <h4>Update Order <span id="deliveryOrderId"></span></h4>
                <div class="form-group">
                    <label>Delivery Status:</label>
                    <select class="form-control" id="deliveryStatus">
                        <option value="delivered">✅ Successfully Delivered</option>
                        <option value="failed_delivery">❌ Failed Delivery</option>
                        <option value="customer_not_available">🚫 Customer Not Available</option>
                        <option value="cancelled_by_customer">❌ Cancelled by Customer</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Customer OTP (if provided):</label>
                    <input type="text" class="form-control" id="customerOtp" placeholder="Enter OTP">
                </div>
                <div class="form-group">
                    <label>COD Collected:</label>
                    <select class="form-control" id="codCollected">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Delivery Notes:</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3" placeholder="Any additional notes..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmDeliveryBtn">Update Status</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentOrderId = '';

function pickupOrder(orderId) {
    currentOrderId = orderId;
    document.getElementById('pickupOrderId').textContent = orderId;
    $('#pickupModal').modal('show');
}

function deliverOrder(orderId, status) {
    currentOrderId = orderId;
    document.getElementById('deliveryOrderId').textContent = orderId;
    document.getElementById('deliveryStatus').value = status;
    $('#deliveryModal').modal('show');
}

document.getElementById('confirmPickupBtn').addEventListener('click', function() {
    const riderName = document.getElementById('riderName').value;
    
    if (!riderName.trim()) {
        alert('Please enter your name');
        return;
    }
    
    const formData = new FormData();
    formData.append('order_id', currentOrderId);
    formData.append('rider_name', riderName);
    
    fetch('/rider/pickup_order', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#pickupModal').modal('hide');
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Error picking up order');
    });
});

document.getElementById('confirmDeliveryBtn').addEventListener('click', function() {
    const deliveryStatus = document.getElementById('deliveryStatus').value;
    const customerOtp = document.getElementById('customerOtp').value;
    const codCollected = document.getElementById('codCollected').value;
    const deliveryNotes = document.getElementById('deliveryNotes').value;
    
    const formData = new FormData();
    formData.append('order_id', currentOrderId);
    formData.append('delivery_status', deliveryStatus);
    formData.append('customer_otp', customerOtp);
    formData.append('cod_collected', codCollected);
    formData.append('delivery_notes', deliveryNotes);
    
    fetch('/rider/deliver_order', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#deliveryModal').modal('hide');
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Error updating delivery status');
    });
});

function refreshDashboard() {
    location.reload();
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);

// Google Maps Integration
let map;
let directionsService;
let directionsRenderer;

function initMap() {
    // Initialize map centered on Karachi
    map = new google.maps.Map(document.getElementById('mapContainer'), {
        zoom: 12,
        center: { lat: 24.8607, lng: 67.0011 }
    });

    directionsService = new google.maps.DirectionsService();
    directionsRenderer = new google.maps.DirectionsRenderer();
    directionsRenderer.setMap(map);
}

function showRoute(destLat, destLng, address) {
    // Get current location
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const origin = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            };
            const destination = { lat: destLat, lng: destLng };

            // Calculate route
            directionsService.route({
                origin: origin,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);

                    // Show map modal
                    document.getElementById('mapModalTitle').textContent = `Route to: ${address}`;
                    $('#mapModal').modal('show');
                } else {
                    alert('Could not calculate route: ' + status);
                }
            });
        }, function() {
            // Fallback to warehouse location
            const warehouseLocation = { lat: 24.8607, lng: 67.0011 };
            const destination = { lat: destLat, lng: destLng };

            directionsService.route({
                origin: warehouseLocation,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);
                    document.getElementById('mapModalTitle').textContent = `Route to: ${address}`;
                    $('#mapModal').modal('show');
                } else {
                    alert('Could not calculate route: ' + status);
                }
            });
        });
    } else {
        alert('Geolocation is not supported by this browser.');
    }
}

function getCurrentLocation() {
    return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(resolve, reject);
        } else {
            reject(new Error('Geolocation not supported'));
        }
    });
}

// Update delivery functions to include location
async function updateDeliveryWithLocation(orderId, status, notes, codCollected) {
    try {
        const position = await getCurrentLocation();

        const formData = new FormData();
        formData.append('order_id', orderId);
        formData.append('delivery_status', status);
        formData.append('delivery_notes', notes);
        formData.append('cod_collected', codCollected);
        formData.append('delivery_latitude', position.coords.latitude);
        formData.append('delivery_longitude', position.coords.longitude);

        const response = await fetch('/rider/deliver_order', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Location error:', error);

        // Fallback without location
        const formData = new FormData();
        formData.append('order_id', orderId);
        formData.append('delivery_status', status);
        formData.append('delivery_notes', notes);
        formData.append('cod_collected', codCollected);

        const response = await fetch('/rider/deliver_order', {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }
}
</script>

<!-- Google Maps Modal -->
<div class="modal fade" id="mapModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h4 class="modal-title" id="mapModalTitle">Delivery Route</h4>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="mapContainer" class="map-container"></div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-primary" onclick="openInGoogleMaps()">
                        <i class="fas fa-external-link-alt"></i> Open in Google Maps App
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openInGoogleMaps() {
    // This will be implemented when route is calculated
    alert('Opening in Google Maps app...');
}

// Load Google Maps API key dynamically
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/get_google_maps_key')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.enabled && data.api_key) {
                // Load Google Maps API with the retrieved key
                const script = document.createElement('script');
                script.src = `https://maps.googleapis.com/maps/api/js?key=${data.api_key}&callback=initMap`;
                script.async = true;
                script.defer = true;
                document.head.appendChild(script);
            } else {
                console.warn('Google Maps API key not configured or disabled');
                // Show message to user that maps are not available
                showMapsUnavailableMessage();
            }
        })
        .catch(error => {
            console.error('Error loading Google Maps API key:', error);
            showMapsUnavailableMessage();
        });
});

function showMapsUnavailableMessage() {
    // Replace map containers with message
    const mapContainers = document.querySelectorAll('.map-container, #map');
    mapContainers.forEach(container => {
        container.innerHTML = `
            <div class="alert alert-warning text-center">
                <h5><i class="fas fa-exclamation-triangle"></i> Google Maps Not Available</h5>
                <p>Google Maps API key is not configured. Please contact administrator.</p>
                <a href="/admin/api_keys" class="btn btn-primary btn-sm">
                    <i class="fas fa-key"></i> Configure API Key
                </a>
            </div>
        `;
    });
}
</script>
{% endblock %}
