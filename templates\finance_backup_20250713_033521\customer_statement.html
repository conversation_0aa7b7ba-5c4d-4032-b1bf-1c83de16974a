{% extends "base.html" %}

{% block title %}Customer Statement - {{ statement_data.customer.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Statement Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-file-invoice-dollar"></i> Customer Statement
                            </h4>
                            <small>{{ statement_data.customer.name }} ({{ statement_data.customer.customer_id }})</small>
                        </div>
                        <div class="col-md-4 text-right">
                            <button class="btn btn-light" onclick="window.print()">
                                <i class="fas fa-print"></i> Print
                            </button>
                            <a href="{{ url_for('finance') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Customer Information -->
                        <div class="col-md-6">
                            <h6><strong>Customer Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Customer ID:</strong></td>
                                    <td>{{ statement_data.customer.customer_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ statement_data.customer.name }}</td>
                                </tr>
                                {% if statement_data.customer.phone %}
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ statement_data.customer.phone }}</td>
                                </tr>
                                {% endif %}
                                {% if statement_data.customer.email %}
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ statement_data.customer.email }}</td>
                                </tr>
                                {% endif %}
                                {% if statement_data.customer.address %}
                                <tr>
                                    <td><strong>Address:</strong></td>
                                    <td>{{ statement_data.customer.address }}</td>
                                </tr>
                                {% endif %}
                                {% if statement_data.customer.credit_limit %}
                                <tr>
                                    <td><strong>Credit Limit:</strong></td>
                                    <td>₹{{ statement_data.customer.credit_limit | format_currency }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        
                        <!-- Statement Summary -->
                        <div class="col-md-6">
                            <h6><strong>Statement Summary</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Statement Date:</strong></td>
                                    <td>{{ now | format_datetime }}</td>
                                </tr>
                                {% if start_date and end_date %}
                                <tr>
                                    <td><strong>Period:</strong></td>
                                    <td>{{ start_date }} to {{ end_date }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Total Transactions:</strong></td>
                                    <td>{{ statement_data.summary.transaction_count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Debits:</strong></td>
                                    <td class="text-danger">₹{{ statement_data.summary.total_debits | format_currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Credits:</strong></td>
                                    <td class="text-success">₹{{ statement_data.summary.total_credits | format_currency }}</td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>Current Balance:</strong></td>
                                    <td class="{% if statement_data.summary.current_balance > 0 %}text-danger{% elif statement_data.summary.current_balance < 0 %}text-success{% endif %}">
                                        <strong>₹{{ statement_data.summary.current_balance | format_currency }}</strong>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('customer_statement', customer_id=statement_data.customer.customer_id) }}" class="form-inline">
                        <div class="form-group mr-3">
                            <label for="start_date" class="mr-2">From:</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ start_date }}">
                        </div>
                        <div class="form-group mr-3">
                            <label for="end_date" class="mr-2">To:</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ end_date }}">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <a href="{{ url_for('customer_statement', customer_id=statement_data.customer.customer_id) }}" class="btn btn-secondary ml-2">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </form>
                </div>
            </div>

            <!-- Transaction History -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Transaction History</h5>
                </div>
                <div class="card-body">
                    {% if statement_data.transactions %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Reference</th>
                                    <th>Type</th>
                                    <th>Debit</th>
                                    <th>Credit</th>
                                    <th>Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in statement_data.transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_date | format_datetime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>
                                        {% if transaction.reference_type == 'order' %}
                                        <a href="{{ url_for('view_order', order_id=transaction.reference_id) }}">{{ transaction.reference_id }}</a>
                                        {% elif transaction.reference_type == 'payment' %}
                                        <span class="badge badge-success">{{ transaction.reference_id }}</span>
                                        {% else %}
                                        {{ transaction.reference_id or 'N/A' }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'debit' %}
                                        <span class="badge badge-danger">Debit</span>
                                        {% else %}
                                        <span class="badge badge-success">Credit</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-danger">
                                        {% if transaction.transaction_type == 'debit' %}
                                        ₹{{ transaction.amount | format_currency }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td class="text-success">
                                        {% if transaction.transaction_type == 'credit' %}
                                        ₹{{ transaction.amount | format_currency }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td class="{% if transaction.balance > 0 %}text-danger{% elif transaction.balance < 0 %}text-success{% endif %}">
                                        <strong>₹{{ transaction.balance | format_currency }}</strong>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <br>No transactions found for the selected period
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('finance') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Finance
                        </a>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Statement
                        </button>
                        <a href="{{ url_for('customer_statement', customer_id=statement_data.customer.customer_id) }}?format=pdf" class="btn btn-info">
                            <i class="fas fa-file-pdf"></i> Download PDF
                        </a>
                        <a href="{{ url_for('customer_statement', customer_id=statement_data.customer.customer_id) }}?format=excel" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </a>
                        <a href="{{ url_for('process_payment') }}?customer_id={{ statement_data.customer.customer_id }}" class="btn btn-warning">
                            <i class="fas fa-credit-card"></i> Record Payment
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header .btn, .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-header {
        background-color: #007bff !important;
        -webkit-print-color-adjust: exact;
    }
    
    body {
        font-size: 12px;
    }
    
    .table {
        font-size: 11px;
    }
}

.balance-positive {
    color: #dc3545;
    font-weight: bold;
}

.balance-negative {
    color: #28a745;
    font-weight: bold;
}

.balance-zero {
    color: #6c757d;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default date range if not provided
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    if (!startDateInput.value) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
    
    if (!endDateInput.value) {
        const today = new Date();
        endDateInput.value = today.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
