{% extends 'base.html' %}

{% block title %}Role Permissions Management{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .permission-group {
        margin-bottom: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        overflow: hidden;
    }

    .permission-group-header {
        background-color: #f5f5f5;
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 1px solid #e0e0e0;
    }

    .permission-group-body {
        padding: 15px;
    }

    .role-template {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .role-template-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .role-template-description {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .audit-log-entry {
        margin-bottom: 10px;
        padding: 10px;
        border-left: 3px solid #0572CE;
        background-color: #f9f9f9;
    }

    .audit-log-entry .timestamp {
        color: #666;
        font-size: 0.8rem;
    }

    .audit-log-entry .username {
        font-weight: bold;
    }

    .audit-log-entry .action {
        color: #0572CE;
    }

    .audit-log-entry .role {
        font-weight: bold;
    }

    .audit-log-entry .permission {
        font-style: italic;
    }

    .permission-visualization {
        margin-top: 20px;
    }

    .permission-matrix {
        width: 100%;
        border-collapse: collapse;
    }

    .permission-matrix th, .permission-matrix td {
        border: 1px solid #e0e0e0;
        padding: 8px;
        text-align: center;
    }

    .permission-matrix th {
        background-color: #f5f5f5;
    }

    .permission-matrix .has-permission {
        background-color: #d4edda;
        color: #155724;
    }

    .permission-matrix .no-permission {
        background-color: #f8d7da;
        color: #721c24;
    }

    .dashboard-widget-permission {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .widget-preview {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
        background-color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Role Permissions Management: {{ role|title }}</h5>
                    <div>
                        <a href="{{ url_for('users.roles') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Roles
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if role == 'admin' %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> The admin role always has all permissions and cannot be modified.
                    </div>
                    {% else %}

                    <!-- Tabs Navigation -->
                    <ul class="nav nav-tabs" id="permissionTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="permissions-tab" data-toggle="tab" href="#permissions" role="tab">
                                <i class="fas fa-key"></i> Permissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="templates-tab" data-toggle="tab" href="#templates" role="tab">
                                <i class="fas fa-copy"></i> Role Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="visualization-tab" data-toggle="tab" href="#visualization" role="tab">
                                <i class="fas fa-chart-bar"></i> Visualization
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="widgets-tab" data-toggle="tab" href="#widgets" role="tab">
                                <i class="fas fa-th-large"></i> Dashboard Widgets
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="audit-tab" data-toggle="tab" href="#audit" role="tab">
                                <i class="fas fa-history"></i> Audit Log
                            </a>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content mt-3" id="permissionTabsContent">
                        <!-- Permissions Tab -->
                        <div class="tab-pane fade show active" id="permissions" role="tabpanel">
                            <form method="post" action="{{ url_for('users.permissions', role=role) }}">
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Permissions for {{ role|title }} Role</h6>
                                                <div>
                                                    <button type="button" class="btn btn-light btn-sm" id="selectAll">
                                                        <i class="fas fa-check-square"></i> Select All
                                                    </button>
                                                    <button type="button" class="btn btn-light btn-sm" id="deselectAll">
                                                        <i class="fas fa-square"></i> Deselect All
                                                    </button>
                                                    <button type="button" class="btn btn-warning btn-sm" id="defaultPermissions">
                                                        <i class="fas fa-undo"></i> Default Permissions
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                {% for category, category_permissions in permissions.items() %}
                                                <div class="permission-category mb-4">
                                                    <h5>
                                                        <div class="form-check">
                                                            <input class="form-check-input category-checkbox" type="checkbox" id="category_{{ category|replace(' ', '_') }}">
                                                            <label class="form-check-label" for="category_{{ category|replace(' ', '_') }}">
                                                                {{ category }}
                                                            </label>
                                                        </div>
                                                    </h5>
                                                    <div class="row">
                                                        {% for permission in category_permissions %}
                                                        <div class="col-md-4 mb-2">
                                                            <div class="form-check">
                                                                <input class="form-check-input permission-checkbox"
                                                                       type="checkbox"
                                                                       name="permissions"
                                                                       value="{{ permission.permission_id }}"
                                                                       id="permission_{{ permission.permission_code }}"
                                                                       data-category="{{ category|replace(' ', '_') }}"
                                                                       {% if permission.permission_code in role_permissions %}checked{% endif %}>
                                                                <label class="form-check-label" for="permission_{{ permission.permission_code }}"
                                                                       title="{{ permission.description }}">
                                                                    {{ permission.permission_name }}
                                                                    <i class="fas fa-info-circle text-info" data-toggle="tooltip" title="{{ permission.description }}"></i>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">Save Permissions</button>
                                    <a href="{{ url_for('users.roles') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>

                        <!-- Role Templates Tab -->
                        <div class="tab-pane fade" id="templates" role="tabpanel">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Role templates provide predefined sets of permissions for common user roles. Applying a template will replace all current permissions for this role.
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="role-template">
                                        <div class="role-template-title">Administrator</div>
                                        <div class="role-template-description">Full system access with all permissions</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary apply-template" data-template="admin">
                                                Apply Template
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary view-template" data-template="admin">
                                                View Permissions
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="role-template">
                                        <div class="role-template-title">Manager</div>
                                        <div class="role-template-description">Manage operations but not system settings</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary apply-template" data-template="manager">
                                                Apply Template
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary view-template" data-template="manager">
                                                View Permissions
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="role-template">
                                        <div class="role-template-title">Sales Representative</div>
                                        <div class="role-template-description">Create and view orders</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary apply-template" data-template="sales">
                                                Apply Template
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary view-template" data-template="sales">
                                                View Permissions
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="role-template">
                                        <div class="role-template-title">Warehouse Staff</div>
                                        <div class="role-template-description">Manage inventory and process orders</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary apply-template" data-template="warehouse">
                                                Apply Template
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary view-template" data-template="warehouse">
                                                View Permissions
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permission Visualization Tab -->
                        <div class="tab-pane fade" id="visualization" role="tabpanel">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> This visualization shows which permissions are assigned to each role in the system.
                            </div>

                            <div class="permission-visualization">
                                <div class="table-responsive">
                                    <table class="permission-matrix">
                                        <thead>
                                            <tr>
                                                <th>Permission</th>
                                                <th>Admin</th>
                                                <th>Manager</th>
                                                <th>Sales</th>
                                                <th>Warehouse</th>
                                                <th>Rider</th>
                                                <th>User</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- This will be populated via AJAX -->
                                            <tr>
                                                <td colspan="7" class="text-center">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Dashboard Widgets Tab -->
                        <div class="tab-pane fade" id="widgets" role="tabpanel">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Configure which dashboard widgets are visible to this role.
                            </div>

                            <form id="widgetPermissionsForm">
                                <div class="dashboard-widget-permission">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="dashboard_orders_widget" name="widget_permissions[]" value="dashboard_orders_widget"
                                               {% if 'dashboard_orders_widget' in role_permissions %}checked{% endif %}>
                                        <label class="custom-control-label" for="dashboard_orders_widget">
                                            <strong>Orders Widget</strong>
                                        </label>
                                    </div>
                                    <div class="text-muted">Shows order statistics on the dashboard</div>
                                    <div class="widget-preview">
                                        <div class="text-center">
                                            <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                                            <h5>Total Orders</h5>
                                            <h3>63</h3>
                                        </div>
                                    </div>
                                </div>

                                <div class="dashboard-widget-permission mt-3">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="dashboard_inventory_widget" name="widget_permissions[]" value="dashboard_inventory_widget"
                                               {% if 'dashboard_inventory_widget' in role_permissions %}checked{% endif %}>
                                        <label class="custom-control-label" for="dashboard_inventory_widget">
                                            <strong>Inventory Widget</strong>
                                        </label>
                                    </div>
                                    <div class="text-muted">Shows inventory statistics on the dashboard</div>
                                    <div class="widget-preview">
                                        <div class="text-center">
                                            <i class="fas fa-boxes fa-2x text-success mb-2"></i>
                                            <h5>Products</h5>
                                            <h3>75</h3>
                                        </div>
                                    </div>
                                </div>

                                <div class="dashboard-widget-permission mt-3">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="dashboard_workflow_widget" name="widget_permissions[]" value="dashboard_workflow_widget"
                                               {% if 'dashboard_workflow_widget' in role_permissions %}checked{% endif %}>
                                        <label class="custom-control-label" for="dashboard_workflow_widget">
                                            <strong>Workflow Widget</strong>
                                        </label>
                                    </div>
                                    <div class="text-muted">Shows order workflow on the dashboard</div>
                                    <div class="widget-preview">
                                        <div class="progress" style="height: 30px;">
                                            <div class="progress-bar bg-warning" style="width: 20%">Placed</div>
                                            <div class="progress-bar bg-info" style="width: 15%">Approved</div>
                                            <div class="progress-bar bg-primary" style="width: 25%">Processing</div>
                                            <div class="progress-bar bg-success" style="width: 40%">Delivered</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="button" class="btn btn-primary" id="saveWidgetPermissions">Save Widget Permissions</button>
                                </div>
                            </form>
                        </div>

                        <!-- Audit Log Tab -->
                        <div class="tab-pane fade" id="audit" role="tabpanel">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> This log shows all permission changes for this role.
                            </div>

                            <div id="auditLogEntries">
                                <!-- This will be populated via AJAX -->
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Select all permissions
        $('#selectAll').click(function() {
            $('.permission-checkbox').prop('checked', true);
            updateCategoryCheckboxes();
        });

        // Deselect all permissions
        $('#deselectAll').click(function() {
            $('.permission-checkbox').prop('checked', false);
            updateCategoryCheckboxes();
        });

        // Category checkbox functionality
        $('.category-checkbox').change(function() {
            const category = $(this).attr('id').replace('category_', '');
            const isChecked = $(this).prop('checked');

            // Select/deselect all permissions in this category
            $(`.permission-checkbox[data-category="${category}"]`).prop('checked', isChecked);
        });

        // Update category checkboxes based on permission checkboxes
        $('.permission-checkbox').change(function() {
            updateCategoryCheckboxes();
        });

        // Function to update category checkboxes
        function updateCategoryCheckboxes() {
            $('.category-checkbox').each(function() {
                const category = $(this).attr('id').replace('category_', '');
                const totalPermissions = $(`.permission-checkbox[data-category="${category}"]`).length;
                const checkedPermissions = $(`.permission-checkbox[data-category="${category}"]:checked`).length;

                // If all permissions in the category are checked, check the category checkbox
                $(this).prop('checked', totalPermissions === checkedPermissions && totalPermissions > 0);
            });
        }

        // Initialize category checkboxes
        updateCategoryCheckboxes();

        // Default Permissions functionality
        $('#defaultPermissions').click(function() {
            const role = '{{ role }}';

            if (confirm(`Are you sure you want to restore default permissions for the ${role} role? This will replace all current permissions with the default set for this role.`)) {
                // AJAX call to restore default permissions
                $.ajax({
                    url: `/users/roles/${role}/default-permissions`,
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            alert('Default permissions restored successfully!');
                            // Reload the page to show updated permissions
                            window.location.reload();
                        } else {
                            alert('Error restoring default permissions: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('An error occurred while restoring default permissions.');
                    }
                });
            }
        });

        // Role Template functionality
        $('.apply-template').click(function() {
            const template = $(this).data('template');
            const role = '{{ role }}';

            if (confirm(`Are you sure you want to apply the ${template} template to the ${role} role? This will replace all current permissions.`)) {
                // AJAX call to apply template
                $.ajax({
                    url: `/users/roles/${role}/apply-template`,
                    type: 'POST',
                    data: {
                        template: template
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Template applied successfully!');
                            // Reload the page to show updated permissions
                            window.location.reload();
                        } else {
                            alert('Error applying template: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('An error occurred while applying the template.');
                    }
                });
            }
        });

        // View Template Permissions
        $('.view-template').click(function() {
            const template = $(this).data('template');

            // AJAX call to get template permissions
            $.ajax({
                url: `/users/templates/${template}/permissions`,
                type: 'GET',
                success: function(response) {
                    if (response.permissions) {
                        // Create modal to display permissions
                        let permissionsList = '';
                        for (const category in response.permissions) {
                            permissionsList += `<h6>${category}</h6><ul>`;
                            response.permissions[category].forEach(permission => {
                                permissionsList += `<li>${permission.name} - ${permission.description}</li>`;
                            });
                            permissionsList += `</ul>`;
                        }

                        // Create and show modal
                        const modal = $(`
                            <div class="modal fade" tabindex="-1" role="dialog">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">${template.charAt(0).toUpperCase() + template.slice(1)} Template Permissions</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            ${permissionsList}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);

                        $('body').append(modal);
                        modal.modal('show');

                        // Remove modal from DOM when hidden
                        modal.on('hidden.bs.modal', function() {
                            modal.remove();
                        });
                    } else {
                        alert('Error loading template permissions: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while loading template permissions.');
                }
            });
        });

        // Load Permission Visualization
        $('#visualization-tab').on('click', function() {
            // AJAX call to get permission matrix data
            $.ajax({
                url: '/users/permissions/matrix',
                type: 'GET',
                success: function(response) {
                    if (response.permissions) {
                        // Clear loading indicator
                        $('.permission-matrix tbody').empty();

                        // Add rows for each permission
                        response.permissions.forEach(permission => {
                            const row = $('<tr></tr>');

                            // Add permission name cell
                            row.append(`<td>${permission.name}</td>`);

                            // Add cells for each role
                            ['admin', 'manager', 'sales', 'warehouse', 'rider', 'user'].forEach(role => {
                                const hasPermission = response.matrix[role].includes(permission.code);
                                const cellClass = hasPermission ? 'has-permission' : 'no-permission';
                                const icon = hasPermission ? '<i class="fas fa-check"></i>' : '<i class="fas fa-times"></i>';

                                row.append(`<td class="${cellClass}">${icon}</td>`);
                            });

                            $('.permission-matrix tbody').append(row);
                        });
                    } else {
                        $('.permission-matrix tbody').html('<tr><td colspan="7" class="text-center">Error loading permission matrix.</td></tr>');
                    }
                },
                error: function() {
                    $('.permission-matrix tbody').html('<tr><td colspan="7" class="text-center">Error loading permission matrix.</td></tr>');
                }
            });
        });

        // Save Widget Permissions
        $('#saveWidgetPermissions').click(function() {
            const role = '{{ role }}';
            const widgetPermissions = [];

            // Get all checked widget permissions
            $('input[name="widget_permissions[]"]:checked').each(function() {
                widgetPermissions.push($(this).val());
            });

            // AJAX call to save widget permissions
            $.ajax({
                url: `/users/roles/${role}/widget-permissions`,
                type: 'POST',
                data: {
                    permissions: widgetPermissions
                },
                success: function(response) {
                    if (response.success) {
                        alert('Widget permissions saved successfully!');
                    } else {
                        alert('Error saving widget permissions: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while saving widget permissions.');
                }
            });
        });

        // Load Audit Log
        $('#audit-tab').on('click', function() {
            const role = '{{ role }}';

            // AJAX call to get audit logs for this role
            $.ajax({
                url: `/users/permissions/audit-logs/${role}`,
                type: 'GET',
                success: function(response) {
                    if (response.logs) {
                        // Clear loading indicator
                        $('#auditLogEntries').empty();

                        if (response.logs.length === 0) {
                            $('#auditLogEntries').html('<div class="alert alert-info">No permission changes have been recorded for this role.</div>');
                            return;
                        }

                        // Add entries for each log
                        response.logs.forEach(log => {
                            const logHtml = `
                                <div class="audit-log-entry">
                                    <div class="timestamp">${log.timestamp}</div>
                                    <div>
                                        <span class="username">${log.username}</span>
                                        <span class="action">${log.action}</span> permission
                                        <span class="permission">${log.permission_name}</span>
                                    </div>
                                    ${log.previous_state && log.new_state ? `
                                    <div class="mt-1">
                                        <small>Changed from <strong>${log.previous_state}</strong> to <strong>${log.new_state}</strong></small>
                                    </div>
                                    ` : ''}
                                </div>
                            `;

                            $('#auditLogEntries').append(logHtml);
                        });
                    } else {
                        $('#auditLogEntries').html('<div class="alert alert-danger">Error loading audit logs.</div>');
                    }
                },
                error: function() {
                    $('#auditLogEntries').html('<div class="alert alert-danger">Error loading audit logs.</div>');
                }
            });
        });
    });
</script>
{% endblock %}
