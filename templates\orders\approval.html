{% extends 'base.html' %}

{% block title %}Order Approval - {{ order.order_id }} - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-0">
                                <i class="fas fa-clipboard-check"></i> Order Approval
                            </h3>
                            <p class="mb-0">Review and approve/reject order: <strong>{{ order.order_id }}</strong></p>
                        </div>
                        <div class="col-md-4 text-right">
                            <div class="progress bg-light" style="height: 25px;">
                                <div class="progress-bar bg-warning" role="progressbar" 
                                     style="width: {{ completion_percentage }}%">
                                    {{ completion_percentage }}% Complete
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Order Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order ID:</strong></td>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ order.customer_phone or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Address:</strong></td>
                                    <td>{{ order.customer_address or 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order Date:</strong></td>
                                    <td>{{ order.order_date|format_datetime }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-warning">{{ order.status }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Sales Agent:</strong></td>
                                    <td>{{ order.sales_agent or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td><strong>{{ order.order_amount|format_currency }}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Order Items ({{ order.total_items }} items)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Manufacturer</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ item.product_name }}</td>
                                    <td>{{ item.strength or 'N/A' }}</td>
                                    <td>{{ item.manufacturer or 'N/A' }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit_price|format_currency }}</td>
                                    <td>{{ item.line_total|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approval Actions -->
        <div class="col-md-4">
            <!-- Order Summary -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ order.total_items }}</h4>
                                <small class="text-muted">Total Items</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ order.total_quantity }}</h4>
                            <small class="text-muted">Total Quantity</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h3 class="text-info">{{ order.order_amount|format_currency }}</h3>
                        <small class="text-muted">Total Amount</small>
                    </div>
                </div>
            </div>

            <!-- Approval Form -->
            <div class="card mt-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-gavel"></i> Approval Decision</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="form-group">
                            <label for="note">Add Note (Optional):</label>
                            <textarea class="form-control" id="note" name="note" rows="4" 
                                      placeholder="Enter approval/rejection note..."></textarea>
                            <small class="form-text text-muted">
                                This note will be permanently attached to the order history.
                            </small>
                        </div>
                        
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" name="action" value="approve" 
                                        class="btn btn-success btn-block">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="submit" name="action" value="reject" 
                                        class="btn btn-danger btn-block">
                                    <i class="fas fa-times"></i> Reject
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Order Notes History -->
            {% if order_notes %}
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Order History</h5>
                </div>
                <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                    {% for note in order_notes %}
                    <div class="border-left border-{{ 'success' if note.note_type == 'approval' else 'danger' if note.note_type == 'rejection' else 'info' }} pl-3 mb-3">
                        <div class="d-flex justify-content-between">
                            <strong class="text-{{ 'success' if note.note_type == 'approval' else 'danger' if note.note_type == 'rejection' else 'info' }}">
                                {{ note.note_type.title() }}
                            </strong>
                            <small class="text-muted">{{ note.created_at|format_datetime }}</small>
                        </div>
                        <p class="mb-1">{{ note.note_text }}</p>
                        <small class="text-muted">by {{ note.created_by }}</small>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Back Button -->
    <div class="row mt-4">
        <div class="col-md-12">
            <a href="{{ url_for('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
        </div>
    </div>
</div>
{% endblock %}
