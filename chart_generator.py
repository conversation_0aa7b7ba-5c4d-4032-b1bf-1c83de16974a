"""
Chart Generator Module
Basic chart generation functionality for the ERP system
"""

import matplotlib.pyplot as plt
import pandas as pd
import io
import base64
from datetime import datetime, timedelta

def generate_sales_chart(data, chart_type='line'):
    """Generate sales chart"""
    try:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        if chart_type == 'line':
            ax.plot(data.get('labels', []), data.get('values', []))
        elif chart_type == 'bar':
            ax.bar(data.get('labels', []), data.get('values', []))
        
        ax.set_title('Sales Chart')
        ax.set_xlabel('Period')
        ax.set_ylabel('Amount')
        
        # Convert plot to base64 string
        img = io.BytesIO()
        plt.savefig(img, format='png', bbox_inches='tight')
        img.seek(0)
        plot_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        return plot_url
    except Exception as e:
        print(f"Chart generation error: {e}")
        return None

def generate_inventory_chart(data):
    """Generate inventory chart"""
    try:
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.bar(data.get('products', []), data.get('quantities', []))
        ax.set_title('Inventory Levels')
        ax.set_xlabel('Products')
        ax.set_ylabel('Quantity')
        
        # Convert plot to base64 string
        img = io.BytesIO()
        plt.savefig(img, format='png', bbox_inches='tight')
        img.seek(0)
        plot_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        return plot_url
    except Exception as e:
        print(f"Inventory chart generation error: {e}")
        return None

def generate_financial_chart(data):
    """Generate financial chart"""
    try:
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(data.get('months', []), data.get('revenue', []), label='Revenue')
        ax.plot(data.get('months', []), data.get('expenses', []), label='Expenses')
        ax.set_title('Financial Overview')
        ax.set_xlabel('Month')
        ax.set_ylabel('Amount')
        ax.legend()
        
        # Convert plot to base64 string
        img = io.BytesIO()
        plt.savefig(img, format='png', bbox_inches='tight')
        img.seek(0)
        plot_url = base64.b64encode(img.getvalue()).decode()
        plt.close()
        
        return plot_url
    except Exception as e:
        print(f"Financial chart generation error: {e}")
        return None
