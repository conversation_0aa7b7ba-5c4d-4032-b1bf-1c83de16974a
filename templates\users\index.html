{% extends 'base.html' %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">User Management</h5>
                    <div>
                        <button type="button" class="btn btn-light btn-sm" id="exportCSV">
                            <i class="fas fa-file-csv"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-light btn-sm" id="exportPDF">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('users.add') }}" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> Add New User
                            </a>
                            <a href="{{ url_for('users.reset_password', user_id=current_user.id) }}" class="btn btn-secondary">
                                <i class="fas fa-key"></i> Change Password
                            </a>
                            <a href="{{ url_for('users.logs') }}" class="btn btn-info">
                                <i class="fas fa-clipboard-list"></i> View User Activity Logs
                            </a>
                        </div>
                    </div>

                    {% if action == 'add' %}
                    <!-- Add User Form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Add New User</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('users.add') }}">
                                        <div class="form-group">
                                            <label for="username">Username</label>
                                            <input type="text" class="form-control" id="username" name="username" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="password">Password</label>
                                            <input type="password" class="form-control" id="password" name="password" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Add User</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% elif action == 'change_password' %}
                    <!-- Change Password Form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Change Password</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('users.reset_password', user_id=current_user.id) }}">
                                        <div class="form-group">
                                            <label for="current_password">Current Password</label>
                                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="new_password">New Password</label>
                                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="confirm_password">Confirm New Password</label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Change Password</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <!-- Users List -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Username</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if users %}
                                    {% for user in users %}
                                    <tr>
                                        <td>{{ user.username }}</td>
                                        <td>
                                            <a href="{{ url_for('users.reset_password', user_id=user.id) }}" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-key"></i> Change Password
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="2" class="text-center">No users found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Export to CSV
        $('#exportCSV').click(function() {
            exportTableToCSV('users_list.csv');
        });

        // Export to PDF
        $('#exportPDF').click(function() {
            // Create a new jsPDF instance
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Add title
            doc.setFontSize(18);
            doc.text('Medivent Pharmaceuticals Pvt. Ltd.', 105, 15, { align: 'center' });
            doc.setFontSize(14);
            doc.text('User Management', 105, 25, { align: 'center' });

            // Add date
            doc.setFontSize(10);
            doc.text('Generated on: ' + new Date().toLocaleString(), 105, 35, { align: 'center' });

            // Get visible rows
            const rows = [];
            const headers = [];

            // Get headers
            $('table thead th').each(function() {
                headers.push($(this).text().trim());
            });

            rows.push(headers);

            // Get visible rows data
            $('table tbody tr').each(function() {
                const rowData = [];
                $(this).find('td').each(function() {
                    // For action buttons, just use "Actions" text
                    if ($(this).find('a').length > 0) {
                        rowData.push("Actions");
                    } else {
                        rowData.push($(this).text().trim());
                    }
                });
                rows.push(rowData);
            });

            // Add table to PDF
            doc.autoTable({
                head: [headers],
                body: rows.slice(1),
                startY: 40,
                theme: 'grid',
                headStyles: {
                    fillColor: [0, 123, 255],
                    textColor: 255,
                    fontStyle: 'bold'
                },
                alternateRowStyles: {
                    fillColor: [240, 240, 240]
                },
                margin: { top: 40 }
            });

            // Save the PDF
            doc.save('users_list.pdf');
        });

        // Export table to CSV
        function exportTableToCSV(filename) {
            var csv = [];

            // Get headers
            var headerRow = [];
            $('table thead th').each(function() {
                headerRow.push('"' + $(this).text().trim() + '"');
            });
            csv.push(headerRow.join(','));

            // Get rows data
            $('table tbody tr').each(function() {
                var row = [];
                $(this).find('td').each(function() {
                    // For action buttons, just use "Actions" text
                    if ($(this).find('a').length > 0) {
                        row.push('"Actions"');
                    } else {
                        var text = $(this).text().replace(/[\n\r]+/g, ' ').trim();
                        row.push('"' + text + '"');
                    }
                });
                csv.push(row.join(','));
            });

            // Download CSV file
            downloadCSV(csv.join('\n'), filename);
        }

        function downloadCSV(csv, filename) {
            try {
                var csvFile = new Blob([csv], {type: 'text/csv'});
                var downloadLink = document.createElement('a');

                downloadLink.download = filename;
                downloadLink.href = window.URL.createObjectURL(csvFile);
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);

                downloadLink.click();
                document.body.removeChild(downloadLink);
            } catch (e) {
                console.error("Error downloading CSV: ", e);
                alert("Error exporting CSV: " + e.message);
            }
        }
    });
</script>
{% endblock %}