{% extends 'base.html' %}

{% block title %}Order Details - Finance{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Order Details - {{ order.order_id }}</h5>
                    <div>
                        <a href="{{ url_for('finance') }}?view=payments" class="btn btn-sm btn-light">
                            <i class="fas fa-arrow-left"></i> Back to Payments
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Order Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">Order ID:</th>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge 
                                            {% if order.status == 'Placed' %}badge-info
                                            {% elif order.status == 'Approved' %}badge-primary
                                            {% elif order.status == 'Processing' %}badge-warning
                                            {% elif order.status == 'Ready for Pickup' %}badge-secondary
                                            {% elif order.status == 'Dispatched' %}badge-dark
                                            {% elif order.status == 'Delivered' %}badge-success
                                            {% elif order.status == 'Cancelled' %}badge-danger
                                            {% else %}badge-light{% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date }}</td>
                                </tr>
                                <tr>
                                    <th>Order Amount:</th>
                                    <td>{{ order.order_amount|format_currency }}</td>
                                </tr>
                                <tr>
                                    <th>Payment Method:</th>
                                    <td>{{ order.payment_method }}</td>
                                </tr>
                                <tr>
                                    <th>Sales Agent:</th>
                                    <td>{{ order.sales_agent }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Customer Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">Customer Name:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Customer Code:</th>
                                    <td>{{ order.customer_code if order.customer_code else 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Address:</th>
                                    <td>{{ order.customer_address }}</td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>{{ order.customer_phone }}</td>
                                </tr>
                                {% if order.customer_id %}
                                <tr>
                                    <th>Actions:</th>
                                    <td>
                                        <a href="{{ url_for('finance') }}?view=ledger&customer_id={{ order.customer_id }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-book"></i> View Ledger
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="text-muted">Order Items</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Line Total</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in order_items %}
                                        <tr>
                                            <td>{{ item.product_name }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.unit_price|format_currency }}</td>
                                            <td>{{ (item.quantity * item.unit_price)|format_currency }}</td>
                                            <td>
                                                <span class="badge 
                                                    {% if item.status == 'Placed' %}badge-info
                                                    {% elif item.status == 'Approved' %}badge-primary
                                                    {% elif item.status == 'Processing' %}badge-warning
                                                    {% elif item.status == 'Ready for Pickup' %}badge-secondary
                                                    {% elif item.status == 'Dispatched' %}badge-dark
                                                    {% elif item.status == 'Delivered' %}badge-success
                                                    {% elif item.status == 'Cancelled' %}badge-danger
                                                    {% elif item.status == 'Pending' %}badge-warning
                                                    {% elif item.status == 'Partially Approved' %}badge-info
                                                    {% else %}badge-light{% endif %}">
                                                    {{ item.status }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-right">Total:</th>
                                            <th>{{ order.order_amount|format_currency }}</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    {% if order.status == 'Dispatched' or order.status == 'Delivered' %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> This order has been dispatched. You can view the invoice and record payments.
                                <div class="mt-2">
                                    <a href="{{ url_for('finance') }}?view=payments&invoice_number={{ order.invoice_number }}" class="btn btn-primary">
                                        <i class="fas fa-file-invoice-dollar"></i> View Invoice
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
