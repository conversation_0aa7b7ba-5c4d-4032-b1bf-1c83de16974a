{% extends 'base.html' %}

{% block title %}Generate Invoice{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-invoice"></i> Generate Invoice
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Finance Step 3:</strong> Generate Invoice after delivery challan is created. 
                        This will make the order ready for warehouse dispatch.
                    </div>

                    <!-- Order Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Order Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Order ID:</strong></td>
                                            <td>{{ order.order_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Customer:</strong></td>
                                            <td>{{ order.customer_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Order Date:</strong></td>
                                            <td>{{ order.order_date }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge badge-info">{{ order.status }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Amount:</strong></td>
                                            <td><strong>₨{{ "{:,.2f}".format(order.order_amount) }}</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Finance Review</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Customer:</strong></td>
                                            <td>{{ order.customer_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Payment Method:</strong></td>
                                            <td>{{ order.payment_method or 'Cash' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Sales Agent:</strong></td>
                                            <td>{{ order.sales_agent or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Approved By:</strong></td>
                                            <td>{{ order.approved_by or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>DC Status:</strong></td>
                                            <td>
                                                <span class="badge badge-success">Generated</span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Workflow Progress -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Workflow Progress</h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress-steps">
                                        <div class="step completed">
                                            <div class="step-icon">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div class="step-label">Order Placed</div>
                                        </div>
                                        <div class="step completed">
                                            <div class="step-icon">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div class="step-label">Boss Approved</div>
                                        </div>
                                        <div class="step completed">
                                            <div class="step-icon">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div class="step-label">DC Generated</div>
                                        </div>
                                        <div class="step active">
                                            <div class="step-icon">
                                                <i class="fas fa-file-invoice"></i>
                                            </div>
                                            <div class="step-label">Generate Invoice</div>
                                        </div>
                                        <div class="step">
                                            <div class="step-icon">
                                                <i class="fas fa-truck"></i>
                                            </div>
                                            <div class="step-label">Dispatch</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Preview -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Invoice Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>Note:</strong> Invoice will be generated with a unique 5-digit number (INV00001 format).
                                        Please review customer ledger and payment terms before generating.
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Customer Information:</h6>
                                            <p>
                                                <strong>{{ order.customer_name }}</strong><br>
                                                {{ order.customer_address or 'Address not provided' }}<br>
                                                Phone: {{ order.customer_phone or 'N/A' }}
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Order Summary:</h6>
                                            <p>
                                                Order ID: {{ order.order_id }}<br>
                                                Order Date: {{ order.order_date }}<br>
                                                <strong>Total Amount: ₨{{ "{:,.2f}".format(order.order_amount) }}</strong>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-12">
                            <form method="POST" class="d-inline">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-file-invoice"></i> Generate Invoice
                                </button>
                            </form>
                            <a href="{{ url_for('finance_invoices') }}" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-arrow-left"></i> Back to Finance
                            </a>
                            <a href="{{ url_for('finance_pending_invoices') }}" class="btn btn-info btn-lg ml-2">
                                <i class="fas fa-list"></i> Pending Invoices
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
}

.step {
    text-align: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.completed:not(:last-child)::after {
    background-color: #28a745;
}

.step.active:not(:last-child)::after {
    background-color: #17a2b8;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    position: relative;
    z-index: 2;
}

.step.completed .step-icon {
    background-color: #28a745;
    color: white;
}

.step.active .step-icon {
    background-color: #17a2b8;
    color: white;
}

.step-label {
    font-size: 12px;
    font-weight: 500;
}
</style>
{% endblock %}
