{% extends 'base.html' %}

{% block title %}User Roles Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">User Roles Management</h5>
                </div>
                <div class="card-body">
                    <!-- Role Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Role Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <canvas id="roleChart" width="400" height="300"></canvas>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Role</th>
                                                        <th>Count</th>
                                                        <th>Percentage</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for role, count in role_counts.items() %}
                                                    <tr>
                                                        <td>{{ role|title }}</td>
                                                        <td>{{ count }}</td>
                                                        <td>{{ ((count / total_users) * 100)|round(1) }}%</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role Permissions -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Role Permissions</h5>
                                    <div>
                                        <button type="button" class="btn btn-light btn-sm" id="syncDefaultPermissions">
                                            <i class="fas fa-sync"></i> Sync Default Permissions
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle"></i> Click on a role name to manage its permissions.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Role</th>
                                                    <th>Users</th>
                                                    <th>Permission Count</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for role, count in role_counts.items() %}
                                                <tr>
                                                    <td>
                                                        <span class="badge
                                                            {% if role == 'admin' %}badge-danger
                                                            {% elif role == 'manager' %}badge-warning
                                                            {% elif role == 'sales' %}badge-success
                                                            {% elif role == 'warehouse' %}badge-info
                                                            {% elif role == 'rider' %}badge-primary
                                                            {% elif role == 'customer' %}badge-secondary
                                                            {% else %}badge-dark
                                                            {% endif %} p-2">
                                                            {{ role|title }}
                                                        </span>
                                                    </td>
                                                    <td>{{ count }}</td>
                                                    <td>{{ role_permission_counts.get(role, 0) }}</td>
                                                    <td>
                                                        <a href="{{ url_for('users.permissions', role=role) }}" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-key"></i> Manage Permissions
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permission Categories -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Permission Categories</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        {% for category, permissions in permission_categories.items() %}
                                        <div class="col-md-4 mb-4">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">{{ category }}</h6>
                                                </div>
                                                <div class="card-body">
                                                    <p><strong>Permissions:</strong> {{ permissions|length }}</p>
                                                    <ul class="list-group">
                                                        {% for permission in permissions[:5] %}
                                                        <li class="list-group-item">{{ permission.permission_name }}</li>
                                                        {% endfor %}
                                                        {% if permissions|length > 5 %}
                                                        <li class="list-group-item text-muted">
                                                            ... and {{ permissions|length - 5 }} more
                                                        </li>
                                                        {% endif %}
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-4">
                        <a href="{{ url_for('users.manage') }}" class="btn btn-secondary">Back to User Management</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // Role distribution chart
        const ctx = document.getElementById('roleChart').getContext('2d');
        const roleChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: [{% for role in role_counts.keys() %}'{{ role|title }}'{% if not loop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'User Roles',
                    data: [{% for count in role_counts.values() %}{{ count }}{% if not loop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(199, 199, 199, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'User Role Distribution'
                    }
                }
            }
        });

        // Sync default permissions
        $('#syncDefaultPermissions').click(function() {
            if (confirm('Are you sure you want to sync default permissions for all roles? This will reset any custom permissions you have set.')) {
                // Show loading spinner
                $(this).html('<i class="fas fa-spinner fa-spin"></i> Syncing...');
                $(this).prop('disabled', true);

                // Send AJAX request to sync permissions
                $.ajax({
                    url: '{{ url_for("users.sync_permissions") }}',
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            alert('Default permissions synced successfully!');
                            // Reload the page
                            window.location.reload();
                        } else {
                            // Show error message
                            alert('Error syncing default permissions: ' + response.message);
                            // Reset button
                            $('#syncDefaultPermissions').html('<i class="fas fa-sync"></i> Sync Default Permissions');
                            $('#syncDefaultPermissions').prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        // Show error message
                        alert('Error syncing default permissions: ' + error);
                        // Reset button
                        $('#syncDefaultPermissions').html('<i class="fas fa-sync"></i> Sync Default Permissions');
                        $('#syncDefaultPermissions').prop('disabled', false);
                    }
                });
            }
        });
    });
</script>
{% endblock %}
