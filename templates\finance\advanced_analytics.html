{% extends "base.html" %}

{% block title %}Advanced Financial Analytics - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Advanced Financial Analytics Styles */
    .analytics-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .analytics-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .analytics-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .analytics-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* KPI Cards */
    .kpi-cards {
        margin-bottom: 25px;
    }

    .kpi-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 120px;
        display: flex;
        align-items: center;
    }

    .kpi-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .kpi-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 20px;
    }

    .kpi-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .kpi-content p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
        display: block;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .chart-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .chart-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }

    .chart-legend {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 15px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin: 0 10px 10px 0;
    }

    .legend-color {
        width: 15px;
        height: 15px;
        border-radius: 3px;
        margin-right: 5px;
    }

    .legend-label {
        font-size: 0.85rem;
        color: #7f8c8d;
    }

    /* Insights Section */
    .insights-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .insights-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .insights-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .insight-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #3498db;
    }

    .insight-card.positive {
        border-left-color: #27ae60;
    }

    .insight-card.negative {
        border-left-color: #e74c3c;
    }

    .insight-card.neutral {
        border-left-color: #f39c12;
    }

    .insight-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .insight-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: white;
        margin-right: 10px;
    }

    .insight-icon.positive {
        background: #27ae60;
    }

    .insight-icon.negative {
        background: #e74c3c;
    }

    .insight-icon.neutral {
        background: #f39c12;
    }

    .insight-title {
        font-weight: 600;
        color: #2c3e50;
        font-size: 1rem;
    }

    .insight-content {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .analytics-title {
            font-size: 1.5rem;
        }
        
        .kpi-card {
            height: auto;
            padding: 15px;
        }
        
        .kpi-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        
        .kpi-content h3 {
            font-size: 1.4rem;
        }
        
        .chart-container {
            height: 250px;
        }
    }
</style>

<div class="analytics-dashboard">
    <div class="dashboard-container">
        <!-- Header -->
        <div class="analytics-header">
            <h1 class="analytics-title">
                <i class="fas fa-chart-bar mr-3"></i>Advanced Financial Analytics
            </h1>
            <p class="analytics-subtitle">Comprehensive financial performance analysis and insights</p>
        </div>

        <!-- KPI Cards -->
        <div class="row kpi-cards">
            <div class="col-lg-3 col-md-6">
                <div class="kpi-card">
                    <div class="kpi-icon" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="kpi-content">
                        <h3 id="revenueGrowth">+15.2%</h3>
                        <p>Revenue Growth</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="kpi-card">
                    <div class="kpi-icon" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="kpi-content">
                        <h3 id="profitMargin">22.8%</h3>
                        <p>Profit Margin</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="kpi-card">
                    <div class="kpi-icon" style="background: linear-gradient(135deg, #f39c12, #f1c40f);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="kpi-content">
                        <h3 id="avgCollectionTime">18 days</h3>
                        <p>Avg Collection Time</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="kpi-card">
                    <div class="kpi-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="kpi-content">
                        <h3 id="customerRetention">87.5%</h3>
                        <p>Customer Retention</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-sliders-h"></i>Analytics Filters
            </div>
            <form id="analyticsFilters">
                <div class="row">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Time Period</label>
                            <select class="form-select" id="timePeriod">
                                <option value="7">Last 7 Days</option>
                                <option value="30">Last 30 Days</option>
                                <option value="90" selected>Last 90 Days</option>
                                <option value="365">Last Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Analysis Type</label>
                            <select class="form-select" id="analysisType">
                                <option value="revenue">Revenue Analysis</option>
                                <option value="profitability">Profitability</option>
                                <option value="cashflow">Cash Flow</option>
                                <option value="customer">Customer Analytics</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Division</label>
                            <select class="form-select" id="divisionFilter">
                                <option value="all">All Divisions</option>
                                <option value="karachi">Karachi</option>
                                <option value="lahore">Lahore</option>
                                <option value="islamabad">Islamabad</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-filter" onclick="updateAnalytics()">
                                    <i class="fas fa-sync-alt"></i> Update Analytics
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-lg-8">
                <div class="chart-section">
                    <div class="chart-title">
                        <div>
                            <i class="fas fa-chart-area"></i>Revenue & Profit Trends
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="exportChart('revenue')">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="revenueProfitChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="chart-section">
                    <div class="chart-title">
                        <div>
                            <i class="fas fa-chart-pie"></i>Revenue by Division
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="divisionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <div>
                            <i class="fas fa-chart-bar"></i>Cash Flow Analysis
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="cashFlowChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <div>
                            <i class="fas fa-chart-line"></i>Customer Acquisition vs Retention
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="customerChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI-Powered Insights -->
        <div class="insights-section">
            <div class="insights-title">
                <i class="fas fa-lightbulb"></i>AI-Powered Financial Insights
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <div class="insight-card positive">
                        <div class="insight-header">
                            <div class="insight-icon positive">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="insight-title">Revenue Growth Opportunity</div>
                        </div>
                        <p class="insight-content">
                            Your Karachi division shows 23% higher profit margins than other divisions.
                            Consider expanding operations or replicating successful strategies from this division.
                        </p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="insight-card neutral">
                        <div class="insight-header">
                            <div class="insight-icon neutral">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="insight-title">Collection Time Optimization</div>
                        </div>
                        <p class="insight-content">
                            Average collection time has increased by 3 days this month.
                            Implementing automated payment reminders could improve cash flow by 15%.
                        </p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <div class="insight-card negative">
                        <div class="insight-header">
                            <div class="insight-icon negative">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="insight-title">Customer Retention Alert</div>
                        </div>
                        <p class="insight-content">
                            3 high-value customers haven't placed orders in 45+ days.
                            Proactive outreach could prevent potential revenue loss of ₹2.5L.
                        </p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="insight-card positive">
                        <div class="insight-header">
                            <div class="insight-icon positive">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="insight-title">Seasonal Trend Detected</div>
                        </div>
                        <p class="insight-content">
                            Historical data shows 18% revenue increase in Q4.
                            Prepare inventory and staffing to capitalize on this seasonal opportunity.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize charts and functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load initial data
    loadAnalyticsData();
    initializeCharts();
});

function loadAnalyticsData() {
    // Simulate loading data - replace with actual API call
    document.getElementById('revenueGrowth').textContent = '+15.2%';
    document.getElementById('profitMargin').textContent = '22.8%';
    document.getElementById('avgCollectionTime').textContent = '18 days';
    document.getElementById('customerRetention').textContent = '87.5%';
}

function initializeCharts() {
    // Revenue & Profit Trends Chart
    const revenueProfitCtx = document.getElementById('revenueProfitChart').getContext('2d');
    new Chart(revenueProfitCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
            datasets: [{
                label: 'Revenue',
                data: [],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Profit',
                data: [],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });

    // Division Revenue Chart
    const divisionCtx = document.getElementById('divisionChart').getContext('2d');
    new Chart(divisionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Karachi', 'Lahore', 'Islamabad', 'Others'],
            datasets: [{
                data: [45, 30, 20, 5],
                backgroundColor: [
                    '#3498db',
                    '#27ae60',
                    '#f39c12',
                    '#e74c3c'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Cash Flow Chart
    const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
    new Chart(cashFlowCtx, {
        type: 'bar',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [{
                label: 'Cash Inflow',
                data: [180000, 220000, 195000, 240000],
                backgroundColor: '#27ae60'
            }, {
                label: 'Cash Outflow',
                data: [150000, 180000, 165000, 190000],
                backgroundColor: '#e74c3c'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });

    // Customer Acquisition vs Retention Chart
    const customerCtx = document.getElementById('customerChart').getContext('2d');
    new Chart(customerCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'New Customers',
                data: [12, 15, 18, 14, 20, 16],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4
            }, {
                label: 'Retained Customers',
                data: [85, 87, 89, 86, 91, 88],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

function updateAnalytics() {
    const timePeriod = document.getElementById('timePeriod').value;
    const analysisType = document.getElementById('analysisType').value;
    const division = document.getElementById('divisionFilter').value;

    console.log('Updating analytics:', { timePeriod, analysisType, division });

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
    btn.disabled = true;

    // Simulate analytics update
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Analytics updated successfully!');
    }, 2000);
}

function exportChart(chartType) {
    alert(`Exporting ${chartType} chart...`);
}
</script>

{% endblock %}
