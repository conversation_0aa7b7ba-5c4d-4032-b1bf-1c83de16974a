"""
Test bulk tracking functionality with sample numbers
"""

import requests
import json
import time
import asyncio

def test_bulk_api():
    """Test bulk tracking using the API"""
    
    # Read sample tracking numbers
    try:
        with open('tcs/sample.txt', 'r') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print("❌ sample.txt not found")
        return
    
    # Extract valid tracking numbers
    tracking_numbers = []
    for line in lines:
        line = line.strip()
        if line.isdigit() and len(line) >= 8:
            tracking_numbers.append(line)
    
    # Limit to first 5 for testing
    test_numbers = tracking_numbers[:5]
    
    print("🚀 BULK TRACKING TEST")
    print("=" * 50)
    print(f"📦 Testing {len(test_numbers)} tracking numbers:")
    for i, num in enumerate(test_numbers, 1):
        print(f"   {i}. {num}")
    
    print(f"\n🌐 API Endpoint: http://localhost:5000/api/track-tcs-public")
    print("=" * 50)
    
    results = []
    successful = 0
    failed = 0
    
    for i, tracking_number in enumerate(test_numbers, 1):
        print(f"\n📦 Processing {i}/{len(test_numbers)}: {tracking_number}")
        
        try:
            start_time = time.time()
            
            response = requests.post(
                'http://localhost:5000/api/track-tcs-public',
                json={'tracking_number': tracking_number},
                timeout=30
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    successful += 1
                    tracking_data = data.get('data', {})
                    
                    print(f"   ✅ SUCCESS ({duration:.1f}s)")
                    print(f"      Status: {tracking_data.get('current_status', 'Unknown')}")
                    print(f"      Route: {tracking_data.get('origin', 'N/A')} → {tracking_data.get('destination', 'N/A')}")
                    print(f"      History: {len(tracking_data.get('track_history', []))} entries")
                    
                    results.append({
                        'tracking_number': tracking_number,
                        'success': True,
                        'data': tracking_data,
                        'duration': duration
                    })
                else:
                    failed += 1
                    error = data.get('error', 'Unknown error')
                    print(f"   ❌ FAILED ({duration:.1f}s)")
                    print(f"      Error: {error}")
                    
                    results.append({
                        'tracking_number': tracking_number,
                        'success': False,
                        'error': error,
                        'duration': duration
                    })
            else:
                failed += 1
                print(f"   ❌ HTTP ERROR ({duration:.1f}s)")
                print(f"      Status: {response.status_code}")
                
                results.append({
                    'tracking_number': tracking_number,
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'duration': duration
                })
                
        except Exception as e:
            failed += 1
            print(f"   💥 EXCEPTION: {e}")
            
            results.append({
                'tracking_number': tracking_number,
                'success': False,
                'error': str(e),
                'duration': 0
            })
        
        # Add delay between requests
        if i < len(test_numbers):
            print(f"   ⏳ Waiting 2 seconds...")
            time.sleep(2)
    
    # Summary
    print(f"\n{'=' * 50}")
    print(f"🏁 BULK TRACKING COMPLETE")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {(successful/len(test_numbers)*100):.1f}%")
    
    # Average response time for successful requests
    successful_durations = [r['duration'] for r in results if r['success']]
    if successful_durations:
        avg_duration = sum(successful_durations) / len(successful_durations)
        print(f"⏱️  Average Response Time: {avg_duration:.1f}s")
    
    # Save results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    results_file = f"bulk_test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'total_tested': len(test_numbers),
            'successful': successful,
            'failed': failed,
            'success_rate': (successful/len(test_numbers)*100),
            'results': results
        }, f, indent=2)
    
    print(f"💾 Results saved to: {results_file}")
    
    return results

def create_bulk_test_html():
    """Create an HTML file for testing bulk functionality"""
    
    # Read sample numbers
    try:
        with open('tcs/sample.txt', 'r') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print("❌ sample.txt not found")
        return
    
    tracking_numbers = []
    for line in lines:
        line = line.strip()
        if line.isdigit() and len(line) >= 8:
            tracking_numbers.append(line)
    
    # Create bulk test data
    bulk_data = '\n'.join(tracking_numbers[:10])  # First 10 numbers
    
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>TCS Bulk Tracking Test</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ max-width: 800px; margin: 0 auto; }}
        textarea {{ width: 100%; height: 200px; padding: 10px; }}
        button {{ background: #D40511; color: white; padding: 10px 20px; border: none; cursor: pointer; }}
        .results {{ margin-top: 20px; }}
        .result-item {{ background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>TCS Bulk Tracking Test</h1>
        <p>Test the bulk tracking functionality with sample tracking numbers:</p>
        
        <textarea id="bulkNumbers" placeholder="Enter tracking numbers (one per line)">{bulk_data}</textarea>
        <br><br>
        <button onclick="testBulkTracking()">Test Bulk Tracking</button>
        
        <div id="results" class="results"></div>
    </div>
    
    <script>
        async function testBulkTracking() {{
            const textarea = document.getElementById('bulkNumbers');
            const resultsDiv = document.getElementById('results');
            
            const numbers = textarea.value.split('\\n').filter(n => n.trim().length > 0);
            
            resultsDiv.innerHTML = '<h3>Processing ' + numbers.length + ' tracking numbers...</h3>';
            
            let successful = 0;
            let failed = 0;
            
            for (let i = 0; i < numbers.length; i++) {{
                const number = numbers[i].trim();
                resultsDiv.innerHTML += '<div class="result-item">Processing ' + number + '...</div>';
                
                try {{
                    const response = await fetch('http://localhost:5000/api/track-tcs-public', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }},
                        body: JSON.stringify({{ tracking_number: number }})
                    }});
                    
                    const data = await response.json();
                    
                    if (data.success) {{
                        successful++;
                        const trackingData = data.data;
                        resultsDiv.innerHTML += '<div class="result-item" style="background: #d4edda;">✅ ' + number + ': ' + trackingData.current_status + ' (' + trackingData.origin + ' → ' + trackingData.destination + ')</div>';
                    }} else {{
                        failed++;
                        resultsDiv.innerHTML += '<div class="result-item" style="background: #f8d7da;">❌ ' + number + ': ' + data.error + '</div>';
                    }}
                }} catch (error) {{
                    failed++;
                    resultsDiv.innerHTML += '<div class="result-item" style="background: #f8d7da;">💥 ' + number + ': ' + error.message + '</div>';
                }}
                
                // Add delay
                await new Promise(resolve => setTimeout(resolve, 1000));
            }}
            
            resultsDiv.innerHTML += '<h3>Complete! ✅ ' + successful + ' successful, ❌ ' + failed + ' failed</h3>';
        }}
    </script>
</body>
</html>
    """
    
    with open('bulk_test.html', 'w') as f:
        f.write(html_content)
    
    print("📄 Created bulk_test.html for browser testing")
    print("🌐 Open bulk_test.html in your browser to test bulk functionality")

if __name__ == "__main__":
    print("🧪 TCS BULK FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test API
    results = test_bulk_api()
    
    # Create HTML test file
    print(f"\n📄 Creating HTML test file...")
    create_bulk_test_html()
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Open bulk_test.html in your browser")
    print(f"   2. Click 'Test Bulk Tracking' to test browser functionality")
    print(f"   3. Compare results with API test above")
