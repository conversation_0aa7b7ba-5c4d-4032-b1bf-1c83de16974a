{% extends "base.html" %}

{% block title %}TCS Express Tracking{% endblock %}

{% block styles %}
<style>
    .tracking-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .search-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 20px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .search-form {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0;
        max-width: 500px;
        margin: 0 auto;
    }
    
    .search-input {
        padding: 15px 20px;
        border: none;
        border-radius: 8px 0 0 8px;
        font-size: 16px;
        width: 300px;
        outline: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .search-btn {
        background: #D40511;
        color: white;
        border: none;
        padding: 15px 25px;
        border-radius: 0 8px 8px 0;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .search-btn:hover {
        background: #b8040f;
        transform: translateY(-2px);
    }
    
    .results-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
        display: none;
    }
    
    .shipment-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .detail-card {
        background: #f8f8f8;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #D40511;
    }
    
    .detail-card h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: bold;
    }
    
    .detail-item {
        display: flex;
        margin-bottom: 10px;
    }
    
    .detail-label {
        color: #666;
        min-width: 150px;
        font-weight: 500;
    }
    
    .detail-value {
        color: #333;
        font-weight: bold;
    }
    
    .tracking-number {
        color: #D40511 !important;
        font-size: 18px;
    }
    
    .status-delivered {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-summary {
        background: #f8f8f8;
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;
        text-align: center;
    }
    
    .track-history {
        margin-top: 30px;
    }
    
    .track-history h2 {
        text-align: center;
        color: #333;
        margin-bottom: 20px;
        font-size: 24px;
    }
    
    .history-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        overflow: hidden;
    }
    
    .history-table th {
        background: #f1f1f1;
        padding: 15px;
        text-align: left;
        font-weight: bold;
        color: #333;
    }
    
    .history-table td {
        padding: 15px;
        border-bottom: 1px solid #eee;
    }
    
    .history-table tr:hover {
        background: #f9f9f9;
    }
    
    .loading {
        text-align: center;
        padding: 40px;
        color: #666;
    }
    
    .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        border: 1px solid #f5c6cb;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #D40511;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .shipment-details {
            grid-template-columns: 1fr;
        }
        
        .search-form {
            flex-direction: column;
            gap: 10px;
        }
        
        .search-input {
            border-radius: 8px;
            width: 100%;
            max-width: 300px;
        }
        
        .search-btn {
            border-radius: 8px;
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="tracking-container">
    <div class="search-section">
        <h1><i class="fas fa-truck"></i> TCS Express Tracking</h1>
        <p>Enter your tracking number to get real-time shipment information</p>
        
        <form class="search-form" id="trackingForm">
            <input type="number" 
                   class="search-input" 
                   id="trackingNumber" 
                   placeholder="Enter Tracking Number" 
                   required>
            <button type="submit" class="search-btn">
                <i class="fas fa-search"></i> Track Shipment
            </button>
        </form>
    </div>
    
    <div class="loading" id="loadingSection" style="display: none;">
        <div class="spinner"></div>
        <p>Fetching tracking information...</p>
    </div>
    
    <div class="error" id="errorSection" style="display: none;">
        <i class="fas fa-exclamation-triangle"></i>
        <span id="errorMessage"></span>
    </div>
    
    <div class="results-section" id="resultsSection">
        <div class="shipment-details" id="shipmentDetails">
            <!-- Shipment details will be populated here -->
        </div>
        
        <div class="status-summary" id="statusSummary">
            <!-- Status summary will be populated here -->
        </div>
        
        <div class="track-history">
            <h2>Track History</h2>
            <table class="history-table" id="historyTable">
                <thead>
                    <tr>
                        <th>Date Time</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    <!-- History will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
document.getElementById('trackingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const trackingNumber = document.getElementById('trackingNumber').value;
    if (!trackingNumber) {
        showError('Please enter a tracking number');
        return;
    }
    
    trackShipment(trackingNumber);
});

function showLoading() {
    document.getElementById('loadingSection').style.display = 'block';
    document.getElementById('resultsSection').style.display = 'none';
    document.getElementById('errorSection').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingSection').style.display = 'none';
}

function showError(message) {
    hideLoading();
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorSection').style.display = 'block';
    document.getElementById('resultsSection').style.display = 'none';
}

function showResults(data) {
    hideLoading();
    document.getElementById('errorSection').style.display = 'none';
    
    // Populate shipment details
    populateShipmentDetails(data);
    
    // Populate status summary
    populateStatusSummary(data);
    
    // Populate track history
    populateTrackHistory(data.track_history);
    
    document.getElementById('resultsSection').style.display = 'block';
}

function populateShipmentDetails(data) {
    const detailsContainer = document.getElementById('shipmentDetails');
    detailsContainer.innerHTML = `
        <div class="detail-card">
            <h3>Shipment Booking Details</h3>
            <div class="detail-item">
                <span class="detail-label">Tracking Number:</span>
                <span class="detail-value tracking-number">${data.tracking_number}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Agent Reference:</span>
                <span class="detail-value">${data.agent_reference || 'NA'}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Origin:</span>
                <span class="detail-value">${data.origin}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Destination:</span>
                <span class="detail-value">${data.destination}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Booking Date:</span>
                <span class="detail-value">${data.booking_date}</span>
            </div>
        </div>
        
        <div class="detail-card">
            <h3>Shipment Track Summary</h3>
            <div class="detail-item">
                <span class="detail-label">Current Status:</span>
                <span class="detail-value status-delivered">${data.current_status}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Delivered On:</span>
                <span class="detail-value">${data.delivered_on || 'N/A'}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Received by:</span>
                <span class="detail-value">${data.received_by || 'N/A'}</span>
            </div>
        </div>
    `;
}

function populateStatusSummary(data) {
    const summaryContainer = document.getElementById('statusSummary');
    summaryContainer.innerHTML = `
        <div><strong>Current Status:</strong> ${data.current_status}</div>
        <div><strong>Delivered On:</strong> ${data.delivered_on || 'N/A'}</div>
        <div><strong>Signed By:</strong> ${data.received_by || 'N/A'}</div>
    `;
}

function populateTrackHistory(history) {
    const tableBody = document.getElementById('historyTableBody');
    tableBody.innerHTML = '';
    
    history.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.date_time}</td>
            <td><strong>${item.status}</strong><br>${item.location || ''}</td>
        `;
        tableBody.appendChild(row);
    });
}

function trackShipment(trackingNumber) {
    showLoading();
    
    fetch('/api/track-tcs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tracking_number: trackingNumber })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResults(data.data);
        } else {
            showError(data.error || 'Failed to fetch tracking information');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('Network error. Please try again.');
    });
}
</script>
{% endblock %}
