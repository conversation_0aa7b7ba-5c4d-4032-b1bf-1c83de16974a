{% extends 'base.html' %}

{% block title %}Rider Registration - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .registration-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .registration-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .card-header h2 {
        margin: 0;
        font-weight: 600;
        font-size: 2rem;
    }
    
    .card-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
    }
    
    .form-section {
        padding: 30px;
    }
    
    .section-title {
        color: #007bff;
        font-weight: 600;
        font-size: 1.3rem;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-right: 10px;
        font-size: 1.5rem;
    }
    
    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .file-upload-area {
        border: 2px dashed #007bff;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        background: #f8f9ff;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .file-upload-area:hover {
        background: #e3f2fd;
        border-color: #0056b3;
    }
    
    .file-upload-area i {
        font-size: 3rem;
        color: #007bff;
        margin-bottom: 15px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 10px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,123,255,0.4);
    }
    
    .btn-secondary {
        border-radius: 10px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .progress-steps {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }
    
    .step {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin: 0 10px;
        position: relative;
    }
    
    .step.active {
        background: #007bff;
        color: white;
    }
    
    .step.completed {
        background: #28a745;
        color: white;
    }
    
    .step::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 100%;
        width: 20px;
        height: 2px;
        background: #e9ecef;
        transform: translateY(-50%);
    }
    
    .step:last-child::after {
        display: none;
    }
    
    .step.completed::after {
        background: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="registration-container">
    <div class="container">
        <div class="registration-card">
            <div class="card-header">
                <h2><i class="fas fa-motorcycle"></i> Rider Registration</h2>
                <p>Join our delivery team and start earning today!</p>
            </div>
            
            <div class="form-section">
                <!-- Progress Steps -->
                <div class="progress-steps">
                    <div class="step active">1</div>
                    <div class="step">2</div>
                    <div class="step">3</div>
                    <div class="step">4</div>
                </div>
                
                <form method="POST" action="{{ url_for('register_rider') }}" enctype="multipart/form-data" id="riderRegistrationForm">
                    <!-- Personal Information -->
                    <div class="form-step active" id="step1">
                        <div class="section-title">
                            <i class="fas fa-user"></i>
                            Personal Information
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name">Full Name <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" placeholder="Muhammad Ali" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email Address <span class="required">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">Phone Number <span class="required">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" placeholder="+92-300-1234567" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="current_location">Current Location</label>
                                    <input type="text" class="form-control" id="current_location" name="current_location" placeholder="Karachi">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_type">Vehicle Type</label>
                                    <select class="form-control" id="vehicle_type" name="vehicle_type">
                                        <option value="Motorcycle">Motorcycle</option>
                                        <option value="Car">Car</option>
                                        <option value="Van">Van</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Address Information -->
                    <div class="form-step" id="step2">
                        <div class="section-title">
                            <i class="fas fa-map-marker-alt"></i>
                            Address Information
                        </div>
                        
                        <div class="form-group">
                            <label for="address">Complete Address <span class="required">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" placeholder="House/Flat number, Street, Area" required></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="city">City <span class="required">*</span></label>
                                    <select class="form-control" id="city" name="city" required>
                                        <option value="">Select City</option>
                                        <option value="Karachi">Karachi</option>
                                        <option value="Lahore">Lahore</option>
                                        <option value="Islamabad">Islamabad</option>
                                        <option value="Rawalpindi">Rawalpindi</option>
                                        <option value="Faisalabad">Faisalabad</option>
                                        <option value="Multan">Multan</option>
                                        <option value="Peshawar">Peshawar</option>
                                        <option value="Quetta">Quetta</option>
                                        <option value="Sialkot">Sialkot</option>
                                        <option value="Gujranwala">Gujranwala</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="postal_code">Postal Code</label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code" placeholder="75300">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="emergency_contact_name">Emergency Contact Name <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="emergency_contact_phone">Emergency Contact Phone <span class="required">*</span></label>
                                    <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- License Information -->
                    <div class="form-step" id="step3">
                        <div class="section-title">
                            <i class="fas fa-id-card"></i>
                            License Information
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="license_number">Driving License Number <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="license_number" name="license_number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="license_expiry">License Expiry Date <span class="required">*</span></label>
                                    <input type="date" class="form-control" id="license_expiry" name="license_expiry" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Buttons -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeStep(-1)" style="display: none;">
                                <i class="fas fa-arrow-left"></i> Previous
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                            <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                <i class="fas fa-check"></i> Register Rider
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentStep = 1;
const totalSteps = 3;

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.form-step').forEach(s => s.style.display = 'none');
    
    // Show current step
    document.getElementById(`step${step}`).style.display = 'block';
    
    // Update progress steps
    document.querySelectorAll('.step').forEach((s, index) => {
        s.classList.remove('active', 'completed');
        if (index + 1 < step) {
            s.classList.add('completed');
        } else if (index + 1 === step) {
            s.classList.add('active');
        }
    });
    
    // Update buttons
    document.getElementById('prevBtn').style.display = step === 1 ? 'none' : 'inline-block';
    document.getElementById('nextBtn').style.display = step === totalSteps ? 'none' : 'inline-block';
    document.getElementById('submitBtn').style.display = step === totalSteps ? 'inline-block' : 'none';
}

function changeStep(direction) {
    const newStep = currentStep + direction;
    
    if (newStep >= 1 && newStep <= totalSteps) {
        // Validate current step before moving
        if (direction === 1 && !validateStep(currentStep)) {
            return;
        }
        
        currentStep = newStep;
        showStep(currentStep);
    }
}

function validateStep(step) {
    const stepElement = document.getElementById(`step${step}`);
    const requiredFields = stepElement.querySelectorAll('[required]');
    
    for (let field of requiredFields) {
        if (!field.value.trim()) {
            field.focus();
            field.classList.add('is-invalid');
            return false;
        }
        field.classList.remove('is-invalid');
    }
    
    return true;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    showStep(1);
    
    // Add input validation
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });
});
</script>
{% endblock %}
