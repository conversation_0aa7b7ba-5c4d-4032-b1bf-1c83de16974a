"""
Permission Groups for Medivent Pharmaceuticals Web Portal
This module defines logical groupings of permissions for easier management
"""

# Define permission groups with descriptions
PERMISSION_GROUPS = {
    "dashboard": {
        "name": "Dashboard",
        "description": "Dashboard access and widgets",
        "permissions": [
            {
                "code": "dashboard_view",
                "name": "View Dashboard",
                "description": "Access to view the main dashboard"
            },
            {
                "code": "dashboard_orders_widget",
                "name": "Orders Widget",
                "description": "View the orders statistics widget on dashboard"
            },
            {
                "code": "dashboard_inventory_widget",
                "name": "Inventory Widget",
                "description": "View the inventory statistics widget on dashboard"
            },
            {
                "code": "dashboard_sales_widget",
                "name": "Sales Widget",
                "description": "View the sales statistics widget on dashboard"
            },
            {
                "code": "dashboard_workflow_widget",
                "name": "Workflow Widget",
                "description": "View the order workflow widget on dashboard"
            }
        ]
    },
    "orders": {
        "name": "Order Management",
        "description": "Order creation, viewing, and processing",
        "permissions": [
            {
                "code": "order_view",
                "name": "View Orders",
                "description": "View order list and details"
            },
            {
                "code": "order_create",
                "name": "Create Orders",
                "description": "Create new orders"
            },
            {
                "code": "order_edit",
                "name": "Edit Orders",
                "description": "Edit existing orders"
            },
            {
                "code": "order_delete",
                "name": "Delete Orders",
                "description": "Delete orders"
            },
            {
                "code": "order_approve",
                "name": "Approve Orders",
                "description": "Approve orders for processing"
            },
            {
                "code": "order_process",
                "name": "Process Orders",
                "description": "Process approved orders"
            },
            {
                "code": "order_dispatch",
                "name": "Dispatch Orders",
                "description": "Mark orders as dispatched"
            },
            {
                "code": "order_deliver",
                "name": "Deliver Orders",
                "description": "Mark orders as delivered"
            }
        ]
    },
    "products": {
        "name": "Product Management",
        "description": "Product creation, editing, and viewing",
        "permissions": [
            {
                "code": "product_view",
                "name": "View Products",
                "description": "View product list and details"
            },
            {
                "code": "product_create",
                "name": "Create Products",
                "description": "Add new products"
            },
            {
                "code": "product_edit",
                "name": "Edit Products",
                "description": "Edit existing products"
            },
            {
                "code": "product_delete",
                "name": "Delete Products",
                "description": "Delete products"
            },
            {
                "code": "product_activate",
                "name": "Activate/Deactivate Products",
                "description": "Toggle product active status"
            }
        ]
    },
    "inventory": {
        "name": "Inventory Management",
        "description": "Stock management and tracking",
        "permissions": [
            {
                "code": "inventory_view",
                "name": "View Inventory",
                "description": "View inventory levels and details"
            },
            {
                "code": "inventory_add",
                "name": "Add Stock",
                "description": "Add new stock to inventory"
            },
            {
                "code": "inventory_adjust",
                "name": "Adjust Inventory",
                "description": "Make inventory adjustments"
            },
            {
                "code": "inventory_transfer",
                "name": "Transfer Stock",
                "description": "Transfer stock between locations"
            },
            {
                "code": "batch_manage",
                "name": "Manage Batches",
                "description": "Manage product batches"
            }
        ]
    },
    "reports": {
        "name": "Reports",
        "description": "Reporting and analytics",
        "permissions": [
            {
                "code": "report_view",
                "name": "View Reports",
                "description": "Access to view reports"
            },
            {
                "code": "report_export",
                "name": "Export Reports",
                "description": "Export reports to Excel/PDF"
            },
            {
                "code": "report_sales",
                "name": "Sales Reports",
                "description": "Access to sales reports"
            },
            {
                "code": "report_inventory",
                "name": "Inventory Reports",
                "description": "Access to inventory reports"
            },
            {
                "code": "report_orders",
                "name": "Order Reports",
                "description": "Access to order reports"
            }
        ]
    },
    "documents": {
        "name": "Documents",
        "description": "Invoices, challans, and other documents",
        "permissions": [
            {
                "code": "invoice_generate",
                "name": "Generate Invoices",
                "description": "Create new invoices"
            },
            {
                "code": "invoice_view",
                "name": "View Invoices",
                "description": "View existing invoices"
            },
            {
                "code": "challan_generate",
                "name": "Generate Challans",
                "description": "Create new delivery challans"
            },
            {
                "code": "challan_view",
                "name": "View Challans",
                "description": "View existing delivery challans"
            }
        ]
    },
    "users": {
        "name": "User Management",
        "description": "User and role management",
        "permissions": [
            {
                "code": "user_view",
                "name": "View Users",
                "description": "View user list and details"
            },
            {
                "code": "user_create",
                "name": "Create Users",
                "description": "Create new users"
            },
            {
                "code": "user_edit",
                "name": "Edit Users",
                "description": "Edit existing users"
            },
            {
                "code": "user_delete",
                "name": "Delete Users",
                "description": "Delete users"
            },
            {
                "code": "role_manage",
                "name": "Manage Roles",
                "description": "Create and edit roles"
            },
            {
                "code": "permission_manage",
                "name": "Manage Permissions",
                "description": "Assign permissions to roles"
            }
        ]
    },
    "system": {
        "name": "System",
        "description": "System settings and maintenance",
        "permissions": [
            {
                "code": "settings_view",
                "name": "View Settings",
                "description": "View system settings"
            },
            {
                "code": "settings_edit",
                "name": "Edit Settings",
                "description": "Edit system settings"
            },
            {
                "code": "backup_create",
                "name": "Create Backups",
                "description": "Create system backups"
            },
            {
                "code": "backup_restore",
                "name": "Restore Backups",
                "description": "Restore from backups"
            },
            {
                "code": "logs_view",
                "name": "View Logs",
                "description": "View system logs"
            },
            {
                "code": "data_import",
                "name": "Import Data",
                "description": "Import data into the system"
            }
        ]
    },
    "menu": {
        "name": "Menu Visibility",
        "description": "Control which menu items are visible",
        "permissions": [
            {
                "code": "menu_dashboard",
                "name": "Dashboard Menu",
                "description": "Show Dashboard menu item"
            },
            {
                "code": "menu_orders",
                "name": "Orders Menu",
                "description": "Show Orders menu item"
            },
            {
                "code": "menu_products",
                "name": "Products Menu",
                "description": "Show Products menu item"
            },
            {
                "code": "menu_inventory",
                "name": "Inventory Menu",
                "description": "Show Inventory menu item"
            },
            {
                "code": "menu_warehouse",
                "name": "Warehouse Menu",
                "description": "Show Warehouse menu item"
            },
            {
                "code": "menu_reports",
                "name": "Reports Menu",
                "description": "Show Reports menu item"
            },
            {
                "code": "menu_users",
                "name": "Users Menu",
                "description": "Show Users menu item"
            },
            {
                "code": "menu_settings",
                "name": "Settings Menu",
                "description": "Show Settings menu item"
            }
        ]
    }
}

# Define role templates with predefined permission sets
ROLE_TEMPLATES = {
    "admin": {
        "name": "Administrator",
        "description": "Full system access",
        "permissions": [perm["code"] for group in PERMISSION_GROUPS.values() for perm in group["permissions"]]
    },
    "manager": {
        "name": "Manager",
        "description": "Manage operations but not system settings",
        "permissions": [
            # Dashboard
            "dashboard_view", "dashboard_orders_widget", "dashboard_inventory_widget", 
            "dashboard_sales_widget", "dashboard_workflow_widget",
            
            # Orders
            "order_view", "order_create", "order_edit", "order_approve", 
            "order_process", "order_dispatch",
            
            # Products
            "product_view", "product_create", "product_edit", "product_activate",
            
            # Inventory
            "inventory_view", "inventory_add", "inventory_adjust", "inventory_transfer", "batch_manage",
            
            # Reports
            "report_view", "report_export", "report_sales", "report_inventory", "report_orders",
            
            # Documents
            "invoice_generate", "invoice_view", "challan_generate", "challan_view",
            
            # Menu Visibility
            "menu_dashboard", "menu_orders", "menu_products", "menu_inventory",
            "menu_warehouse", "menu_reports"
        ]
    },
    "sales": {
        "name": "Sales Representative",
        "description": "Create and view orders",
        "permissions": [
            # Dashboard
            "dashboard_view", "dashboard_orders_widget", "dashboard_sales_widget",
            
            # Orders
            "order_view", "order_create",
            
            # Products
            "product_view",
            
            # Documents
            "invoice_view", "challan_view",
            
            # Menu Visibility
            "menu_dashboard", "menu_orders", "menu_products"
        ]
    },
    "warehouse": {
        "name": "Warehouse Staff",
        "description": "Manage inventory and process orders",
        "permissions": [
            # Dashboard
            "dashboard_view", "dashboard_inventory_widget", "dashboard_workflow_widget",
            
            # Orders
            "order_view", "order_process", "order_dispatch",
            
            # Products
            "product_view",
            
            # Inventory
            "inventory_view", "inventory_add", "inventory_adjust", "inventory_transfer", "batch_manage",
            
            # Documents
            "challan_view",
            
            # Menu Visibility
            "menu_dashboard", "menu_orders", "menu_products", "menu_inventory", "menu_warehouse"
        ]
    }
}

def get_permission_groups():
    """Get all permission groups"""
    return PERMISSION_GROUPS

def get_role_templates():
    """Get all role templates"""
    return ROLE_TEMPLATES

def get_permissions_by_group(group_code):
    """Get all permissions in a specific group"""
    if group_code in PERMISSION_GROUPS:
        return PERMISSION_GROUPS[group_code]["permissions"]
    return []

def get_template_permissions(template_code):
    """Get all permissions for a specific role template"""
    if template_code in ROLE_TEMPLATES:
        return ROLE_TEMPLATES[template_code]["permissions"]
    return []
