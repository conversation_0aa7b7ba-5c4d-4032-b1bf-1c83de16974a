-- Create divisions table with enhanced fields
CREATE TABLE IF NOT EXISTS divisions (
    division_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    category TEXT,
    manager TEXT NOT NULL,
    status TEXT DEFAULT 'Active',
    budget REAL,
    contact_email TEXT,
    contact_phone TEXT,
    location TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- Add division_id column to products table if it doesn't exist
ALTER TABLE products ADD COLUMN division_id INTEGER REFERENCES divisions(division_id);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_divisions_name ON divisions(name);
CREATE INDEX IF NOT EXISTS idx_divisions_manager ON divisions(manager);
CREATE INDEX IF NOT EXISTS idx_divisions_status ON divisions(status);
CREATE INDEX IF NOT EXISTS idx_products_division_id ON products(division_id);
