BACKUP CREATED: July 9, 2025 - Before Customer & Product Improvements
Original Project Location: C:\Users\<USER>\Desktop\New folder\COMPREHENSIVE_ERP_BACKUP_20250629_015033

ISSUES TO FIX:
1. Customer pricing route not working: /customers?action=set_pricing
2. Export Data button functionality missing
3. Bulk Actions button functionality missing
4. MRP to TP calculation formula incorrect - should be: TP = MRP * 0.15 - MRP = MRP * 0.85
5. Customer form enhancements needed:
   - Add NTN number field (Pakistani tax number)
   - Add customer category dropdown (Institute, Distributor, Direct Customer, Doctor, Private Customer)
   - Add multiple file upload capability
   - Auto-generate unique customer code (C00135 format)
   - File preview functionality
6. Order form enhancements needed:
   - Add file upload for prescriptions/documents
   - Multiple file upload with preview
   - Mandatory PO number field

CURRENT STATUS: All previous fixes completed successfully
- DC Generation Error: FIXED
- Customer Template Error: FIXED  
- Finance Pending Invoices: FIXED
- MRP to TP Auto-calculation: IMPLEMENTED (needs formula correction)
- Stock Fields Removal: COMPLETED
