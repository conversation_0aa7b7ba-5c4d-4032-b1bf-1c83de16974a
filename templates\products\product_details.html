{% extends "base.html" %}

{% block title %}Product Details - {{ product.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Product Header -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-pills"></i> {{ product.name }}
                            </h4>
                            <small>{{ product.product_id }} | {{ product.generic_name or 'No Generic Name' }}</small>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="badge badge-{% if product.is_active != False %}success{% else %}danger{% endif %} badge-lg">
                                {% if product.is_active != False %}Active{% else %}Inactive{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6><strong>Basic Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Product ID:</strong></td>
                                    <td>{{ product.product_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Product Name:</strong></td>
                                    <td>{{ product.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Generic Name:</strong></td>
                                    <td>{{ product.generic_name or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if product.type == 'Tablets' %}primary{% elif product.type == 'Capsules' %}info{% elif product.type == 'Syrup' %}warning{% else %}secondary{% endif %}">
                                            {{ product.type }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Strength:</strong></td>
                                    <td>{{ product.strength or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Unit of Measure:</strong></td>
                                    <td>{{ product.unit_of_measure or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>{{ product.category or 'General' }}</td>
                                </tr>
                                {% if product.division_id %}
                                <tr>
                                    <td><strong>Division:</strong></td>
                                    <td>{{ product.division_id }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        
                        <!-- Pricing & Stock Information -->
                        <div class="col-md-6">
                            <h6><strong>Pricing & Stock Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Cost Price:</strong></td>
                                    <td>₹{{ product.cost_price | format_currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Selling Price:</strong></td>
                                    <td>₹{{ product.selling_price | format_currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Profit Margin:</strong></td>
                                    <td>
                                        {% set margin = ((product.selling_price - product.cost_price) / product.cost_price * 100) if product.cost_price > 0 else 0 %}
                                        <span class="{% if margin > 30 %}text-success{% elif margin > 15 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ "%.1f"|format(margin) }}%
                                        </span>
                                    </td>
                                </tr>
                                {% if product.tax_rate %}
                                <tr>
                                    <td><strong>Tax Rate:</strong></td>
                                    <td>{{ product.tax_rate }}%</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Current Stock:</strong></td>
                                    <td>
                                        {% if total_stock is defined %}
                                            {% if total_stock <= (product.reorder_level or 0) %}
                                            <span class="badge badge-danger">{{ total_stock }}</span>
                                            <small class="text-danger">Low Stock!</small>
                                            {% elif total_stock <= (product.reorder_level or 0) * 2 %}
                                            <span class="badge badge-warning">{{ total_stock }}</span>
                                            <small class="text-warning">Running Low</small>
                                            {% else %}
                                            <span class="badge badge-success">{{ total_stock }}</span>
                                            {% endif %}
                                        {% else %}
                                        <span class="badge badge-secondary">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if product.reorder_level %}
                                <tr>
                                    <td><strong>Reorder Level:</strong></td>
                                    <td>{{ product.reorder_level }}</td>
                                </tr>
                                {% endif %}
                                {% if product.max_stock_level %}
                                <tr>
                                    <td><strong>Max Stock Level:</strong></td>
                                    <td>{{ product.max_stock_level }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    {% if product.manufacturer or product.country or product.supplier_id %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6><strong>Additional Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                {% if product.manufacturer %}
                                <tr>
                                    <td style="width: 200px;"><strong>Manufacturer:</strong></td>
                                    <td>{{ product.manufacturer }}</td>
                                </tr>
                                {% endif %}
                                {% if product.country %}
                                <tr>
                                    <td><strong>Country:</strong></td>
                                    <td>{{ product.country }}</td>
                                </tr>
                                {% endif %}
                                {% if product.supplier_id %}
                                <tr>
                                    <td><strong>Supplier ID:</strong></td>
                                    <td>{{ product.supplier_id }}</td>
                                </tr>
                                {% endif %}
                                {% if product.created_at %}
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ product.created_at | format_datetime }}</td>
                                </tr>
                                {% endif %}
                                {% if product.updated_at %}
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>{{ product.updated_at | format_datetime }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Inventory Details -->
            {% if inventory_items %}
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-warehouse"></i> Inventory Details</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Warehouse</th>
                                    <th>Batch Number</th>
                                    <th>Stock Quantity</th>
                                    <th>Available</th>
                                    <th>Allocated</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inventory_items %}
                                <tr>
                                    <td>{{ item.warehouse_name }}</td>
                                    <td>{{ item.batch_number }}</td>
                                    <td>{{ item.stock_quantity }}</td>
                                    <td>
                                        {% set available = item.stock_quantity - (item.allocated_quantity or 0) %}
                                        <span class="badge badge-{% if available > 0 %}success{% else %}danger{% endif %}">
                                            {{ available }}
                                        </span>
                                    </td>
                                    <td>{{ item.allocated_quantity or 0 }}</td>
                                    <td>
                                        {% if item.expiry_date %}
                                            {% set days_to_expiry = (item.expiry_date | format_datetime('%Y-%m-%d') | strptime('%Y-%m-%d') - now.date()).days %}
                                            <span class="{% if days_to_expiry < 30 %}text-danger{% elif days_to_expiry < 90 %}text-warning{% else %}text-success{% endif %}">
                                                {{ item.expiry_date | format_datetime('%Y-%m-%d') }}
                                            </span>
                                        {% else %}
                                        <span class="text-muted">No Expiry</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if item.status == 'active' %}success{% else %}secondary{% endif %}">
                                            {{ item.status|title }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Recent Sales -->
            {% if recent_sales %}
            <div class="card mb-4">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Recent Sales (Last 30 Days)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Quantity Sold</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td><a href="{{ url_for('view_order', order_id=sale.order_id) }}">{{ sale.order_id }}</a></td>
                                    <td>{{ sale.customer_name }}</td>
                                    <td>{{ sale.quantity }}</td>
                                    <td>₹{{ sale.unit_price | format_currency }}</td>
                                    <td>₹{{ sale.total_price | format_currency }}</td>
                                    <td>{{ sale.order_date | format_datetime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Products
                        </a>
                        <a href="{{ url_for('update_product', product_id=product.product_id) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Product
                        </a>
                        <a href="{{ url_for('inventory_add') }}?product={{ product.product_id }}" class="btn btn-info">
                            <i class="fas fa-plus"></i> Add Stock
                        </a>
                        <a href="{{ url_for('inventory') }}?product={{ product.product_id }}" class="btn btn-warning">
                            <i class="fas fa-warehouse"></i> View All Inventory
                        </a>
                        <a href="{{ url_for('reports') }}?type=product&id={{ product.product_id }}" class="btn btn-success">
                            <i class="fas fa-chart-line"></i> Sales Report
                        </a>
                        {% if product.is_active != False %}
                        <button class="btn btn-outline-danger" onclick="toggleProductStatus('{{ product.product_id }}', false)">
                            <i class="fas fa-pause"></i> Deactivate
                        </button>
                        {% else %}
                        <button class="btn btn-outline-success" onclick="toggleProductStatus('{{ product.product_id }}', true)">
                            <i class="fas fa-play"></i> Activate
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}
</style>

<script>
function toggleProductStatus(productId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    const message = activate ? 'Activate this product?' : 'Deactivate this product?';
    
    if (confirm(message)) {
        fetch(`/products/${productId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ activate: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating product status');
        });
    }
}
</script>
{% endblock %}
