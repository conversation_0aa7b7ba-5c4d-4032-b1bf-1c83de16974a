{% extends 'base.html' %}

{% block title %}DC Pending - Warehouse Management - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <h3 class="mb-0">
                        <i class="fas fa-warehouse"></i> DC Pending Dashboard
                    </h3>
                    <p class="mb-0">Manage delivery challans and inventory allocation</p>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-4">
                            <i class="fas fa-clock fa-3x"></i>
                        </div>
                        <div class="col-8">
                            <h3 class="mb-0">{{ kpi_data.pending_count }}</h3>
                            <p class="mb-0">Pending DCs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-4">
                            <i class="fas fa-check-circle fa-3x"></i>
                        </div>
                        <div class="col-8">
                            <h3 class="mb-0">{{ kpi_data.today_completed }}</h3>
                            <p class="mb-0">Today Completed</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-4">
                            <i class="fas fa-dollar-sign fa-3x"></i>
                        </div>
                        <div class="col-8">
                            <h3 class="mb-0">{{ kpi_data.pending_revenue|format_currency }}</h3>
                            <p class="mb-0">Pending Revenue</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-4">
                            <i class="fas fa-list fa-3x"></i>
                        </div>
                        <div class="col-8">
                            <h3 class="mb-0">{{ kpi_data.total_orders }}</h3>
                            <p class="mb-0">Total Orders</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Orders Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-truck"></i> Approved Orders Waiting for DC Generation
                    </h5>
                </div>
                <div class="card-body">
                    {% if pending_orders %}
                    <div class="table-responsive">
                        <table id="pendingOrdersTable" class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Approval Date</th>
                                    <th>Items</th>
                                    <th>Quantity</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in pending_orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_id }}</strong>
                                        <br><small class="text-muted">{{ order.invoice_number or 'No Invoice' }}</small>
                                    </td>
                                    <td>
                                        {{ order.customer_name }}
                                        <br><small class="text-muted">{{ order.customer_phone or 'No Phone' }}</small>
                                    </td>
                                    <td>{{ order.approval_date|format_datetime }}</td>
                                    <td>
                                        <span class="badge badge-info">{{ order.total_items }} items</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ order.total_quantity }} units</span>
                                    </td>
                                    <td>{{ order.order_amount|format_currency }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('orders.view_order', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-info" title="View Order Details">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{{ url_for('dc_generate', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-success" title="Generate DC">
                                                <i class="fas fa-plus-circle"></i> Generate DC
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                        <h4 class="text-muted">All Caught Up!</h4>
                        <p class="text-muted">No pending DCs at the moment. All approved orders have been processed.</p>
                        <a href="{{ url_for('orders.index') }}" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All Orders
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('orders.index') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-list"></i> All Orders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('warehouses') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-warehouse"></i> Warehouse Dashboard
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('workflow_management') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-project-diagram"></i> Workflow Management
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#pendingOrdersTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[2, "desc"]], // Sort by approval date
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting for actions column
        ]
    });
    
    // Auto-refresh every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
