{% extends "base.html" %}

{% block title %}Invoice Details - {{ invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary mb-0">
                        <i class="fas fa-file-invoice-dollar"></i> Invoice {{ invoice_number }}
                    </h2>
                    <p class="text-muted mb-0">Comprehensive Payment Processing</p>
                </div>
                <div>
                    <a href="{{ url_for('finance', view='payments') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Payments
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Amount</h6>
                            <h4 class="mb-0">₨{{ "{:,.2f}".format(total_invoice_amount) }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-invoice fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Paid Amount</h6>
                            <h4 class="mb-0">₨{{ "{:,.2f}".format(total_paid) }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card {% if outstanding_balance > 0 %}bg-warning{% else %}bg-success{% endif %} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Outstanding</h6>
                            <h4 class="mb-0">₨{{ "{:,.2f}".format(outstanding_balance) }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Payment Status</h6>
                            <h4 class="mb-0">
                                {% if outstanding_balance <= 0 %}
                                    Paid
                                {% elif total_paid > 0 %}
                                    Partial
                                {% else %}
                                    Unpaid
                                {% endif %}
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-pie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column: Invoice & Customer Details -->
        <div class="col-lg-8">
            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> {{ customer_name }}</p>
                            {% if customer and customer.phone %}
                            <p><strong>Phone:</strong> {{ customer.phone }}</p>
                            {% endif %}
                            {% if customer and customer.address %}
                            <p><strong>Address:</strong> {{ customer.address }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if customer %}
                            <p><strong>Customer ID:</strong> {{ customer.customer_id }}</p>
                            <p><strong>Customer Type:</strong> {{ customer.customer_type or 'Regular' }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Entries (Multiple Products) -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Invoice Breakdown ({{ invoice_entries|length }} entries)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Status</th>
                                    <th>Sales Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in invoice_entries %}
                                <tr>
                                    <td>{{ entry.order_id }}</td>
                                    <td>{{ entry.order_date|format_datetime('%Y-%m-%d') if entry.order_date else 'N/A' }}</td>
                                    <td class="text-end">₨{{ "{:,.2f}".format(entry.order_amount) }}</td>
                                    <td>{{ entry.payment_method }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if entry.order_status == 'Delivered' else 'warning' }}">
                                            {{ entry.order_status }}
                                        </span>
                                    </td>
                                    <td>{{ entry.sales_agent or 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <th colspan="2">Total</th>
                                    <th class="text-end">₨{{ "{:,.2f}".format(total_invoice_amount) }}</th>
                                    <th colspan="3"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Payment History ({{ payments|length }} payments)</h5>
                    {% if outstanding_balance > 0 %}
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#recordPaymentModal">
                        <i class="fas fa-plus"></i> Record Payment
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Payment ID</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Reference</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_id }}</td>
                                    <td>{{ payment.payment_date|format_datetime('%Y-%m-%d %H:%M') if payment.payment_date else 'N/A' }}</td>
                                    <td class="text-end text-success">₨{{ "{:,.2f}".format(payment.amount) }}</td>
                                    <td>{{ payment.payment_method }}</td>
                                    <td>{{ payment.reference_number or 'N/A' }}</td>
                                    <td>{{ payment.notes or 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-credit-card fa-3x mb-3"></i>
                        <p>No payments recorded yet</p>
                        {% if outstanding_balance > 0 %}
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#recordPaymentModal">
                            <i class="fas fa-plus"></i> Record First Payment
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Right Column: Customer Ledger & Other Invoices -->
        <div class="col-lg-4">
            <!-- Customer Ledger Summary -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-book"></i> Customer Ledger (Recent)</h6>
                </div>
                <div class="card-body">
                    {% if customer_ledger %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in customer_ledger[:5] %}
                                <tr>
                                    <td>{{ entry.transaction_date|format_datetime('%m-%d') if entry.transaction_date else 'N/A' }}</td>
                                    <td>{{ entry.transaction_type }}</td>
                                    <td class="text-end">
                                        {% if entry.credit_amount %}
                                        <span class="text-success">₨{{ "{:,.0f}".format(entry.credit_amount) }}</span>
                                        {% elif entry.debit_amount %}
                                        <span class="text-danger">₨{{ "{:,.0f}".format(entry.debit_amount) }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('finance_customer_ledger') }}?customer={{ customer_name }}" class="btn btn-outline-primary btn-sm">
                            View Full Ledger
                        </a>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No ledger entries found</p>
                    {% endif %}
                </div>
            </div>

            <!-- Other Unpaid Invoices -->
            {% if other_unpaid %}
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Other Unpaid Invoices</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Invoice</th>
                                    <th>Outstanding</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in other_unpaid %}
                                <tr>
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td class="text-end text-danger">₨{{ "{:,.0f}".format(invoice.outstanding) }}</td>
                                    <td>
                                        <a href="{{ url_for('finance_invoice_details', invoice_number=invoice.invoice_number) }}" 
                                           class="btn btn-outline-primary btn-xs">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Record Payment Modal -->
{% if outstanding_balance > 0 %}
<div class="modal fade" id="recordPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('record_payment') }}">
                <div class="modal-header">
                    <h5 class="modal-title">Record Payment - {{ invoice_number }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="invoice_id" value="{{ invoice_number }}">
                    
                    <div class="mb-3">
                        <label class="form-label">Outstanding Amount</label>
                        <input type="text" class="form-control" value="₨{{ '{:,.2f}'.format(outstanding_balance) }}" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Payment Amount *</label>
                        <input type="number" name="amount" class="form-control" step="0.01" max="{{ outstanding_balance }}" required>
                        <div class="form-text">Maximum: ₨{{ "{:,.2f}".format(outstanding_balance) }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Payment Method *</label>
                        <select name="payment_method" class="form-select" required>
                            <option value="">Select Method</option>
                            <option value="Cash">Cash</option>
                            <option value="Cheque">Cheque</option>
                            <option value="Online">Online Transfer</option>
                            <option value="Bank Transfer">Bank Transfer</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Reference Number</label>
                        <input type="text" name="reference_number" class="form-control" placeholder="Cheque/Transaction number">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="2" placeholder="Payment notes"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Record Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<style>
.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
}
.table-sm th, .table-sm td {
    padding: 0.3rem;
}
</style>
{% endblock %}
