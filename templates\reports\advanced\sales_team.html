{% extends 'reports/reports_base.html' %}

{% block title %}Sales Team Performance - Medivent Pharmaceuticals ERP{% endblock %}

{% block head %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/python_charts.js') }}"></script>
<script src="{{ url_for('static', filename='js/python_charts_init.js') }}"></script>
<script src="{{ url_for('static', filename='js/org_chart.js') }}"></script>
<link rel="stylesheet" href="{{ url_for('static', filename='css/org_chart.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Sales Team Performance</h4>
                    <div>
                        <button class="btn btn-light" id="printSalesTeamBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('export_sales_team_pdf', start_date=start_date, end_date=end_date) }}" class="btn btn-light ml-2">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </a>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('sales_team_performance') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt"></i> Showing data from {{ start_date }} to {{ end_date }}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Team Summary</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <p><strong>Sales Agents:</strong> {{ summary.agent_count }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Total Sales:</strong> {{ "%.2f"|format(summary.total_sales or 0) }} PKR</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Total Orders:</strong> {{ summary.order_count }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Average Order:</strong> {{ "%.2f"|format(summary.average_order or 0) }} PKR</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Organizational Structure Chart -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Organizational Structure</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> Click on any division to view detailed sales analysis for that division.
                                    </div>
                                    <div id="organizationalStructureChart" class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status" id="orgChartSpinner">
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                        <img id="orgChartImage" src="" alt="Organizational Structure Chart" style="max-width: 100%; display: none;" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Division-specific Charts Container -->
                    <div id="divisionChartsContainer"></div>

                    <!-- Python-processed charts section -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Python-Powered Sales Analysis</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <div class="chart-title">Team Performance Metrics</div>
                                                <div id="teamPerformanceContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="teamPerformanceSpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="teamPerformanceImage" src="" alt="Team Performance Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <div class="chart-title">Sales Distribution by Agent</div>
                                                <div id="agentSalesContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="agentSalesSpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="agentSalesImage" src="" alt="Sales by Agent Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Nested Sales Analysis by Agent and Division</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> Click on any division in the charts below to see detailed sales analysis.
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <div class="chart-title">Sales Distribution by Agent</div>
                                                <div id="salesByAgentContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="salesByAgentSpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="salesByAgentImage" src="" alt="Sales by Agent Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <div class="chart-title">Top Agent's Division Sales</div>
                                                <div id="topAgentDivisionsContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="topAgentDivisionsSpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="topAgentDivisionsImage" src="" alt="Top Agent's Division Sales Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="chart-container">
                                                <div class="chart-title">Agent-Division Hierarchy (Nested Sunburst)</div>
                                                <div class="alert alert-info mb-3">
                                                    <i class="fas fa-info-circle"></i> Click on any agent or division in the chart to see detailed sales breakdown.
                                                </div>
                                                <div id="agentDivisionHierarchyContainer" class="text-center py-3">
                                                    <div class="spinner-border text-primary" role="status" id="agentDivisionHierarchySpinner">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <img id="agentDivisionHierarchyImage" src="" alt="Agent-Division Hierarchy Chart" style="max-width: 100%; display: none;" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Division-specific nested charts will appear here when a division is clicked -->
                                    <div id="nestedDivisionCharts" style="display: none;">
                                        <div class="division-detail-header mt-4 mb-3">
                                            <h4 id="selectedDivisionName" class="text-primary"></h4>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="hideNestedCharts()">
                                                <i class="fas fa-times"></i> Close
                                            </button>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="chart-container" style="height: 400px; min-height: 400px;">
                                                    <div class="chart-title">Division Products Sales</div>
                                                    <div id="divisionProductsChart" style="width: 100%; height: 350px;"></div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="chart-container" style="height: 400px; min-height: 400px;">
                                                    <div class="chart-title">Division Sales by Agent</div>
                                                    <div id="divisionAgentsChart" style="width: 100%; height: 350px;"></div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <div class="chart-container" style="height: 400px; min-height: 400px;">
                                                    <div class="chart-title">Monthly Sales Trend</div>
                                                    <div id="divisionMonthlySalesChart" style="width: 100%; height: 350px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Original Plotly charts (hidden but used for data) -->
                                    {% if charts.nested_pie %}
                                    <div id="nested-pie-chart" style="display: none;"></div>
                                    <script>
                                        var nestedPieData = {{ charts.nested_pie|safe }};
                                        Plotly.newPlot('nested-pie-chart', nestedPieData.data, nestedPieData.layout);
                                    </script>
                                    {% endif %}

                                    {% if charts.agent_pie %}
                                    <div id="agent-pie-chart" style="display: none;"></div>
                                    <script>
                                        var agentPieData = {{ charts.agent_pie|safe }};
                                        Plotly.newPlot('agent-pie-chart', agentPieData.data, agentPieData.layout);
                                    </script>
                                    {% endif %}

                                    {% if charts.division_pie %}
                                    <div id="division-pie-chart" style="display: none;"></div>
                                    <script>
                                        var divisionPieData = {{ charts.division_pie|safe }};
                                        Plotly.newPlot('division-pie-chart', divisionPieData.data, divisionPieData.layout);
                                    </script>
                                    {% endif %}

                                    {% if charts.sunburst %}
                                    <div id="sunburst-chart" style="display: none;"></div>
                                    <script>
                                        var sunburstData = {{ charts.sunburst|safe }};
                                        Plotly.newPlot('sunburst-chart', sunburstData.data, sunburstData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4" style="display: none;">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Sales Distribution by Agent</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.agent_pie %}
                                    <div id="agent-pie-chart"></div>
                                    <script>
                                        var agentPieData = {{ charts.agent_pie|safe }};
                                        Plotly.newPlot('agent-pie-chart', agentPieData.data, agentPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Top Agent's Division Sales</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.division_pie %}
                                    <div id="division-pie-chart"></div>
                                    <script>
                                        var divisionPieData = {{ charts.division_pie|safe }};
                                        Plotly.newPlot('division-pie-chart', divisionPieData.data, divisionPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4" style="display: none;">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Agent-Division Hierarchy (Nested Analysis)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.sunburst %}
                                    <div id="sunburst-chart"></div>
                                    <script>
                                        var sunburstData = {{ charts.sunburst|safe }};
                                        Plotly.newPlot('sunburst-chart', sunburstData.data, sunburstData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4" style="display: none;">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Sales by Agent (Nested Donut)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_donut_outer %}
                                    <div id="nested-donut-outer-chart"></div>
                                    <script>
                                        var nestedDonutOuterData = {{ charts.nested_donut_outer|safe }};
                                        Plotly.newPlot('nested-donut-outer-chart', nestedDonutOuterData.data, nestedDonutOuterData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("sales_team_performance") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_donut_outer) {
                                                        var chartData = JSON.parse(data.charts.nested_donut_outer);
                                                        Plotly.react('nested-donut-outer-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Top Agent's Division Sales (Nested Donut)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_donut_inner %}
                                    <div id="nested-donut-inner-chart"></div>
                                    <script>
                                        var nestedDonutInnerData = {{ charts.nested_donut_inner|safe }};
                                        Plotly.newPlot('nested-donut-inner-chart', nestedDonutInnerData.data, nestedDonutInnerData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("sales_team_performance") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_donut_inner) {
                                                        var chartData = JSON.parse(data.charts.nested_donut_inner);
                                                        Plotly.react('nested-donut-inner-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4" style="display: none;">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Sales Team Performance Metrics</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.team_performance_bar %}
                                    <div id="team-performance-chart"></div>
                                    <script>
                                        var teamPerformanceData = {{ charts.team_performance_bar|safe }};
                                        Plotly.newPlot('team-performance-chart', teamPerformanceData.data, teamPerformanceData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h5>Agent Performance</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Sales Agent</th>
                                            <th>Orders</th>
                                            <th>Total Sales</th>
                                            <th>Average Order</th>
                                            <th>Customers</th>
                                            <th>Largest Order</th>
                                            <th>Performance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if team_data %}
                                            {% for agent in team_data %}
                                            <tr>
                                                <td>{{ agent.sales_agent }}</td>
                                                <td>{{ agent.order_count }}</td>
                                                <td>{{ "%.2f"|format(agent.total_sales) }}</td>
                                                <td>{{ "%.2f"|format(agent.average_order) }}</td>
                                                <td>{{ agent.customer_count }}</td>
                                                <td>{{ "%.2f"|format(agent.largest_order) }}</td>
                                                <td>
                                                    <div class="progress">
                                                        {% if summary.total_sales %}
                                                            {% set percentage = (agent.total_sales / summary.total_sales * 100)|round(1) %}
                                                        {% else %}
                                                            {% set percentage = 0 %}
                                                        {% endif %}
                                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ percentage }}%" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100">{{ percentage }}%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center">No agent performance data available for the selected period</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>Product Category by Agent</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Sales Agent</th>
                                            <th>Category</th>
                                            <th>Sales</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if category_by_agent %}
                                            {% set current_agent = '' %}
                                            {% for item in category_by_agent %}
                                                {% if item.sales_agent != current_agent %}
                                                    {% set current_agent = item.sales_agent %}
                                                    <tr class="table-secondary">
                                                        <th colspan="3">{{ item.sales_agent }}</th>
                                                    </tr>
                                                {% endif %}
                                                <tr>
                                                    <td></td>
                                                    <td>{{ item.category }}</td>
                                                    <td>{{ "%.2f"|format(item.category_sales) }}</td>
                                                </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center">No category data available by agent</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block report_scripts %}
<script>
    // Global variables to store chart instances and data
    let salesByAgentChart = null;
    let topAgentDivisionsChart = null;
    let agentDivisionHierarchyChart = null;
    let divisionProductsChart = null;

    // Load Python-generated charts
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Sales team performance page loaded');

        // Initialize charts directly
        setTimeout(function() {
            // Get date range
            const startDate = document.querySelector('input[name="start_date"]').value;
            const endDate = document.querySelector('input[name="end_date"]').value;

            console.log(`Loading Python-generated charts with date range: ${startDate} to ${endDate}`);

            // Load sales team charts
            loadPythonSalesTeamCharts(startDate, endDate);
        }, 500); // Delay to ensure DOM is fully loaded
    });
    let divisionAgentsChart = null;
    let divisionMonthlySalesChart = null;
    let globalSalesData = null;

    // Load Python-generated charts
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Loading Python-generated charts');

        // Get date range
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;

        // Load organizational structure chart
        loadPythonChart('org_structure', startDate, endDate);

        // Load sales team charts
        loadPythonChart('sales_team', startDate, endDate);
    });

    // Function to load Python-generated charts
    function loadPythonChart(chartType, startDate, endDate) {
        console.log(`Loading ${chartType} charts for date range: ${startDate} to ${endDate}`);

        // Construct URL
        const url = `/python-charts/generate?type=${chartType}&start_date=${startDate}&end_date=${endDate}`;

        // Fetch chart data
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log(`Received ${chartType} chart data:`, data);

                if (chartType === 'org_structure') {
                    // Display organizational structure chart
                    if (data.org_structure) {
                        document.getElementById('orgChartSpinner').style.display = 'none';
                        const imgElement = document.getElementById('orgChartImage');
                        imgElement.src = data.org_structure;
                        imgElement.style.display = 'block';
                    }
                } else if (chartType === 'sales_team') {
                    // Display team performance chart
                    if (data.team_performance) {
                        document.getElementById('teamPerformanceSpinner').style.display = 'none';
                        const imgElement = document.getElementById('teamPerformanceImage');
                        imgElement.src = data.team_performance;
                        imgElement.style.display = 'block';
                    }

                    // Display agent sales pie chart
                    if (data.agent_sales_pie) {
                        document.getElementById('agentSalesSpinner').style.display = 'none';
                        const imgElement = document.getElementById('agentSalesImage');
                        imgElement.src = data.agent_sales_pie;
                        imgElement.style.display = 'block';
                    }

                    // Display sales by agent chart
                    if (data.agent_sales_pie) {
                        document.getElementById('salesByAgentSpinner').style.display = 'none';
                        const imgElement = document.getElementById('salesByAgentImage');
                        imgElement.src = data.agent_sales_pie;
                        imgElement.style.display = 'block';
                    }

                    // Display top agent's division sales chart
                    if (data.top_agent_divisions) {
                        document.getElementById('topAgentDivisionsSpinner').style.display = 'none';
                        const imgElement = document.getElementById('topAgentDivisionsImage');
                        imgElement.src = data.top_agent_divisions;
                        imgElement.style.display = 'block';
                    }

                    // Display agent-division hierarchy chart
                    if (data.agent_division_hierarchy) {
                        document.getElementById('agentDivisionHierarchySpinner').style.display = 'none';
                        const imgElement = document.getElementById('agentDivisionHierarchyImage');
                        imgElement.src = data.agent_division_hierarchy;
                        imgElement.style.display = 'block';
                    }
                }
            })
            .catch(error => {
                console.error(`Error loading ${chartType} charts:`, error);
                // Show error message
                if (chartType === 'org_structure') {
                    document.getElementById('orgChartSpinner').style.display = 'none';
                    document.getElementById('organizationalStructureChart').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';
                } else if (chartType === 'sales_team') {
                    document.getElementById('teamPerformanceSpinner').style.display = 'none';
                    document.getElementById('teamPerformanceContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';

                    document.getElementById('agentSalesSpinner').style.display = 'none';
                    document.getElementById('agentSalesContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';

                    document.getElementById('salesByAgentSpinner').style.display = 'none';
                    document.getElementById('salesByAgentContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';

                    document.getElementById('topAgentDivisionsSpinner').style.display = 'none';
                    document.getElementById('topAgentDivisionsContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';

                    document.getElementById('agentDivisionHierarchySpinner').style.display = 'none';
                    document.getElementById('agentDivisionHierarchyContainer').innerHTML = '<div class="alert alert-danger">Error loading chart. Please try again later.</div>';
                }
            });
    }

        // Render organizational structure directly
        setTimeout(function() {
            const container = document.getElementById('organizationalStructureChart');
            if (container) {
                console.log('Rendering organizational structure chart directly');

                // Create a simple org chart using Plotly
                const divisions = [
                    { name: 'ONKO-KOSEL', head: 'Raees Ur Rehman Warsi', title: 'General Manager' },
                    { name: 'AQVIDA', head: 'Kashif Hayat', title: 'Business Unit Head' },
                    { name: 'BEACON-R', head: 'Javed Anwar', title: 'Business Unit Head' },
                    { name: 'BEACON-NOC', head: 'Fahad Mir', title: 'Business Unit Head' },
                    { name: 'CARDIO', head: 'Ali Asif', title: 'General Manager' },
                    { name: 'NOC', head: 'Asad Ali Sheikh', title: 'Business Unit Head' }
                ];

                // Create a sunburst chart for the organizational structure
                const labels = ['Medivent Pharmaceuticals'];
                const parents = [''];
                const values = [100];
                const ids = ['root'];

                // Add divisions
                divisions.forEach(div => {
                    labels.push(div.name);
                    parents.push('Medivent Pharmaceuticals');
                    values.push(16); // Equal size for each division
                    ids.push(`div-${div.name}`);
                });

                // Add division heads
                divisions.forEach(div => {
                    labels.push(`${div.title}: ${div.head}`);
                    parents.push(div.name);
                    values.push(16); // Equal size for each head
                    ids.push(`head-${div.name}`);
                });

                const data = [{
                    type: 'sunburst',
                    labels: labels,
                    parents: parents,
                    values: values,
                    ids: ids,
                    branchvalues: 'total',
                    insidetextorientation: 'radial',
                    marker: {
                        colors: [
                            'rgba(31, 119, 180, 0.8)',  // Root
                            'rgba(255, 127, 14, 0.7)',  // Division 1
                            'rgba(44, 160, 44, 0.7)',   // Division 2
                            'rgba(214, 39, 40, 0.7)',   // Division 3
                            'rgba(148, 103, 189, 0.7)', // Division 4
                            'rgba(140, 86, 75, 0.7)',   // Division 5
                            'rgba(227, 119, 194, 0.7)', // Division 6
                            'rgba(255, 127, 14, 0.5)',  // Head 1
                            'rgba(44, 160, 44, 0.5)',   // Head 2
                            'rgba(214, 39, 40, 0.5)',   // Head 3
                            'rgba(148, 103, 189, 0.5)', // Head 4
                            'rgba(140, 86, 75, 0.5)',   // Head 5
                            'rgba(227, 119, 194, 0.5)'  // Head 6
                        ],
                        line: {
                            color: 'white',
                            width: 1
                        }
                    },
                    hoverinfo: 'label+text',
                    text: labels.map(l => l === 'Medivent Pharmaceuticals' ? 'Click to explore divisions' : 'Click for details'),
                    textfont: {
                        size: 14
                    }
                }];

                const layout = {
                    margin: {l: 0, r: 0, b: 0, t: 0},
                    width: container.offsetWidth,
                    height: 500,
                    title: {
                        text: 'Medivent Pharmaceuticals Organizational Structure',
                        font: {
                            size: 18
                        }
                    }
                };

                Plotly.newPlot(container, data, layout);

                // Add click event to show division details
                container.on('plotly_click', function(data) {
                    const point = data.points[0];
                    const divName = point.label;

                    // Check if this is a division name
                    if (divisions.some(d => d.name === divName)) {
                        console.log(`Clicked on division: ${divName}`);
                        toggleDivisionCharts(divName);
                    }
                });
            }
        }, 500);

        // Render team performance charts
        setTimeout(function() {
            if (document.getElementById('teamPerformanceChart')) {
                renderTeamPerformanceChart(fallbackData.team_performance);
            }

            if (document.getElementById('agentSalesChart')) {
                renderAgentSalesChart(fallbackData.team_performance);
            }
        }, 1000);

        // Render nested charts directly
        setTimeout(function() {
            // Sales by Agent Chart
            const salesByAgentContainer = document.getElementById('salesByAgentChart');
            if (salesByAgentContainer) {
                console.log('Rendering sales by agent chart directly');

                // Sort agents by sales
                const sortedAgents = [...fallbackData.team_performance].sort((a, b) => b.total_sales - a.total_sales);

                // Create the chart data
                const data = [{
                    type: 'pie',
                    labels: sortedAgents.map(a => a.agent_name),
                    values: sortedAgents.map(a => a.total_sales),
                    textinfo: 'label+percent',
                    insidetextorientation: 'radial',
                    marker: {
                        colors: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        line: {
                            color: 'white',
                            width: 1
                        }
                    },
                    hoverinfo: 'label+value+percent',
                    hoverlabel: {
                        bgcolor: '#FFF',
                        bordercolor: '#333',
                        font: {
                            size: 14
                        }
                    }
                }];

                const layout = {
                    title: {
                        text: 'Sales Distribution by Agent',
                        font: {
                            size: 16
                        }
                    },
                    margin: {
                        l: 20,
                        r: 20,
                        t: 40,
                        b: 20
                    },
                    height: 350,
                    width: salesByAgentContainer.offsetWidth
                };

                Plotly.newPlot(salesByAgentContainer, data, layout);
                salesByAgentChart = salesByAgentContainer;
            }

            // Top Agent's Division Sales Chart
            const topAgentDivisionsContainer = document.getElementById('topAgentDivisionsChart');
            if (topAgentDivisionsContainer && fallbackData.division_by_agent) {
                console.log('Rendering top agent divisions chart directly');

                // Get top agent
                const topAgent = fallbackData.team_performance.sort((a, b) => b.total_sales - a.total_sales)[0];
                const topAgentName = topAgent.agent_name;

                // Get divisions for top agent
                const topAgentDivisions = fallbackData.division_by_agent.filter(d => d.agent_name === topAgentName);

                // Create the chart data
                const data = [{
                    type: 'pie',
                    labels: topAgentDivisions.map(d => d.division_name),
                    values: topAgentDivisions.map(d => d.total_sales),
                    textinfo: 'label+percent',
                    insidetextorientation: 'radial',
                    marker: {
                        colors: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        line: {
                            color: 'white',
                            width: 1
                        }
                    },
                    hoverinfo: 'label+value+percent',
                    hoverlabel: {
                        bgcolor: '#FFF',
                        bordercolor: '#333',
                        font: {
                            size: 14
                        }
                    }
                }];

                const layout = {
                    title: {
                        text: `${topAgentName}'s Division Sales`,
                        font: {
                            size: 16
                        }
                    },
                    margin: {
                        l: 20,
                        r: 20,
                        t: 40,
                        b: 20
                    },
                    height: 350,
                    width: topAgentDivisionsContainer.offsetWidth
                };

                Plotly.newPlot(topAgentDivisionsContainer, data, layout);
                topAgentDivisionsChart = topAgentDivisionsContainer;
            }

            // Agent-Division Hierarchy Chart
            const hierarchyContainer = document.getElementById('agentDivisionHierarchyChart');
            if (hierarchyContainer && fallbackData.division_by_agent) {
                console.log('Rendering agent-division hierarchy chart directly');

                // Group data by agent and division
                const agentDivisionMap = {};
                const divisions = new Set();
                const agents = new Set();

                fallbackData.division_by_agent.forEach(item => {
                    if (!agentDivisionMap[item.agent_name]) {
                        agentDivisionMap[item.agent_name] = {};
                    }
                    agentDivisionMap[item.agent_name][item.division_name] = item.total_sales;
                    divisions.add(item.division_name);
                    agents.add(item.agent_name);
                });

                // Convert to arrays
                const divisionNames = Array.from(divisions);
                const agentNames = Array.from(agents);

                // Create traces for each division
                const traces = divisionNames.map((division, index) => {
                    return {
                        x: agentNames,
                        y: agentNames.map(agent => agentDivisionMap[agent][division] || 0),
                        name: division,
                        type: 'bar',
                        marker: {
                            color: [
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)',
                                'rgba(255, 159, 64, 0.7)'
                            ][index % 6]
                        }
                    };
                });

                const layout = {
                    title: {
                        text: 'Agent-Division Hierarchy (Nested Analysis)',
                        font: {
                            size: 16
                        }
                    },
                    barmode: 'stack',
                    margin: {
                        l: 50,
                        r: 20,
                        t: 40,
                        b: 100
                    },
                    height: 400,
                    width: hierarchyContainer.offsetWidth,
                    xaxis: {
                        tickangle: -45
                    },
                    yaxis: {
                        title: 'Sales (PKR)'
                    },
                    legend: {
                        orientation: 'h',
                        y: -0.2
                    }
                };

                Plotly.newPlot(hierarchyContainer, traces, layout);
                agentDivisionHierarchyChart = hierarchyContainer;
            }
        }, 1500);
    });

    // Function to show empty state message
    function showEmptyStateMessage(chartId, message) {
        const chartElement = document.getElementById(chartId);
        if (chartElement) {
            chartElement.parentElement.innerHTML = `
                <div class="text-center text-muted p-4">
                    <i class="fas fa-chart-line fa-3x mb-3"></i><br>
                    ${message}
                </div>
            `;
        }
    }

    // Function to resize charts when window is resized
    function resizeCharts() {
        // Resize organizational structure chart
        const orgStructureChart = document.getElementById('organizationalStructureChart');
        if (orgStructureChart) {
            Plotly.relayout(orgStructureChart, {
                width: orgStructureChart.offsetWidth,
                height: 500
            });
        }

        if (salesByAgentChart) {
            Plotly.relayout(salesByAgentChart, {
                width: document.getElementById('salesByAgentChart').offsetWidth,
                height: 350
            });
        }

        if (topAgentDivisionsChart) {
            Plotly.relayout(topAgentDivisionsChart, {
                width: document.getElementById('topAgentDivisionsChart').offsetWidth,
                height: 350
            });
        }

        if (agentDivisionHierarchyChart) {
            Plotly.relayout(agentDivisionHierarchyChart, {
                width: document.getElementById('agentDivisionHierarchyChart').offsetWidth,
                height: 400
            });
        }

        if (divisionProductsChart) {
            Plotly.relayout(divisionProductsChart, {
                width: document.getElementById('divisionProductsChart').offsetWidth,
                height: 350
            });
        }

        if (divisionAgentsChart) {
            Plotly.relayout(divisionAgentsChart, {
                width: document.getElementById('divisionAgentsChart').offsetWidth,
                height: 350
            });
        }

        if (divisionMonthlySalesChart) {
            Plotly.relayout(divisionMonthlySalesChart, {
                width: document.getElementById('divisionMonthlySalesChart').offsetWidth,
                height: 350
            });
        }
    }

    // Function to render the team performance chart
    function renderTeamPerformanceChart(teamData) {
        console.log('Rendering team performance chart');
        const canvas = document.getElementById('teamPerformanceChart');
        if (!canvas) {
            console.error('teamPerformanceChart container not found');
            return;
        }

        // Get the 2D context
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Sort agents by performance or sales
        const sortedAgents = [...teamData].sort((a, b) => b.total_sales - a.total_sales);
        const topAgents = sortedAgents.slice(0, 5); // Get top 5 agents

        // Create the chart
        canvas.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: topAgents.map(a => a.agent_name),
                datasets: [{
                    label: 'Total Sales (PKR)',
                    data: topAgents.map(a => a.total_sales),
                    backgroundColor: 'rgba(54, 162, 235, 0.7)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Sales (PKR)'
                        }
                    }
                }
            }
        });
    }

    // Function to render the agent sales chart
    function renderAgentSalesChart(teamData) {
        console.log('Rendering agent sales chart');
        const canvas = document.getElementById('agentSalesChart');
        if (!canvas) {
            console.error('agentSalesChart container not found');
            return;
        }

        // Get the 2D context
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Sort agents by sales
        const sortedAgents = [...teamData].sort((a, b) => b.total_sales - a.total_sales);
        const topAgents = sortedAgents.slice(0, 5); // Get top 5 agents

        // Create the chart
        canvas.chart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: topAgents.map(a => a.agent_name),
                datasets: [{
                    data: topAgents.map(a => a.total_sales),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: 'white',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }

    // Function to render the organizational structure chart
    function renderOrganizationalStructureChart() {
        const container = document.getElementById('organizationalStructureChart');
        if (!container) {
            console.error('organizationalStructureChart container not found');
            return;
        }

        console.log('Rendering organizational structure chart');

        // Prepare data for the sunburst chart
        const labels = ['Medivent Pharmaceuticals'];
        const parents = [''];
        const values = [100]; // Root value
        const ids = ['root'];
        const titles = [''];

        // Add divisions
        organizationalData.divisions.forEach((division, index) => {
            labels.push(division.name);
            parents.push('Medivent Pharmaceuticals');
            values.push(70); // Division value
            ids.push(`division-${index}`);
            titles.push(`Division: ${division.name}`);

            // Add division leader
            const leaderId = `leader-${division.name}`;
            labels.push(`${division.leader.name} (${division.leader.title})`);
            parents.push(division.name);
            values.push(40); // Leader value
            ids.push(leaderId);
            titles.push(`${division.leader.title}: ${division.leader.name}`);

            // Add team members
            division.members.forEach((member, memberIndex) => {
                labels.push(`${member.name} (${member.title})`);
                parents.push(division.name);
                values.push(20); // Member value
                ids.push(`member-${division.name}-${memberIndex}`);
                titles.push(`${member.title}: ${member.name}`);
            });
        });

        // Add special teams
        organizationalData.specialTeams.forEach((team, index) => {
            labels.push(team.name);
            parents.push('Medivent Pharmaceuticals');
            values.push(60); // Special team value
            ids.push(`special-team-${index}`);
            titles.push(`Team: ${team.name}`);

            // Add team leader
            const leaderId = `leader-${team.name}`;
            labels.push(`${team.leader.name} (${team.leader.title})`);
            parents.push(team.name);
            values.push(35); // Leader value
            ids.push(leaderId);
            titles.push(`${team.leader.title}: ${team.leader.name}`);

            // Add team members
            team.members.forEach((member, memberIndex) => {
                labels.push(`${member.name} (${member.title})`);
                parents.push(team.name);
                values.push(15); // Member value
                ids.push(`special-member-${team.name}-${memberIndex}`);
                titles.push(`${member.title}: ${member.name}`);
            });
        });

        // Create color mapping for divisions and roles
        const colorMap = {
            'Medivent Pharmaceuticals': '#1f77b4', // Root - blue
            'ONKO-KOSEL': '#ff7f0e',    // Orange
            'AQVIDA': '#2ca02c',        // Green
            'BEACON-R': '#d62728',      // Red
            'BEACON-NOC': '#9467bd',    // Purple
            'CARDIO': '#8c564b',        // Brown
            'NOC': '#e377c2',           // Pink
            'Deputy General Manager': '#7f7f7f' // Gray
        };

        // Create color array based on labels
        const colors = labels.map(label => {
            // Check if this is a division or special team
            if (colorMap[label]) {
                return colorMap[label];
            }

            // Check if this is a leader or member
            for (const division of organizationalData.divisions) {
                if (label.includes(division.leader.name)) {
                    return colorMap[division.name] + 'cc'; // Slightly transparent
                }

                // Check if it's a team member
                for (const member of division.members) {
                    if (label.includes(member.name)) {
                        return colorMap[division.name] + '99'; // More transparent
                    }
                }
            }

            // Check special teams
            for (const team of organizationalData.specialTeams) {
                if (label.includes(team.leader.name)) {
                    return colorMap[team.name] + 'cc'; // Slightly transparent
                }

                // Check if it's a team member
                for (const member of team.members) {
                    if (label.includes(member.name)) {
                        return colorMap[team.name] + '99'; // More transparent
                    }
                }
            }

            // Default color
            return '#17a2b8';
        });

        // Create the chart
        const data = {
            type: 'sunburst',
            labels: labels,
            parents: parents,
            values: values,
            ids: ids,
            text: titles,
            branchvalues: 'total',
            insidetextorientation: 'radial',
            hoverinfo: 'label+text',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            },
            textfont: {
                size: 12
            },
            marker: {
                colors: colors,
                line: {
                    color: 'white',
                    width: 1
                }
            }
        };

        const layout = {
            margin: {
                l: 0,
                r: 0,
                t: 50,
                b: 0
            },
            height: 500,
            width: container.offsetWidth,
            title: {
                text: 'Medivent Pharmaceuticals Organizational Structure',
                font: {
                    size: 18,
                    color: '#333'
                }
            }
        };

        Plotly.newPlot(container, [data], layout);

        // Add click event to show division details
        container.on('plotly_click', function(data) {
            const pointData = data.points[0];
            const label = pointData.label;

            // Check if clicked on a division
            const division = organizationalData.divisions.find(div => div.name === label);
            if (division) {
                showDivisionDetails(label);
            }
        });
    }

    // Direct initialization script for sales team charts
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Sales team page loaded');

        // Add window resize event listener
        window.addEventListener('resize', resizeCharts);

        // Render organizational structure chart
        renderOrganizationalStructureChart();

        // Check if all chart containers exist
        const chartContainers = [
            'salesByAgentChart',
            'topAgentDivisionsChart',
            'agentDivisionHierarchyChart',
            'divisionProductsChart',
            'divisionAgentsChart',
            'divisionMonthlySalesChart'
        ];

        chartContainers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                console.log(`Chart container found: ${containerId}`);
            } else {
                console.error(`Chart container NOT found: ${containerId}`);
            }
        });

        // Initialize charts directly with a longer delay to ensure DOM is fully loaded
        setTimeout(function() {
            // Get date range
            const startDate = document.querySelector('input[name="start_date"]').value;
            const endDate = document.querySelector('input[name="end_date"]').value;

            console.log(`Initializing charts with date range: ${startDate} to ${endDate}`);

            // Fetch data and render charts
            fetch(`/api/data/sales-team?start_date=${startDate}&end_date=${endDate}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Sales team data received:', data);
                    globalSalesData = data;

                    // Initialize the main charts
                    initializeMainCharts(data);

                    // Resize charts after a short delay to ensure they're properly rendered
                    setTimeout(resizeCharts, 500);
                })
                .catch(error => {
                    console.error('Error fetching sales team data:', error);

                    // Generate fallback data for testing
                    console.log('Generating fallback data for testing');
                    globalSalesData = generateFallbackData();
                    initializeMainCharts(globalSalesData);

                    // Resize charts after a short delay to ensure they're properly rendered
                    setTimeout(resizeCharts, 500);
                });
        }, 1000); // Longer delay to ensure DOM is fully loaded
    });

    // Function to initialize the main charts
    function initializeMainCharts(data) {
        console.log('Initializing main charts with data:', data);

        if (data.team_performance && data.team_performance.length > 0) {
            console.log('Rendering sales by agent chart with data:', data.team_performance);
            renderSalesByAgentChart(data.team_performance);
        } else {
            console.warn('No team_performance data available for sales by agent chart');
        }

        if (data.division_by_agent && data.division_by_agent.length > 0 &&
            data.team_performance && data.team_performance.length > 0) {

            // Get the top agent
            const topAgent = data.team_performance.sort((a, b) => b.total_sales - a.total_sales)[0];
            console.log('Top agent identified:', topAgent);

            // Filter division data for the top agent
            const topAgentDivisions = data.division_by_agent.filter(d => d.agent_name === topAgent.agent_name);
            console.log('Top agent divisions:', topAgentDivisions);

            // Render the top agent's division sales chart
            renderTopAgentDivisionsChart(topAgentDivisions, topAgent.agent_name);

            // Render the agent-division hierarchy chart
            renderAgentDivisionHierarchyChart(data.division_by_agent);
        } else {
            console.warn('Missing data for division charts:', {
                'division_by_agent': data.division_by_agent ? data.division_by_agent.length : 0,
                'team_performance': data.team_performance ? data.team_performance.length : 0
            });
        }
    }

    // Function to render the sales by agent chart (sunburst chart)
    function renderSalesByAgentChart(teamData) {
        const container = document.getElementById('salesByAgentChart');
        if (!container) {
            console.error('salesByAgentChart container not found');
            return;
        }

        console.log('Rendering sales by agent chart');

        // Sort agents by sales
        const sortedAgents = [...teamData].sort((a, b) => b.total_sales - a.total_sales);

        // Prepare data for the sunburst chart
        const labels = ['Total Sales'];
        const parents = [''];
        const values = [sortedAgents.reduce((sum, agent) => sum + agent.total_sales, 0)];
        const ids = ['total'];
        const colors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
        ];

        // Add agent data
        sortedAgents.forEach((agent, index) => {
            labels.push(agent.agent_name);
            parents.push('Total Sales');
            values.push(agent.total_sales);
            ids.push(`agent-${index}`);
        });

        // Create the chart
        const data = {
            type: 'sunburst',
            labels: labels,
            parents: parents,
            values: values,
            ids: ids,
            branchvalues: 'total',
            insidetextorientation: 'radial',
            marker: {
                colors: colors,
                line: {
                    color: 'white',
                    width: 1
                }
            },
            hoverinfo: 'label+value+percent parent',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            }
        };

        const layout = {
            margin: {
                l: 0,
                r: 0,
                t: 30,
                b: 0
            },
            height: 350,
            width: container.offsetWidth
        };

        Plotly.newPlot(container, [data], layout);

        // Store chart instance
        salesByAgentChart = container;
    }

    // Function to render the top agent's division sales chart
    function renderTopAgentDivisionsChart(divisionData, agentName) {
        const container = document.getElementById('topAgentDivisionsChart');
        if (!container) {
            console.error('topAgentDivisionsChart container not found');
            return;
        }

        console.log('Rendering top agent divisions chart');

        // Sort divisions by sales
        const sortedDivisions = [...divisionData].sort((a, b) => b.total_sales - a.total_sales);

        // Prepare data for the sunburst chart
        const labels = ['Total Sales'];
        const parents = [''];
        const values = [sortedDivisions.reduce((sum, div) => sum + div.total_sales, 0)];
        const ids = ['total'];
        const colors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
        ];

        // Add division data
        sortedDivisions.forEach((division, index) => {
            labels.push(division.division_name);
            parents.push('Total Sales');
            values.push(division.total_sales);
            ids.push(`division-${index}`);
        });

        // Create the chart
        const data = {
            type: 'sunburst',
            labels: labels,
            parents: parents,
            values: values,
            ids: ids,
            branchvalues: 'total',
            insidetextorientation: 'radial',
            marker: {
                colors: colors,
                line: {
                    color: 'white',
                    width: 1
                }
            },
            hoverinfo: 'label+value+percent parent',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            }
        };

        const layout = {
            title: {
                text: `${agentName}'s Division Sales`,
                font: {
                    size: 16
                }
            },
            margin: {
                l: 0,
                r: 0,
                t: 40,
                b: 0
            },
            height: 350,
            width: container.offsetWidth
        };

        Plotly.newPlot(container, [data], layout);

        // Store chart instance
        topAgentDivisionsChart = container;
    }

    // Function to render the agent-division hierarchy chart
    function renderAgentDivisionHierarchyChart(divisionData) {
        const container = document.getElementById('agentDivisionHierarchyChart');
        if (!container) {
            console.error('agentDivisionHierarchyChart container not found');
            return;
        }

        console.log('Rendering agent-division hierarchy chart');

        // Group data by agent
        const agentGroups = {};
        divisionData.forEach(item => {
            const agent = item.agent_name;
            if (!agentGroups[agent]) {
                agentGroups[agent] = [];
            }
            agentGroups[agent].push(item);
        });

        // Create a treemap-like visualization using a bar chart
        const agents = Object.keys(agentGroups);
        const datasets = [];

        // Create a dataset for each division
        const allDivisions = [...new Set(divisionData.map(item => item.division_name))];

        const colors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
        ];

        allDivisions.forEach((division, index) => {
            const data = [];
            agents.forEach(agent => {
                const divisionItems = agentGroups[agent].filter(item => item.division_name === division);
                data.push(divisionItems.length > 0 ? divisionItems[0].total_sales : 0);
            });

            datasets.push({
                name: division,
                type: 'bar',
                x: agents,
                y: data,
                marker: {
                    color: colors[index % colors.length]
                }
            });
        });

        const layout = {
            title: {
                text: 'Agent-Division Hierarchy (Nested Analysis)',
                font: {
                    size: 16
                }
            },
            barmode: 'stack',
            xaxis: {
                title: 'Sales Agents'
            },
            yaxis: {
                title: 'Sales (PKR)'
            },
            margin: {
                l: 50,
                r: 20,
                t: 40,
                b: 50
            },
            height: 400,
            width: container.offsetWidth
        };

        Plotly.newPlot(container, datasets, layout);

        // Store chart instance
        agentDivisionHierarchyChart = container;
    }

    // Function to show division empty state
    function showDivisionEmptyState(divisionName) {
        console.log(`Showing empty state for division: ${divisionName}`);

        // Show empty state for all division charts
        showEmptyStateMessage('productSalesChart', `No product sales data available for ${divisionName}`);
        showEmptyStateMessage('agentSalesChart', `No agent sales data available for ${divisionName}`);
        showEmptyStateMessage('monthlySalesChart', `No monthly sales data available for ${divisionName}`);
    }

    // Function to toggle division charts visibility
    function toggleDivisionCharts(divisionName) {
        console.log(`Toggling charts for division: ${divisionName}`);

        // Create a container for the division charts if it doesn't exist
        let divisionContainer = document.getElementById(`division-charts-${divisionName.replace(/\s+/g, '-')}`);

        if (!divisionContainer) {
            console.log(`Creating container for division: ${divisionName}`);
            divisionContainer = document.createElement('div');
            divisionContainer.id = `division-charts-${divisionName.replace(/\s+/g, '-')}`;
            divisionContainer.className = 'division-charts';

            // Create the division charts HTML
            divisionContainer.innerHTML = `
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">${divisionName} Division Sales Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-container" style="height: 300px;">
                                    <div class="chart-title">${divisionName} Sales by Product</div>
                                    <div id="division-product-sales-${divisionName.replace(/\s+/g, '-')}" style="width: 100%; height: 280px;"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container" style="height: 300px;">
                                    <div class="chart-title">${divisionName} Sales by Agent</div>
                                    <div id="division-agent-sales-${divisionName.replace(/\s+/g, '-')}" style="width: 100%; height: 280px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="chart-container" style="height: 300px;">
                                    <div class="chart-title">${divisionName} Monthly Sales Trend</div>
                                    <div id="division-monthly-sales-${divisionName.replace(/\s+/g, '-')}" style="width: 100%; height: 280px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="text-right mt-3">
                            <button type="button" class="btn btn-secondary" onclick="hideDivisionCharts('${divisionName}')">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add the container to the page
            document.getElementById('divisionChartsContainer').appendChild(divisionContainer);
        }

        // Toggle visibility
        const isVisible = divisionContainer.style.display !== 'none';

        // Hide all division charts first
        const allDivisionCharts = document.querySelectorAll('.division-charts');
        allDivisionCharts.forEach(container => {
            container.style.display = 'none';
        });

        // If it was hidden, show it
        if (!isVisible) {
            divisionContainer.style.display = 'block';

            // Load division-specific charts
            loadDivisionCharts(divisionName);

            // Scroll to the charts
            divisionContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    // Function to hide division charts
    function hideDivisionCharts(divisionName) {
        const divisionContainer = document.getElementById(`division-charts-${divisionName.replace(/\s+/g, '-')}`);
        if (divisionContainer) {
            divisionContainer.style.display = 'none';
        }
    }

    // Function to load division-specific charts
    function loadDivisionCharts(divisionName) {
        console.log(`Loading charts for division: ${divisionName}`);

        // Generate fallback data for the division
        const divisionData = generateFallbackDivisionData(divisionName);

        // Render the division charts
        renderDivisionProductSalesChart(divisionName, divisionData.productSales);
        renderDivisionAgentSalesChart(divisionName, divisionData.agentSales);
        renderDivisionMonthlySalesChart(divisionName, divisionData.monthlySales);
    }

    // Function to render division product sales chart
    function renderDivisionProductSalesChart(divisionName, productData) {
        const divisionId = divisionName.replace(/\s+/g, '-');
        const containerId = `division-product-sales-${divisionId}`;
        const container = document.getElementById(containerId);

        if (!container) {
            console.error(`Container not found: ${containerId}`);
            return;
        }

        console.log(`Rendering product sales chart for division: ${divisionName}`);

        // Sort products by sales
        const sortedProducts = [...productData].sort((a, b) => b.total_sales - a.total_sales);

        // Create the chart data
        const data = [{
            type: 'bar',
            x: sortedProducts.map(p => p.product_name),
            y: sortedProducts.map(p => p.total_sales),
            marker: {
                color: 'rgba(54, 162, 235, 0.7)',
                line: {
                    color: 'rgba(54, 162, 235, 1)',
                    width: 1
                }
            }
        }];

        const layout = {
            margin: {
                l: 50,
                r: 20,
                t: 30,
                b: 100
            },
            height: 280,
            width: container.offsetWidth,
            xaxis: {
                tickangle: -45
            },
            yaxis: {
                title: 'Sales (PKR)'
            }
        };

        Plotly.newPlot(container, data, layout);
    }

    // Function to render division agent sales chart
    function renderDivisionAgentSalesChart(divisionName, agentData) {
        const divisionId = divisionName.replace(/\s+/g, '-');
        const containerId = `division-agent-sales-${divisionId}`;
        const container = document.getElementById(containerId);

        if (!container) {
            console.error(`Container not found: ${containerId}`);
            return;
        }

        console.log(`Rendering agent sales chart for division: ${divisionName}`);

        // Create the chart data
        const data = [{
            type: 'pie',
            labels: agentData.map(a => a.agent_name),
            values: agentData.map(a => a.total_sales),
            textinfo: 'label+percent',
            insidetextorientation: 'radial',
            marker: {
                colors: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                line: {
                    color: 'white',
                    width: 1
                }
            }
        }];

        const layout = {
            margin: {
                l: 20,
                r: 20,
                t: 30,
                b: 20
            },
            height: 280,
            width: container.offsetWidth
        };

        Plotly.newPlot(container, data, layout);
    }

    // Function to render division monthly sales chart
    function renderDivisionMonthlySalesChart(divisionName, monthlyData) {
        const divisionId = divisionName.replace(/\s+/g, '-');
        const containerId = `division-monthly-sales-${divisionId}`;
        const container = document.getElementById(containerId);

        if (!container) {
            console.error(`Container not found: ${containerId}`);
            return;
        }

        console.log(`Rendering monthly sales chart for division: ${divisionName}`);

        // Sort monthly data by date
        const sortedMonthlyData = [...monthlyData].sort((a, b) => a.month.localeCompare(b.month));

        // Format month labels
        const monthLabels = sortedMonthlyData.map(m => {
            const [year, month] = m.month.split('-');
            const date = new Date(year, month - 1);
            return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        });

        // Create the chart data
        const data = [{
            type: 'scatter',
            mode: 'lines+markers',
            x: monthLabels,
            y: sortedMonthlyData.map(m => m.total_sales),
            line: {
                color: 'rgba(75, 192, 192, 1)',
                width: 3
            },
            marker: {
                color: 'rgba(75, 192, 192, 1)',
                size: 8
            },
            fill: 'tozeroy',
            fillcolor: 'rgba(75, 192, 192, 0.2)'
        }];

        const layout = {
            margin: {
                l: 50,
                r: 20,
                t: 30,
                b: 50
            },
            height: 280,
            width: container.offsetWidth,
            xaxis: {
                title: 'Month'
            },
            yaxis: {
                title: 'Sales (PKR)'
            }
        };

        Plotly.newPlot(container, data, layout);
    }

    // Function to render the top agent's division sales chart (sunburst chart)
    function renderTopAgentDivisionsChart(divisionData, agentName) {
        const container = document.getElementById('topAgentDivisionsChart');
        if (!container) {
            console.error('topAgentDivisionsChart container not found');
            return;
        }

        console.log('topAgentDivisionsChart container dimensions:', {
            width: container.offsetWidth,
            height: container.offsetHeight,
            clientWidth: container.clientWidth,
            clientHeight: container.clientHeight
        });

        // Sort divisions by sales
        const sortedDivisions = [...divisionData].sort((a, b) => b.total_sales - a.total_sales);

        // Prepare data for the sunburst chart
        const labels = ['Total Sales'];
        const parents = [''];
        const values = [sortedDivisions.reduce((sum, div) => sum + div.total_sales, 0)];
        const ids = ['total'];
        const colors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
        ];

        // Add division data
        sortedDivisions.forEach((division, index) => {
            labels.push(division.division_name);
            parents.push('Total Sales');
            values.push(division.total_sales);
            ids.push(`division-${index}`);
        });

        // Create the chart
        const data = {
            type: 'sunburst',
            labels: labels,
            parents: parents,
            values: values,
            ids: ids,
            branchvalues: 'total',
            insidetextorientation: 'radial',
            marker: {
                colors: colors,
                line: {
                    color: 'white',
                    width: 1
                }
            },
            hoverinfo: 'label+value+percent parent',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            }
        };

        const layout = {
            title: {
                text: `${agentName}'s Division Sales`,
                font: {
                    size: 16
                }
            },
            margin: {
                l: 0,
                r: 0,
                t: 40,
                b: 0
            },
            height: 350,
            clickmode: 'event'
        };

        Plotly.newPlot(container, [data], layout);

        // Add click event to show division details
        container.on('plotly_click', function(data) {
            const pointData = data.points[0];
            // Only respond to clicks on division slices (not the center)
            if (pointData.label !== 'Total Sales') {
                showDivisionDetails(pointData.label);
            }
        });

        // Store chart instance
        topAgentDivisionsChart = container;
    }

    // Function to render the agent-division hierarchy chart (stacked bar chart)
    function renderAgentDivisionHierarchyChart(divisionData) {
        const container = document.getElementById('agentDivisionHierarchyChart');
        if (!container) {
            console.error('agentDivisionHierarchyChart container not found');
            return;
        }

        console.log('agentDivisionHierarchyChart container dimensions:', {
            width: container.offsetWidth,
            height: container.offsetHeight,
            clientWidth: container.clientWidth,
            clientHeight: container.clientHeight
        });

        // Group data by agent and division
        const agentDivisionMap = {};
        const divisions = new Set();
        const agents = new Set();

        divisionData.forEach(item => {
            if (!agentDivisionMap[item.agent_name]) {
                agentDivisionMap[item.agent_name] = {};
            }
            agentDivisionMap[item.agent_name][item.division_name] = item.total_sales;
            divisions.add(item.division_name);
            agents.add(item.agent_name);
        });

        // Convert to arrays
        const divisionNames = Array.from(divisions);
        const agentNames = Array.from(agents);

        // Create traces for each division
        const traces = divisionNames.map(division => {
            const trace = {
                x: agentNames,
                y: agentNames.map(agent => agentDivisionMap[agent][division] || 0),
                name: division,
                type: 'bar',
                hoverinfo: 'name+y',
                hoverlabel: {
                    bgcolor: '#FFF',
                    bordercolor: '#333',
                    font: {
                        size: 14
                    }
                }
            };
            return trace;
        });

        const layout = {
            barmode: 'stack',
            showlegend: true,
            legend: {
                orientation: 'h',
                y: -0.2
            },
            margin: {
                l: 50,
                r: 20,
                t: 30,
                b: 100
            },
            height: 400,
            xaxis: {
                tickangle: -45
            },
            yaxis: {
                title: 'Sales (PKR)'
            },
            clickmode: 'event'
        };

        Plotly.newPlot(container, traces, layout);

        // Add click event to show division details
        container.on('plotly_click', function(data) {
            const divisionName = data.points[0].data.name;
            showDivisionDetails(divisionName);
        });

        // Store chart instance
        agentDivisionHierarchyChart = container;
    }

    // Function to show division details
    function showDivisionDetails(divisionName) {
        console.log(`Showing details for division: ${divisionName}`);

        // Update the division name in the UI
        document.getElementById('selectedDivisionName').textContent = `${divisionName} Division Sales Analysis`;

        // Show the nested charts container
        document.getElementById('nestedDivisionCharts').style.display = 'block';

        // Scroll to the nested charts
        document.getElementById('nestedDivisionCharts').scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Fetch division-specific data
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;

        fetch(`/api/data/division-sales?division=${encodeURIComponent(divisionName)}&start_date=${startDate}&end_date=${endDate}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`Division data received for ${divisionName}:`, data);

                // Render division-specific charts
                renderDivisionProductsChart(data.productSales, divisionName);
                renderDivisionAgentsChart(data.agentSales, divisionName);
                renderDivisionMonthlySalesChart(data.monthlySales, divisionName);
            })
            .catch(error => {
                console.error(`Error fetching data for division ${divisionName}:`, error);

                // Generate fallback data
                const fallbackData = generateFallbackDivisionData(divisionName);
                renderDivisionProductsChart(fallbackData.productSales, divisionName);
                renderDivisionAgentsChart(fallbackData.agentSales, divisionName);
                renderDivisionMonthlySalesChart(fallbackData.monthlySales, divisionName);
            });
    }

    // Function to hide nested charts
    function hideNestedCharts() {
        document.getElementById('nestedDivisionCharts').style.display = 'none';
    }

    // Function to show agent details
    function showAgentDetails(agentName) {
        console.log(`Showing details for agent: ${agentName}`);

        // Filter division data for this agent
        const agentDivisions = globalSalesData.division_by_agent.filter(d => d.agent_name === agentName);

        // Update the top agent's division chart
        renderTopAgentDivisionsChart(agentDivisions, agentName);
    }

    // Function to render division products chart
    function renderDivisionProductsChart(productData, divisionName) {
        const container = document.getElementById('divisionProductsChart');
        if (!container) return;

        // Clear any existing chart
        if (divisionProductsChart) {
            Plotly.purge(container);
        }

        // Sort products by sales
        const sortedProducts = [...productData].sort((a, b) => b.total_sales - a.total_sales);

        // Prepare data for the chart
        const trace = {
            x: sortedProducts.map(p => p.product_name),
            y: sortedProducts.map(p => p.total_sales),
            type: 'bar',
            marker: {
                color: 'rgba(54, 162, 235, 0.7)',
                line: {
                    color: 'rgba(54, 162, 235, 1)',
                    width: 1
                }
            },
            hoverinfo: 'x+y',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            }
        };

        const layout = {
            title: {
                text: `${divisionName} Products by Sales`,
                font: {
                    size: 14
                }
            },
            margin: {
                l: 50,
                r: 20,
                t: 40,
                b: 100
            },
            height: 300,
            xaxis: {
                tickangle: -45
            },
            yaxis: {
                title: 'Sales (PKR)'
            }
        };

        Plotly.newPlot(container, [trace], layout);

        // Store chart instance
        divisionProductsChart = container;
    }

    // Function to render division agents chart
    function renderDivisionAgentsChart(agentData, divisionName) {
        const container = document.getElementById('divisionAgentsChart');
        if (!container) return;

        // Clear any existing chart
        if (divisionAgentsChart) {
            Plotly.purge(container);
        }

        // Sort agents by sales
        const sortedAgents = [...agentData].sort((a, b) => b.total_sales - a.total_sales);

        // Prepare data for the sunburst chart
        const labels = [`${divisionName} Division`];
        const parents = [''];
        const values = [sortedAgents.reduce((sum, agent) => sum + agent.total_sales, 0)];
        const ids = ['total'];
        const colors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
        ];

        // Add agent data
        sortedAgents.forEach((agent, index) => {
            labels.push(agent.agent_name);
            parents.push(`${divisionName} Division`);
            values.push(agent.total_sales);
            ids.push(`agent-${index}`);
        });

        // Create the chart
        const data = {
            type: 'sunburst',
            labels: labels,
            parents: parents,
            values: values,
            ids: ids,
            branchvalues: 'total',
            insidetextorientation: 'radial',
            marker: {
                colors: colors,
                line: {
                    color: 'white',
                    width: 1
                }
            },
            hoverinfo: 'label+value+percent parent',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            },
            textfont: {
                size: 12
            }
        };

        const layout = {
            title: {
                text: `${divisionName} Sales by Agent`,
                font: {
                    size: 16
                }
            },
            margin: {
                l: 0,
                r: 0,
                t: 40,
                b: 0
            },
            height: 350
        };

        Plotly.newPlot(container, [data], layout);

        // Store chart instance
        divisionAgentsChart = container;
    }

    // Function to render division monthly sales chart
    function renderDivisionMonthlySalesChart(monthlyData, divisionName) {
        const container = document.getElementById('divisionMonthlySalesChart');
        if (!container) return;

        // Clear any existing chart
        if (divisionMonthlySalesChart) {
            Plotly.purge(container);
        }

        // Sort monthly data by date
        const sortedMonthlyData = [...monthlyData].sort((a, b) => a.month.localeCompare(b.month));

        // Format month labels
        const monthLabels = sortedMonthlyData.map(m => {
            const [year, month] = m.month.split('-');
            const date = new Date(year, month - 1);
            return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        });

        // Prepare data for the chart
        const trace = {
            x: monthLabels,
            y: sortedMonthlyData.map(m => m.total_sales),
            type: 'scatter',
            mode: 'lines+markers',
            line: {
                color: 'rgba(75, 192, 192, 1)',
                width: 3
            },
            marker: {
                color: 'rgba(75, 192, 192, 1)',
                size: 8
            },
            fill: 'tozeroy',
            fillcolor: 'rgba(75, 192, 192, 0.2)',
            hoverinfo: 'x+y',
            hoverlabel: {
                bgcolor: '#FFF',
                bordercolor: '#333',
                font: {
                    size: 14
                }
            }
        };

        const layout = {
            title: {
                text: `${divisionName} Monthly Sales Trend`,
                font: {
                    size: 14
                }
            },
            margin: {
                l: 50,
                r: 20,
                t: 40,
                b: 50
            },
            height: 300,
            xaxis: {
                title: 'Month'
            },
            yaxis: {
                title: 'Sales (PKR)'
            }
        };

        Plotly.newPlot(container, [trace], layout);

        // Store chart instance
        divisionMonthlySalesChart = container;
    }

    // Organizational structure data
    const organizationalData = {
        divisions: [
        ],
        specialTeams: []
    };

    // Fallback data generator for testing
    function generateFallbackData() {
        console.log('Generating fallback data for charts based on organizational structure');

        // Extract divisions and leaders from organizational data
        const divisions = organizationalData.divisions.map(div => div.name);
        const leaders = organizationalData.divisions.map(div => div.leader.name);

        // Add all team members to the agents list
        const agents = [...leaders];
        organizationalData.divisions.forEach(div => {
            div.members.forEach(member => {
                if (!agents.includes(member.name)) {
                    agents.push(member.name);
                }
            });
        });

        // Add special team members
        organizationalData.specialTeams.forEach(team => {
            if (!agents.includes(team.leader.name)) {
                agents.push(team.leader.name);
            }
            team.members.forEach(member => {
                if (!agents.includes(member.name)) {
                    agents.push(member.name);
                }
            });
        });

        // Generate empty team performance data
        const team_performance = [];

        // Generate empty division by agent data
        const division_by_agent = [];

        console.log('Generated fallback data:', { team_performance, division_by_agent });
        return {
            team_performance,
            division_by_agent
        };
    }

    // Function to generate fallback division data
    function generateFallbackDivisionData(divisionName) {
        // Sample product names based on division
        const products = [];
        for (let i = 1; i <= 5; i++) {
            products.push(`${divisionName} Product ${i}`);
        }

        // Sample agent names
        const agents = [];

        // Generate monthly data for the last 6 months
        const months = [];
        const currentDate = new Date();
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate);
            date.setMonth(currentDate.getMonth() - i);
            months.push(date.toISOString().slice(0, 7)); // Format: YYYY-MM
        }

        return {
            productSales: [],
            agentSales: [],
            monthlySales: []
        };
    }
</script>
{% endblock %}

{% block report_styles %}
<style>
    /* Chart container styles */
    .chart-container {
        position: relative;
        height: 350px;
        width: 100%;
        margin-bottom: 20px;
        border: 1px solid #eee;
        padding: 10px;
        border-radius: 5px;
        background-color: #fff;
    }

    .chart-title {
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
        font-size: 16px;
    }

    /* Print styles */
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }

    /* Make sure canvas elements are visible */
    canvas {
        display: block !important;
        max-width: 100%;
    }

    /* Division detail header styles */
    .division-detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #007bff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .division-detail-header h4 {
        margin: 0;
        color: #007bff;
    }

    /* Nested charts animation */
    #nestedDivisionCharts {
        animation: slideDown 0.5s ease-in-out;
    }

    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printSalesTeamBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}