{% extends 'base.html' %}

{% block title %}Order History - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Order History #{{ order.order_id }}</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Order Details</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Order ID:</th>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <th>Customer:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge
                                            {% if order.status == 'Placed' %}badge-warning
                                            {% elif order.status == 'Approved' %}badge-primary
                                            {% elif order.status == 'Processing' %}badge-info
                                            {% elif order.status == 'Ready for Pickup' %}badge-secondary
                                            {% elif order.status == 'Dispatched' %}badge-dark
                                            {% elif order.status == 'Delivered' %}badge-success
                                            {% elif order.status == 'Cancelled' %}badge-danger
                                            {% elif order.status == 'Pending' %}badge-warning
                                            {% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td>{{ order.last_updated }}</td>
                                </tr>
                                <tr>
                                    <th>Updated By:</th>
                                    <td>{{ order.updated_by }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Order Timeline</h5>
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Order Placed</h6>
                                        <p>{{ order.order_date }}</p>
                                    </div>
                                </div>

                                {% if order.approval_date %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Order Approved</h6>
                                        <p>{{ order.approval_date }} by {{ order.approved_by }}</p>
                                    </div>
                                </div>
                                {% endif %}

                                {% if order.dispatch_date %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-dark"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Order Dispatched</h6>
                                        <p>{{ order.dispatch_date }} with rider {{ order.rider_id }}</p>
                                    </div>
                                </div>
                                {% endif %}

                                {% if order.delivery_date %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Order Delivered</h6>
                                        <p>{{ order.delivery_date }}</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <h5>Order Items</h5>
                    <div class="table-responsive mb-4">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity</th>
                                    <th>FOC Qty</th>
                                    <th>Total Qty</th>
                                    <th>Unit Price</th>
                                    <th>Line Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ item.product_name }}</td>
                                    <td>{{ item.strength }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>
                                        {% if item.foc_quantity and item.foc_quantity > 0 %}
                                            <span class="badge badge-success">{{ item.foc_quantity }}</span>
                                        {% else %}
                                            <span class="text-muted">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ item.quantity + (item.foc_quantity or 0) }}</strong>
                                    </td>
                                    <td>Rs. {{ item.unit_price }}</td>
                                    <td>Rs. {{ item.line_total }}</td>
                                    <td>
                                        <span class="badge
                                            {% if item.status == 'Placed' %}badge-warning
                                            {% elif item.status == 'Approved' %}badge-primary
                                            {% elif item.status == 'Processing' %}badge-info
                                            {% elif item.status == 'Ready for Pickup' %}badge-secondary
                                            {% elif item.status == 'Dispatched' %}badge-dark
                                            {% elif item.status == 'Delivered' %}badge-success
                                            {% elif item.status == 'Cancelled' %}badge-danger
                                            {% elif item.status == 'Pending' %}badge-warning
                                            {% endif %}">
                                            {{ item.status }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-right">Total:</th>
                                    <th>Rs. {{ order.order_amount }}</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <h5>Activity Log</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in activity_logs %}
                                <tr>
                                    <td>{{ log.timestamp }}</td>
                                    <td>{{ log.username }}</td>
                                    <td>{{ log.action }}</td>
                                    <td>{{ log.details }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-right">
                            <a href="{{ url_for('orders') }}" class="btn btn-secondary">Back to Orders</a>
                            {% if order.status in ['Approved', 'Processing', 'Ready for Pickup', 'Dispatched', 'Delivered'] %}
                            <a href="{{ url_for('view_challan', order_id=order.order_id) }}" class="btn btn-primary">
                                <i class="fas fa-truck"></i> View Delivery Challan
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-top: 5px;
    }

    .timeline-item:not(:last-child):before {
        content: '';
        position: absolute;
        left: -23px;
        top: 20px;
        height: calc(100% - 20px);
        width: 2px;
        background-color: #e9ecef;
    }

    .timeline-content {
        margin-bottom: 10px;
    }

    .timeline-title {
        margin-bottom: 5px;
    }
</style>
{% endblock %}
