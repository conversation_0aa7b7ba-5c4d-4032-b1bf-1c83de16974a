{% extends 'base.html' %}

{% block title %}Division Revenue Comparison{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Division Revenue Comparison</h1>
        <a href="{{ url_for('reports') }}" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Reports
        </a>
    </div>

    <!-- Division Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Divisions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ divisions|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_revenue|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Top Division</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ top_division_name }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Growth Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ overall_growth_rate }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Division Performance Chart -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue by Division</h6>
                </div>
                <div class="card-body">
                    <canvas id="divisionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Market Share</h6>
                </div>
                <div class="card-body">
                    <canvas id="marketShareChart" width="400" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Division Comparison Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Division Revenue Comparison</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Division</th>
                            <th>Total Revenue</th>
                            <th>Total Orders</th>
                            <th>Average Order Value</th>
                            <th>Market Share</th>
                            <th>Growth Rate</th>
                            <th>Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for division in divisions %}
                        <tr>
                            <td>
                                {% if loop.index <= 3 %}
                                <span class="badge badge-{% if loop.index == 1 %}warning{% elif loop.index == 2 %}secondary{% else %}dark{% endif %}">
                                    #{{ loop.index }}
                                </span>
                                {% else %}
                                {{ loop.index }}
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ division.division_name }}</strong>
                                {% if loop.index == 1 %}
                                <i class="fas fa-crown text-warning ml-1" title="Top Division"></i>
                                {% endif %}
                            </td>
                            <td>{{ division.total_revenue|format_currency }}</td>
                            <td>{{ division.total_orders }}</td>
                            <td>{{ division.average_order_value|format_currency }}</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{% if division.market_share >= 25 %}success{% elif division.market_share >= 15 %}info{% else %}secondary{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ division.market_share }}%"
                                         aria-valuenow="{{ division.market_share }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ division.market_share }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-{% if division.growth_rate >= 10 %}success{% elif division.growth_rate >= 0 %}info{% else %}danger{% endif %}">
                                    {{ division.growth_rate }}%
                                </span>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{% if division.performance_score >= 90 %}success{% elif division.performance_score >= 70 %}info{% elif division.performance_score >= 50 %}warning{% else %}danger{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ division.performance_score }}%"
                                         aria-valuenow="{{ division.performance_score }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ division.performance_score }}%
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 2, "desc" ]], // Sort by revenue descending
        "pageLength": 25
    });

    // Division Revenue Chart
    const ctx1 = document.getElementById('divisionChart').getContext('2d');
    new Chart(ctx1, {
        type: 'bar',
        data: {
            labels: [{% for division in divisions %}'{{ division.division_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Revenue',
                data: [{% for division in divisions %}{{ division.total_revenue }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Market Share Pie Chart
    const ctx2 = document.getElementById('marketShareChart').getContext('2d');
    new Chart(ctx2, {
        type: 'pie',
        data: {
            labels: [{% for division in divisions %}'{{ division.division_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for division in divisions %}{{ division.market_share }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
{% endblock %}
