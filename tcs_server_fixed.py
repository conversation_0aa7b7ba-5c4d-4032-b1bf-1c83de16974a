"""
CORS-enabled TCS tracking server with enhanced error handling
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import time

# Create Flask app with CORS enabled
app = Flask(__name__)
CORS(app, origins="*")  # Allow all origins for testing

@app.route('/api/track-tcs-public', methods=['POST', 'OPTIONS'])
def track_tcs():
    """CORS-enabled TCS tracking endpoint with enhanced error handling"""
    
    if request.method == 'OPTIONS':
        # Handle preflight request
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, OPTIONS')
        return response
    
    try:
        from tcs_scraper_demo import track_tcs_demo, validate_tracking_number
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'Invalid JSON data'})
            
        tracking_number = data.get('tracking_number')
        
        if not tracking_number:
            return jsonify({'success': False, 'error': 'Tracking number is required'})
        
        # Clean and validate tracking number
        tracking_number = str(tracking_number).strip()
        
        if not validate_tracking_number(tracking_number):
            return jsonify({'success': False, 'error': 'Incorrect Number'})
        
        print(f"📦 Tracking: {tracking_number}")
        start_time = time.time()
        
        # Use demo scraper with enhanced timeout handling
        try:
            tracking_data = track_tcs_demo(tracking_number, headless=True)
        except Exception as scraper_error:
            # Handle scraper-specific errors
            error_msg = str(scraper_error)
            if 'timeout' in error_msg.lower():
                return jsonify({'success': False, 'error': 'Network timeout. Please try again.'})
            elif 'connection' in error_msg.lower():
                return jsonify({'success': False, 'error': 'Network error. Please try again.'})
            else:
                return jsonify({'success': False, 'error': 'System error. Please try again later.'})
        
        duration = time.time() - start_time
        print(f"⏱️  Processing time: {duration:.1f}s")
        
        if tracking_data.get('success', False):
            print(f"✅ Success: {tracking_data.get('current_status')}")
            
            # Ensure all required fields are present
            required_fields = ['tracking_number', 'current_status', 'origin', 'destination', 'booking_date']
            for field in required_fields:
                if field not in tracking_data:
                    tracking_data[field] = 'N/A'
            
            # Ensure track_history is a list
            if 'track_history' not in tracking_data:
                tracking_data['track_history'] = []
            
            return jsonify({'success': True, 'data': tracking_data})
        else:
            error_msg = tracking_data.get('error', 'Failed to fetch tracking information')
            print(f"❌ Failed: {error_msg}")
            
            # Enhanced error categorization
            error_msg_lower = error_msg.lower()
            
            if any(keyword in error_msg_lower for keyword in ['not found', 'invalid', 'incorrect', 'does not exist']):
                categorized_error = 'Incorrect Number'
            elif any(keyword in error_msg_lower for keyword in ['timeout', 'network', 'connection']):
                categorized_error = 'Network error. Please try again.'
            elif any(keyword in error_msg_lower for keyword in ['system', 'server', 'internal']):
                categorized_error = 'System error. Please try again later.'
            else:
                categorized_error = error_msg
            
            return jsonify({'success': False, 'error': categorized_error})
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return jsonify({'success': False, 'error': 'TCS scraper module not available'})
    except Exception as e:
        print(f"❌ System error: {e}")
        return jsonify({'success': False, 'error': f'System error: {str(e)}'})

@app.route('/test')
def test():
    """Test endpoint"""
    return jsonify({
        'message': 'CORS-enabled TCS server working!', 
        'status': 'ok',
        'cors_enabled': True,
        'timestamp': time.time()
    })

@app.route('/health')
def health():
    """Health check endpoint"""
    try:
        from tcs_scraper_demo import validate_tracking_number
        scraper_available = True
    except ImportError:
        scraper_available = False
    
    return jsonify({
        'status': 'healthy',
        'scraper_available': scraper_available,
        'cors_enabled': True,
        'timestamp': time.time()
    })

@app.route('/api/bulk-track', methods=['POST', 'OPTIONS'])
def bulk_track():
    """Bulk tracking endpoint"""
    
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, OPTIONS')
        return response
    
    try:
        from tcs_scraper_demo import track_tcs_demo, validate_tracking_number
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'Invalid JSON data'})
        
        tracking_numbers = data.get('tracking_numbers', [])
        
        if not tracking_numbers or not isinstance(tracking_numbers, list):
            return jsonify({'success': False, 'error': 'tracking_numbers array is required'})
        
        if len(tracking_numbers) > 20:
            return jsonify({'success': False, 'error': 'Maximum 20 tracking numbers allowed'})
        
        results = []
        
        for tracking_number in tracking_numbers:
            tracking_number = str(tracking_number).strip()
            
            if not validate_tracking_number(tracking_number):
                results.append({
                    'tracking_number': tracking_number,
                    'success': False,
                    'error': 'Incorrect Number'
                })
                continue
            
            try:
                tracking_data = track_tcs_demo(tracking_number, headless=True)
                
                if tracking_data.get('success'):
                    results.append({
                        'tracking_number': tracking_number,
                        'success': True,
                        'data': tracking_data
                    })
                else:
                    error_msg = tracking_data.get('error', 'Failed to fetch tracking information')
                    results.append({
                        'tracking_number': tracking_number,
                        'success': False,
                        'error': error_msg
                    })
            except Exception as e:
                results.append({
                    'tracking_number': tracking_number,
                    'success': False,
                    'error': f'Processing error: {str(e)}'
                })
        
        return jsonify({
            'success': True,
            'total': len(tracking_numbers),
            'results': results
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': f'Bulk tracking error: {str(e)}'})

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🚀 Starting CORS-enabled TCS server...")
    print("📱 URL: http://localhost:5001")
    print("🧪 Test endpoint: http://localhost:5001/test")
    print("🏥 Health check: http://localhost:5001/health")
    print("📦 TCS API: http://localhost:5001/api/track-tcs-public")
    print("📋 Bulk API: http://localhost:5001/api/bulk-track")
    print("🌐 CORS enabled for all origins")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5001, debug=True, use_reloader=False)
