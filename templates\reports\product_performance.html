{% extends 'base.html' %}

{% block title %}Product Performance Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Product Performance Report</h4>
                    <div>
                        <button class="btn btn-light" id="printProductPerformanceBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('product_performance_report') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-info mb-0">
                                <strong>Total Sales:</strong> Rs. {{ "%.2f"|format(total_sales) }}
                            </div>
                        </div>
                    </div>

                    <!-- Product Performance Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered" id="productTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity Sold</th>
                                    <th>Total Sales</th>
                                    <th>Order Count</th>
                                    <th>% of Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if products %}
                                    {% for product in products %}
                                    <tr>
                                        <td>{{ product.product_name }}</td>
                                        <td>{{ product.strength }}</td>
                                        <td>{{ product.total_quantity }}</td>
                                        <td>Rs. {{ "%.2f"|format(product.total_sales) }}</td>
                                        <td>{{ product.order_count }}</td>
                                        <td>{{ "%.2f"|format((product.total_sales / total_sales * 100) if total_sales > 0 else 0) }}%</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center">No product data available for the selected period</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Charts -->
                    <div class="row mt-4">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Sales Distribution by Product</h5>
                                </div>
                                <div class="card-body">
                                    <div id="product-sales-chart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Quantity Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <div id="product-quantity-chart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Opportunities and Insights -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Insights & Opportunities</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Top Performing Products</h6>
                                            <ul class="list-group">
                                                {% for product in products[:5] %}
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    {{ product.product_name }} ({{ product.strength }})
                                                    <span class="badge badge-primary badge-pill">Rs. {{ "%.2f"|format(product.total_sales) }}</span>
                                                </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Opportunities</h6>
                                            <div class="alert alert-success">
                                                <ul>
                                                    <li>Consider bundling top products with lower-performing ones</li>
                                                    <li>Analyze seasonal trends for inventory planning</li>
                                                    <li>Review pricing strategy for high-volume products</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#productTable').DataTable({
            "order": [[3, "desc"]], // Sort by total sales by default
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
        
        // Product Sales Chart
        var productNames = [];
        var productSales = [];
        
        {% if products %}
            {% for product in products[:10] %}
                productNames.push("{{ product.product_name }}");
                productSales.push({{ product.total_sales }});
            {% endfor %}
        {% endif %}
        
        var salesData = [{
            type: 'pie',
            labels: productNames,
            values: productSales,
            textinfo: 'label+percent',
            insidetextorientation: 'radial'
        }];
        
        var salesLayout = {
            margin: {t:30, b:30, l:30, r:30},
            showlegend: false
        };
        
        Plotly.newPlot('product-sales-chart', salesData, salesLayout);
        
        // Product Quantity Chart
        var productQuantities = [];
        
        {% if products %}
            {% for product in products[:10] %}
                productQuantities.push({{ product.total_quantity }});
            {% endfor %}
        {% endif %}
        
        var quantityData = [{
            type: 'bar',
            x: productNames,
            y: productQuantities,
            marker: {
                color: 'rgba(50, 171, 96, 0.7)'
            }
        }];
        
        var quantityLayout = {
            margin: {t:30, b:80, l:50, r:30},
            xaxis: {
                tickangle: -45
            },
            yaxis: {
                title: 'Quantity Sold'
            }
        };
        
        Plotly.newPlot('product-quantity-chart', quantityData, quantityLayout);
    });
</script>

<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printProductPerformanceBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .container-fluid, .container-fluid * {
                            visibility: visible;
                        }
                        .container-fluid {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        table {
                            page-break-inside: auto;
                        }
                        tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                    }
                `;
                
                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
