{% extends 'base.html' %}

{% block title %}Customer Purchase History - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Customer Purchase History</h4>
                    <div>
                        <button class="btn btn-light" id="printCustomerHistoryBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form action="{{ url_for('customer_purchase_history') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Customer</span>
                                    </div>
                                    <select name="customer" class="form-control">
                                        <option value="">All Customers</option>
                                        {% for customer_item in customers %}
                                        <option value="{{ customer_item.customer_name }}" {% if selected_customer == customer_item.customer_name %}selected{% endif %}>
                                            {{ customer_item.customer_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-4 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt"></i> Showing data from {{ start_date }} to {{ end_date }}
                            </div>
                        </div>
                    </div>
                    
                    {% if selected_customer %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Customer Summary: {{ selected_customer }}</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <p><strong>Total Orders:</strong> {{ summary.order_count }}</p>
                                            <p><strong>First Order:</strong> {{ summary.first_order }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Total Spent:</strong> {{ "%.2f"|format(summary.total_spent) }} PKR</p>
                                            <p><strong>Last Order:</strong> {{ summary.last_order }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Average Order:</strong> {{ "%.2f"|format(summary.average_order) }} PKR</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Order History</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Payment Method</th>
                                            <th>Sales Agent</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if order_history %}
                                            {% for order in order_history %}
                                            <tr>
                                                <td>{{ order.order_id }}</td>
                                                <td>{{ order.customer_name }}</td>
                                                <td>{{ order.order_date }}</td>
                                                <td>{{ "%.2f"|format(order.order_amount) }}</td>
                                                <td>
                                                    <span class="badge 
                                                        {% if order.status == 'Delivered' %}badge-success
                                                        {% elif order.status == 'Dispatched' %}badge-info
                                                        {% elif order.status == 'Approved' %}badge-primary
                                                        {% elif order.status == 'Placed' %}badge-secondary
                                                        {% else %}badge-warning
                                                        {% endif %}">
                                                        {{ order.status }}
                                                    </span>
                                                </td>
                                                <td>{{ order.payment_method }}</td>
                                                <td>{{ order.sales_agent }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center">No order history available for the selected criteria</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        {% if product_preferences %}
                        <div class="col-md-4">
                            <h5>Product Preferences</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>Spent</th>
                                            <th>Orders</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in product_preferences %}
                                        <tr>
                                            <td>{{ product.product_name }} {% if product.strength %}({{ product.strength }}){% endif %}</td>
                                            <td>{{ product.total_quantity }}</td>
                                            <td>{{ "%.2f"|format(product.total_spent) }}</td>
                                            <td>{{ product.order_count }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printCustomerHistoryBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}