@echo off
setlocal enabledelayedexpansion
title Medivent ERP System - First Time Setup

echo.
echo ================================================================
echo                    MEDIVENT ERP SYSTEM
echo                    First Time Setup Script
echo ================================================================
echo.
echo This script will:
echo   1. Verify Python installation
echo   2. Install all required dependencies
echo   3. Verify application integrity
echo   4. Create necessary directories
echo   5. Initialize the database
echo   6. Test the application
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo [STEP 1/6] Verifying Python Installation...
echo --------------------------------------------------------
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.7 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo ✅ Python !PYTHON_VERSION! is installed
)

echo.
echo [STEP 2/6] Installing Dependencies...
echo --------------------------------------------------------
echo Installing core Flask dependencies...
pip install Flask==2.3.3 Flask-Login==0.6.2 Flask-SQLAlchemy==3.1.1 Flask-WTF==1.2.1 --quiet
if errorlevel 1 (
    echo ❌ Failed to install Flask dependencies
    goto :error_exit
)

echo Installing data processing libraries...
pip install pandas==2.2.3 numpy==2.2.5 xlsxwriter==3.1.9 openpyxl==3.1.2 --quiet
if errorlevel 1 (
    echo ❌ Failed to install data processing libraries
    goto :error_exit
)

echo Installing visualization libraries...
pip install matplotlib==3.10.1 plotly==5.17.0 seaborn==0.13.0 --quiet
if errorlevel 1 (
    echo ❌ Failed to install visualization libraries
    goto :error_exit
)

echo Installing additional dependencies...
pip install reportlab==4.0.7 pillow==11.2.1 bleach==6.1.0 --quiet
if errorlevel 1 (
    echo ❌ Failed to install additional dependencies
    goto :error_exit
)

echo ✅ All dependencies installed successfully

echo.
echo [STEP 3/6] Verifying Application Integrity...
echo --------------------------------------------------------
python -c "import app; print('Application modules verified successfully')" 2>nul
if errorlevel 1 (
    echo ❌ ERROR: Application has syntax errors or missing modules
    echo Please check the application files and try again
    pause
    exit /b 1
) else (
    echo ✅ Application integrity verified
)

echo.
echo [STEP 4/6] Creating Necessary Directories...
echo --------------------------------------------------------
if not exist "instance" mkdir instance
if not exist "static\uploads" mkdir static\uploads
if not exist "static\exports" mkdir static\exports
if not exist "static\charts" mkdir static\charts
if not exist "static\documents" mkdir static\documents
echo ✅ Directory structure created

echo.
echo [STEP 5/6] Database Initialization...
echo --------------------------------------------------------
if exist "instance\medivent.db" (
    echo ✅ Database already exists
) else (
    echo ⚠️  Database will be created on first application run
)

echo.
echo [STEP 6/6] Testing Application Startup...
echo --------------------------------------------------------
echo Testing application import and basic functionality...
python -c "
import sys
sys.path.insert(0, '.')
try:
    import app
    print('✅ Application test successful')
    print('✅ All modules loaded correctly')
    print('✅ Flask application initialized')
except Exception as e:
    print(f'❌ Application test failed: {e}')
    sys.exit(1)
"
if errorlevel 1 (
    echo ❌ Application test failed
    goto :error_exit
)

echo.
echo ================================================================
echo                    SETUP COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo Your Medivent ERP system is now ready to use!
echo.
echo To start the system:
echo   • Double-click 'start_erp.bat' or
echo   • Run 'python run_app.py' from command line
echo.
echo System will be available at: http://localhost:3000
echo.
echo Default login credentials:
echo   Username: admin
echo   Password: admin123
echo.
echo ================================================================
echo.
echo Would you like to start the system now? (Y/N)
set /p choice="Enter your choice: "
if /i "%choice%"=="Y" (
    echo.
    echo Starting Medivent ERP System...
    call start_erp.bat
) else (
    echo.
    echo Setup complete. You can start the system anytime using start_erp.bat
)

goto :end

:error_exit
echo.
echo ================================================================
echo                        SETUP FAILED
echo ================================================================
echo.
echo The setup encountered errors. Please:
echo   1. Check your internet connection
echo   2. Ensure Python is properly installed
echo   3. Run this script as Administrator if needed
echo   4. Contact support if problems persist
echo.
pause
exit /b 1

:end
echo.
echo Press any key to exit...
pause >nul
