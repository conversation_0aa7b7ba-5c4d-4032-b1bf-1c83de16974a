{% extends 'base.html' %}

{% block title %}Customer Order History{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Customer Order History</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View All Customers
                            </a>
                            <a href="{{ url_for('customers', view='by_type') }}" class="btn btn-secondary">
                                <i class="fas fa-user-tag"></i> View Customers by Type
                            </a>
                            <a href="{{ url_for('customers', action='add') }}" class="btn btn-secondary">
                                <i class="fas fa-user-plus"></i> Add New Customer
                            </a>
                            <a href="{{ url_for('customers', view='pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-tags"></i> View Customer Pricing
                            </a>
                            <a href="{{ url_for('customers', action='set_pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-dollar-sign"></i> Set Customer Pricing
                            </a>
                        </div>
                    </div>
                    
                    {% if customer %}
                        <!-- Customer-specific order history -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">Order History for {{ customer.name }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="thead-dark">
                                                    <tr>
                                                        <th>Order ID</th>
                                                        <th>Date</th>
                                                        <th>Amount</th>
                                                        <th>Status</th>
                                                        <th>Payment Method</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% if orders %}
                                                        {% for order in orders %}
                                                        <tr>
                                                            <td>{{ order.order_id }}</td>
                                                            <td>{{ order.order_date }}</td>
                                                            <td>{{ order.order_amount }}</td>
                                                            <td>
                                                                <span class="badge 
                                                                    {% if order.status == 'Delivered' %}badge-success
                                                                    {% elif order.status == 'Cancelled' %}badge-danger
                                                                    {% elif order.status == 'Dispatched' %}badge-info
                                                                    {% elif order.status == 'Placed' %}badge-warning
                                                                    {% else %}badge-secondary{% endif %}">
                                                                    {{ order.status }}
                                                                </span>
                                                            </td>
                                                            <td>{{ order.payment_method }}</td>
                                                            <td>
                                                                <a href="{{ url_for('view_order_history', order_id=order.order_id) }}" class="btn btn-sm btn-info">
                                                                    <i class="fas fa-history"></i> View Details
                                                                </a>
                                                                {% if order.status in ['Approved', 'Processing', 'Ready for Pickup', 'Dispatched', 'Delivered'] %}
                                                                <a href="{{ url_for('view_invoice', order_id=order.order_id) }}" class="btn btn-sm btn-secondary">
                                                                    <i class="fas fa-file-invoice"></i> Invoice
                                                                </a>
                                                                {% endif %}
                                                                {% if order.status in ['Dispatched', 'Delivered'] %}
                                                                <a href="{{ url_for('view_challan', order_id=order.order_id) }}" class="btn btn-sm btn-secondary">
                                                                    <i class="fas fa-truck"></i> Challan
                                                                </a>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    {% else %}
                                                        <tr>
                                                            <td colspan="6" class="text-center">No orders found for this customer</td>
                                                        </tr>
                                                    {% endif %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Customer selection form -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">Select Customer</h5>
                                    </div>
                                    <div class="card-body">
                                        <form method="get" action="{{ url_for('customers', view='orders') }}">
                                            <div class="form-group">
                                                <label for="customer_id">Customer</label>
                                                <select class="form-control" id="customer_id" name="customer_id" required onchange="this.form.submit()">
                                                    <option value="">-- Select Customer --</option>
                                                    {% for customer in customers %}
                                                    <option value="{{ customer.customer_id }}">{{ customer.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <button type="submit" class="btn btn-primary">View Order History</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
