{% extends "base.html" %}

{% block title %}Invoice Details - {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-file-invoice-dollar text-primary"></i>
                        Invoice Details - {{ invoice.invoice_number }}
                    </h4>
                    <div>
                        <a href="{{ url_for('finance_invoices') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Invoices
                        </a>
                        <button class="btn btn-primary" id="printInvoiceDetailBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Company Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h3 class="text-primary">Medivent Pharmaceuticals Pvt. Ltd.</h3>
                            <p class="mb-1">123 Business District, Karachi</p>
                            <p class="mb-1">Phone: +92-21-1234567</p>
                            <p class="mb-1">Email: <EMAIL></p>
                            <p class="mb-0">NTN: 1234567-8</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h4 class="text-primary">INVOICE</h4>
                            <p class="mb-1"><strong>Invoice #:</strong> {{ invoice.invoice_number }}</p>
                            <p class="mb-1"><strong>Order ID:</strong> {{ invoice.order_id }}</p>
                            <p class="mb-1"><strong>Date:</strong> {{ invoice.date_generated|format_datetime }}</p>
                            <p class="mb-0"><strong>Division:</strong> {{ invoice.division or 'N/A' }}</p>
                        </div>
                    </div>
                    
                    <!-- Customer Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-secondary">Bill To:</h5>
                            <div class="border p-3 bg-light">
                                <p class="mb-1"><strong>{{ invoice.customer_name }}</strong></p>
                                <p class="mb-1">{{ invoice.customer_address or 'Address not available' }}</p>
                                <p class="mb-0">{{ invoice.customer_phone or 'Phone not available' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-secondary">Order Information:</h5>
                            <div class="border p-3 bg-light">
                                <p class="mb-1"><strong>PO Number:</strong> {{ invoice.po_number or 'N/A' }}</p>
                                <p class="mb-1"><strong>Order Date:</strong> {{ invoice.order_date[:10] if invoice.order_date else 'N/A' }}</p>
                                <p class="mb-0"><strong>Generated By:</strong> {{ invoice.generated_by or 'System' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Invoice Items -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-secondary">Invoice Items:</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>Product</th>
                                            <th class="text-center">Quantity</th>
                                            <th class="text-end">Unit Price</th>
                                            <th class="text-end">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in order_items %}
                                        <tr>
                                            <td>{{ item.product_name }}</td>
                                            <td class="text-center">{{ item.quantity }}</td>
                                            <td class="text-end">{{ item.unit_price|format_currency }}</td>
                                            <td class="text-end">{{ (item.quantity * item.unit_price)|format_currency }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financial Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Payment Terms -->
                            <h5 class="text-secondary">Payment Terms:</h5>
                            <div class="border p-3 bg-light">
                                <p class="mb-1"><strong>Payment Method:</strong> Cash</p>
                                <p class="mb-1"><strong>Terms:</strong> Net 30 Days</p>
                                <p class="mb-0"><strong>Due Date:</strong> {{ (invoice.date_generated | string)[:10] if invoice.date_generated else 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Financial Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-8"><strong>Total Amount:</strong></div>
                                        <div class="col-4 text-end">{{ invoice.total_amount|format_currency }}</div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-8">Paid Amount:</div>
                                        <div class="col-4 text-end text-success">{{ paid_amount|format_currency }}</div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-8"><strong>Outstanding:</strong></div>
                                        <div class="col-4 text-end">
                                            <strong class="{% if outstanding_amount > 0 %}text-danger{% else %}text-success{% endif %}">
                                                {{ outstanding_amount|format_currency }}
                                            </strong>
                                        </div>
                                    </div>
                                    {% if outstanding_amount > 0 %}
                                    <hr>
                                    <div class="text-center">
                                        <a href="{{ url_for('finance') }}?view=payments&invoice_id={{ invoice.invoice_number }}"
                                           class="btn btn-success">
                                            <i class="fas fa-money-bill"></i> Record Payment
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment History -->
                    {% if payments %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-history"></i> Payment History
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Payment ID</th>
                                                    <th>Date</th>
                                                    <th>Method</th>
                                                    <th>Amount</th>
                                                    <th>Reference</th>
                                                    <th>Notes</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for payment in payments %}
                                                <tr>
                                                    <td><strong class="text-primary">{{ payment.payment_id }}</strong></td>
                                                    <td>{{ payment.payment_date|format_datetime }}</td>
                                                    <td>
                                                        <span class="badge badge-{% if payment.payment_method == 'Cash' %}success{% elif payment.payment_method == 'Cheque' %}warning{% else %}info{% endif %}">
                                                            {{ payment.payment_method }}
                                                        </span>
                                                    </td>
                                                    <td>{{ payment.amount|format_currency }}</td>
                                                    <td>{{ payment.reference_number or 'N/A' }}</td>
                                                    <td>{{ payment.notes or 'N/A' }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Footer -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="text-center text-muted">
                                <p class="mb-1">Thank you for your business!</p>
                                <p class="mb-0">This is a computer-generated invoice.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }
    
    .container-fluid {
        margin: 0;
        padding: 0;
    }
    
    .card {
        border: none;
        box-shadow: none;
    }
    
    .card-header {
        background: white !important;
        border: none;
    }
    
    body {
        font-size: 12px;
    }
    
    h3, h4, h5 {
        font-size: 14px;
    }
    
    .table {
        font-size: 11px;
    }
}
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printInvoiceDetailBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}