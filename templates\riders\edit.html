{% extends 'base.html' %}

{% block title %}Edit Rider - {{ rider.name }} - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-primary"></i> Edit Rider - {{ rider.name }}
        </h1>
        <div>
            <a href="{{ url_for('view_rider', rider_id=rider.rider_id) }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to View
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Edit Rider Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('edit_rider', rider_id=rider.rider_id) }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="font-weight-bold">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ rider.name }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email" class="font-weight-bold">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ rider.email or '' }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="phone" class="font-weight-bold">Phone Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="{{ rider.phone }}" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_type" class="font-weight-bold">Vehicle Type</label>
                                    <select class="form-control" id="vehicle_type" name="vehicle_type">
                                        <option value="Motorcycle" {{ 'selected' if rider.vehicle_type == 'Motorcycle' else '' }}>Motorcycle</option>
                                        <option value="Bike" {{ 'selected' if rider.vehicle_type == 'Bike' else '' }}>Bike</option>
                                        <option value="Car" {{ 'selected' if rider.vehicle_type == 'Car' else '' }}>Car</option>
                                        <option value="Van" {{ 'selected' if rider.vehicle_type == 'Van' else '' }}>Van</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="status" class="font-weight-bold">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="Active" {{ 'selected' if rider.status == 'Active' else '' }}>Active</option>
                                        <option value="pending" {{ 'selected' if rider.status == 'pending' else '' }}>Pending</option>
                                        <option value="Suspended" {{ 'selected' if rider.status == 'Suspended' else '' }}>Suspended</option>
                                        <option value="Inactive" {{ 'selected' if rider.status == 'Inactive' else '' }}>Inactive</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="current_location" class="font-weight-bold">Current Location</label>
                                    <input type="text" class="form-control" id="current_location" name="current_location" 
                                           value="{{ rider.current_location or '' }}" 
                                           placeholder="e.g., Karachi, Lahore, Islamabad">
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="vehicle_number" class="font-weight-bold">Vehicle Number</label>
                                    <input type="text" class="form-control" id="vehicle_number" name="vehicle_number" 
                                           value="{{ rider.vehicle_number or '' }}" 
                                           placeholder="e.g., ABC-123">
                                    <small class="form-text text-muted">Vehicle registration number</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="license_number" class="font-weight-bold">License Number</label>
                                    <input type="text" class="form-control" id="license_number" name="license_number" 
                                           value="{{ rider.license_number or '' }}" 
                                           placeholder="e.g., LIC123456">
                                    <small class="form-text text-muted">Driving license number</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="max_orders_per_day" class="font-weight-bold">Max Orders Per Day</label>
                                    <input type="number" class="form-control" id="max_orders_per_day" name="max_orders_per_day" 
                                           value="{{ rider.max_orders_per_day or 10 }}" min="1" max="50">
                                    <small class="form-text text-muted">Maximum orders this rider can handle per day</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Rider
                            </button>
                            <a href="{{ url_for('view_rider', rider_id=rider.rider_id) }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Rider Info Summary -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle bg-primary text-white mx-auto mb-2" style="width: 60px; height: 60px; line-height: 60px; font-size: 24px;">
                            {{ rider.name[0].upper() if rider.name else 'R' }}
                        </div>
                        <h6 class="font-weight-bold">{{ rider.name }}</h6>
                        <span class="badge badge-{{ 'success' if rider.status == 'Active' else 'warning' if rider.status == 'pending' else 'danger' }}">
                            {{ rider.status }}
                        </span>
                    </div>
                    
                    <hr>
                    
                    <div class="small">
                        <div class="mb-2">
                            <strong>Rider ID:</strong> {{ rider.rider_id }}
                        </div>
                        <div class="mb-2">
                            <strong>Phone:</strong> {{ rider.phone }}
                        </div>
                        <div class="mb-2">
                            <strong>Vehicle:</strong> {{ rider.vehicle_type }}
                        </div>
                        <div class="mb-2">
                            <strong>Location:</strong> {{ rider.current_location or 'Not set' }}
                        </div>
                        <div class="mb-2">
                            <strong>Total Deliveries:</strong> {{ rider.total_deliveries or 0 }}
                        </div>
                        <div class="mb-2">
                            <strong>Rating:</strong> 
                            <span class="text-warning">
                                {% for i in range((rider.rating or 0)|int) %}
                                    <i class="fas fa-star"></i>
                                {% endfor %}
                                {% for i in range(5 - ((rider.rating or 0)|int)) %}
                                    <i class="far fa-star"></i>
                                {% endfor %}
                            </span>
                            ({{ "%.1f"|format(rider.rating or 0) }})
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('view_rider', rider_id=rider.rider_id) }}" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        
                        {% if rider.status == 'pending' %}
                        <button class="btn btn-success btn-sm" onclick="approveRider('{{ rider.rider_id }}')">
                            <i class="fas fa-check"></i> Approve Rider
                        </button>
                        {% endif %}
                        
                        {% if rider.status == 'Active' %}
                        <button class="btn btn-warning btn-sm" onclick="suspendRider('{{ rider.rider_id }}')">
                            <i class="fas fa-ban"></i> Suspend Rider
                        </button>
                        {% endif %}
                        
                        <a href="{{ url_for('riders') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-list"></i> All Riders
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function approveRider(riderId) {
    if (confirm('Are you sure you want to approve this rider?')) {
        fetch(`/riders/${riderId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error approving rider', 'error');
        });
    }
}

function suspendRider(riderId) {
    if (confirm('Are you sure you want to suspend this rider?')) {
        fetch(`/riders/${riderId}/suspend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'warning');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error suspending rider', 'error');
        });
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>
{% endblock %}
