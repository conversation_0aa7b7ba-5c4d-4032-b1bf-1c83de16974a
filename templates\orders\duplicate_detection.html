{% extends "base.html" %}

{% block title %}Duplicate Order Detection - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .duplicate-dashboard {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .duplicate-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .duplicate-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .duplicate-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 5px solid;
        transition: all 0.3s ease;
    }

    .duplicate-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .confidence-high { border-left-color: #dc3545; }
    .confidence-very-high { border-left-color: #6f42c1; }
    .confidence-medium { border-left-color: #ffc107; }
    .confidence-low { border-left-color: #28a745; }

    .confidence-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .confidence-high { background: #f8d7da; color: #721c24; }
    .confidence-very-high { background: #e2e3f3; color: #383d41; }
    .confidence-medium { background: #fff3cd; color: #856404; }
    .confidence-low { background: #d4edda; color: #155724; }

    .order-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
    }

    .order-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
    }

    .order-details h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .detail-item {
        display: flex;
        justify-content: between;
        margin-bottom: 5px;
    }

    .detail-label {
        font-weight: 500;
        color: #6c757d;
        min-width: 100px;
    }

    .detail-value {
        color: #495057;
        font-weight: 600;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .btn-resolve {
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-cancel {
        background: #dc3545;
        color: white;
        border: none;
    }

    .btn-cancel:hover {
        background: #c82333;
        color: white;
    }

    .btn-not-duplicate {
        background: #28a745;
        color: white;
        border: none;
    }

    .btn-not-duplicate:hover {
        background: #218838;
        color: white;
    }

    .btn-view {
        background: #007bff;
        color: white;
        border: none;
    }

    .btn-view:hover {
        background: #0056b3;
        color: white;
    }

    .filters-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .duplicate-type-tabs {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 10px;
        margin-right: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #ffecd2, #fcb69f);
        color: #495057;
    }

    .tab-content {
        margin-top: 20px;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>

<div class="duplicate-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="duplicate-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="duplicate-title">
                        <i class="fas fa-search-plus me-3"></i>Duplicate Order Detection
                    </h1>
                    <p class="text-white-50 mb-0">Advanced algorithms to detect and resolve duplicate orders</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="runDetection()">
                        <i class="fas fa-sync-alt me-2"></i>Re-scan
                    </button>
                    <button class="btn btn-light btn-modern" onclick="exportDuplicates()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <a href="/orders" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left me-2"></i>Back to Orders
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-warning">{{ total_duplicates }}</div>
                    <div class="stat-label">Potential Duplicates</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-danger">₹{{ "{:,.0f}".format(total_potential_savings) }}</div>
                    <div class="stat-label">Potential Savings</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-info">{{ duplicate_types.customer_product|length }}</div>
                    <div class="stat-label">Customer Duplicates</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-success">{{ duplicate_types.cross_salesperson|length }}</div>
                    <div class="stat-label">Cross-Agent Duplicates</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Filters & Search
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" id="customerFilter" placeholder="Search customer...">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Confidence Level</label>
                    <select class="form-control" id="confidenceFilter">
                        <option value="">All Confidence Levels</option>
                        <option value="Very High">Very High</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Duplicate Type</label>
                    <select class="form-control" id="typeFilter">
                        <option value="">All Types</option>
                        <option value="customer_product">Customer Product</option>
                        <option value="salesperson_duplicate">Salesperson</option>
                        <option value="cross_salesperson">Cross-Salesperson</option>
                        <option value="time_based">Time-based</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Days Apart</label>
                    <select class="form-control" id="daysFilter">
                        <option value="">All Ranges</option>
                        <option value="0-1">Same Day - 1 Day</option>
                        <option value="1-3">1-3 Days</option>
                        <option value="3-7">3-7 Days</option>
                        <option value="7+">7+ Days</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Duplicate Detection Results -->
        <div class="duplicate-type-tabs">
            <ul class="nav nav-tabs" id="duplicateTypeTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        All Duplicates ({{ total_duplicates }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer" type="button" role="tab">
                        Customer Duplicates ({{ duplicate_types.customer_product|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="salesperson-tab" data-bs-toggle="tab" data-bs-target="#salesperson" type="button" role="tab">
                        Salesperson ({{ duplicate_types.salesperson_duplicate|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cross-tab" data-bs-toggle="tab" data-bs-target="#cross" type="button" role="tab">
                        Cross-Agent ({{ duplicate_types.cross_salesperson|length }})
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="duplicateTypeTabContent">
                <!-- All Duplicates Tab -->
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    {% if duplicates %}
                        {% for duplicate in duplicates %}
                        <div class="duplicate-card confidence-{{ duplicate.confidence.lower().replace(' ', '-') }}" 
                             data-customer="{{ duplicate.customer_name }}" 
                             data-confidence="{{ duplicate.confidence }}"
                             data-type="{{ duplicate.duplicate_type }}"
                             data-days="{{ duplicate.days_apart }}">
                            
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">{{ duplicate.reason }}</h5>
                                    <span class="confidence-badge confidence-{{ duplicate.confidence.lower().replace(' ', '-') }}">
                                        {{ duplicate.confidence }} Confidence
                                    </span>
                                </div>
                                <div class="text-end">
                                    <div class="text-muted small">{{ "{:.1f}".format(duplicate.days_apart) }} days apart</div>
                                    <div class="fw-bold text-danger">₹{{ "{:,.2f}".format(duplicate.total_amount) }} at risk</div>
                                </div>
                            </div>

                            <div class="order-comparison">
                                <div class="order-details">
                                    <h6><i class="fas fa-file-alt me-2"></i>Order #{{ duplicate.order1_id }}</h6>
                                    <div class="detail-item">
                                        <span class="detail-label">Customer:</span>
                                        <span class="detail-value">{{ duplicate.customer_name }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Date:</span>
                                        <span class="detail-value">{{ duplicate.date1 }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Amount:</span>
                                        <span class="detail-value">₹{{ "{:,.2f}".format(duplicate.amount1) }}</span>
                                    </div>
                                    {% if duplicate.get('agent1') %}
                                    <div class="detail-item">
                                        <span class="detail-label">Agent:</span>
                                        <span class="detail-value">{{ duplicate.agent1 }}</span>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="order-details">
                                    <h6><i class="fas fa-file-alt me-2"></i>Order #{{ duplicate.order2_id }}</h6>
                                    <div class="detail-item">
                                        <span class="detail-label">Customer:</span>
                                        <span class="detail-value">{{ duplicate.customer_name }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Date:</span>
                                        <span class="detail-value">{{ duplicate.date2 }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Amount:</span>
                                        <span class="detail-value">₹{{ "{:,.2f}".format(duplicate.amount2) }}</span>
                                    </div>
                                    {% if duplicate.get('agent2') %}
                                    <div class="detail-item">
                                        <span class="detail-label">Agent:</span>
                                        <span class="detail-value">{{ duplicate.agent2 }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button class="btn btn-resolve btn-cancel" 
                                        onclick="resolveDuplicate('{{ duplicate.order1_id }}', '{{ duplicate.order2_id }}', 'cancel_duplicate')">
                                    <i class="fas fa-times me-1"></i>Cancel Duplicate
                                </button>
                                <button class="btn btn-resolve btn-not-duplicate" 
                                        onclick="resolveDuplicate('{{ duplicate.order1_id }}', '{{ duplicate.order2_id }}', 'mark_not_duplicate')">
                                    <i class="fas fa-check me-1"></i>Not Duplicate
                                </button>
                                <button class="btn btn-resolve btn-view" 
                                        onclick="viewOrderComparison('{{ duplicate.order1_id }}', '{{ duplicate.order2_id }}')">
                                    <i class="fas fa-eye me-1"></i>Compare Details
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-check-circle text-success"></i>
                            <h4>No Duplicates Found</h4>
                            <p>Great! No potential duplicate orders were detected in your system.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Other tabs would have similar structure but filtered by type -->
                <div class="tab-pane fade" id="customer" role="tabpanel">
                    <!-- Customer duplicate content -->
                </div>
                <div class="tab-pane fade" id="salesperson" role="tabpanel">
                    <!-- Salesperson duplicate content -->
                </div>
                <div class="tab-pane fade" id="cross" role="tabpanel">
                    <!-- Cross-salesperson duplicate content -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resolveDuplicate(order1Id, order2Id, action) {
    const actionText = action === 'cancel_duplicate' ? 'cancel the duplicate order' : 'mark as not duplicate';
    
    if (confirm(`Are you sure you want to ${actionText}?`)) {
        const notes = prompt('Add resolution notes (optional):') || '';
        
        fetch('/orders/api/resolve-duplicate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order1_id: order1Id,
                order2_id: order2Id,
                action: action,
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resolving duplicate.');
        });
    }
}

function viewOrderComparison(order1Id, order2Id) {
    window.open(`/orders/compare?order1=${order1Id}&order2=${order2Id}`, '_blank');
}

function runDetection() {
    if (confirm('Re-run duplicate detection? This may take a few moments.')) {
        location.reload();
    }
}

function exportDuplicates() {
    window.open('/orders/export/duplicates?format=excel', '_blank');
}

// Initialize filters
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality would be implemented here
});
</script>

{% endblock %}
