{% extends 'base.html' %}

{% block title %}Delivery Performance Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Delivery Performance Report</h4>
                    <div>
                        <button class="btn btn-light" id="printDeliveryPerformanceBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('delivery_performance_report') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt"></i> Showing data from {{ start_date }} to {{ end_date }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Delivery Performance Summary</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <p><strong>Delivered Orders:</strong> {{ summary.delivered_orders }}</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Avg. Processing Time:</strong> {{ "%.1f"|format(summary.avg_processing_time or 0) }} days</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Avg. Delivery Time:</strong> {{ "%.1f"|format(summary.avg_delivery_time or 0) }} days</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p><strong>Avg. Total Time:</strong> {{ "%.1f"|format(summary.avg_total_time or 0) }} days</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Delivery Data</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Order Date</th>
                                            <th>Dispatch Date</th>
                                            <th>Delivery Date</th>
                                            <th>Processing Time</th>
                                            <th>Delivery Time</th>
                                            <th>Total Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if delivery_data %}
                                            {% for delivery in delivery_data %}
                                            <tr>
                                                <td>{{ delivery.order_id }}</td>
                                                <td>{{ delivery.customer_name }}</td>
                                                <td>{{ delivery.order_date }}</td>
                                                <td>{{ delivery.dispatch_date }}</td>
                                                <td>{{ delivery.delivery_date }}</td>
                                                <td>{{ "%.1f"|format(delivery.processing_time) }} days</td>
                                                <td>{{ "%.1f"|format(delivery.delivery_time) }} days</td>
                                                <td>{{ "%.1f"|format(delivery.total_time) }} days</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="8" class="text-center">No delivery data available for the selected period</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <h5>Rider Performance</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Rider ID</th>
                                            <th>Deliveries</th>
                                            <th>Avg. Delivery Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if rider_performance %}
                                            {% for rider in rider_performance %}
                                            <tr>
                                                <td>{{ rider.rider_id }}</td>
                                                <td>{{ rider.delivered_orders }}</td>
                                                <td>{{ "%.1f"|format(rider.avg_delivery_time) }} days</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center">No rider performance data available</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printDeliveryPerformanceBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}