{% extends 'base.html' %}

{% block title %}Sales Analytics - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Sales Analytics Dashboard</h4>
                    <div>
                        <button class="btn btn-light" id="printSalesAnalyticsBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Date Range Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('sales_analytics') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">Period</span>
                                    </div>
                                    <select class="form-control" name="date_range" onchange="this.form.submit()">
                                        <option value="this_month" {% if date_range == 'this_month' %}selected{% endif %}>This Month</option>
                                        <option value="last_month" {% if date_range == 'last_month' %}selected{% endif %}>Last Month</option>
                                        <option value="this_quarter" {% if date_range == 'this_quarter' %}selected{% endif %}>This Quarter</option>
                                        <option value="this_year" {% if date_range == 'this_year' %}selected{% endif %}>This Year</option>
                                        <option value="last_year" {% if date_range == 'last_year' %}selected{% endif %}>Last Year</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <small class="text-muted">Period: {{ start_date }} to {{ end_date }}</small>
                        </div>
                    </div>
                    
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h2>{{ sales_data|length }}</h2>
                                    <p class="mb-0">Sales Days</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h2>₨{{ "{:,.0f}".format(sales_data|sum(attribute='daily_sales')|default(0)) }}</h2>
                                    <p class="mb-0">Total Sales</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h2>{{ sales_data|sum(attribute='order_count')|default(0) }}</h2>
                                    <p class="mb-0">Total Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h2>₨{{ "{:,.0f}".format((sales_data|sum(attribute='daily_sales')|default(1)) / (sales_data|sum(attribute='order_count')|default(1))) }}</h2>
                                    <p class="mb-0">Avg Order Value</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Daily Sales Trend</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="dailySalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Division Sales</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="divisionSalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Charts Row -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">Top Products</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="topProductsChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">Payment Methods</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="paymentMethodsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sales Data Table -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Daily Sales Details</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered" id="salesTable">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Date</th>
                                            <th>Orders</th>
                                            <th>Daily Sales</th>
                                            <th>Avg Order Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if sales_data %}
                                            {% for day in sales_data %}
                                            <tr>
                                                <td>{{ day.sale_date }}</td>
                                                <td>{{ day.order_count }}</td>
                                                <td>₨{{ "{:,.2f}".format(day.daily_sales|default(0)) }}</td>
                                                <td>₨{{ "{:,.2f}".format((day.daily_sales|default(0) / day.order_count|default(1)) if day.order_count else 0) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center">No sales data found for the selected period</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#salesTable').DataTable({
        "order": [[ 0, "desc" ]], // Sort by date descending
        "pageLength": 10,
        "responsive": true
    });

    // Daily Sales Line Chart
    var ctx1 = document.getElementById('dailySalesChart').getContext('2d');
    var dailySalesChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: [{% for day in sales_data %}'{{ day.sale_date }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Daily Sales',
                data: [{% for day in sales_data %}{{ day.daily_sales|default(0) }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₨' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Sales: ₨' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Division Sales Pie Chart
    var ctx2 = document.getElementById('divisionSalesChart').getContext('2d');
    var divisionSalesChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: [{% for div in division_sales %}'{{ div.division_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for div in division_sales %}{{ div.total_sales|default(0) }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ₨' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Top Products Bar Chart
    var ctx3 = document.getElementById('topProductsChart').getContext('2d');
    var topProductsChart = new Chart(ctx3, {
        type: 'bar',
        data: {
            labels: [{% for prod in top_products %}'{{ prod.product_name[:20] }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Sales',
                data: [{% for prod in top_products %}{{ prod.total_sales|default(0) }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: '#ffc107'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₨' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Sales: ₨' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Payment Methods Pie Chart
    var ctx4 = document.getElementById('paymentMethodsChart').getContext('2d');
    var paymentMethodsChart = new Chart(ctx4, {
        type: 'pie',
        data: {
            labels: [{% for pm in payment_methods %}'{{ pm.payment_method|title }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for pm in payment_methods %}{{ pm.total_amount|default(0) }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#dc3545', '#28a745', '#007bff', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ₨' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>

<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printSalesAnalyticsBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Add print styles for A4 page formatting
                const printStyles = `
                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body * {
                            visibility: hidden;
                        }
                        .container-fluid, .container-fluid * {
                            visibility: visible;
                        }
                        .container-fluid {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                        }
                        .card-header, .btn, .no-print {
                            display: none !important;
                        }
                        table {
                            page-break-inside: auto;
                        }
                        tr {
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }
                    }
                `;
                
                // Add styles to head
                const styleSheet = document.createElement('style');
                styleSheet.type = 'text/css';
                styleSheet.innerText = printStyles;
                document.head.appendChild(styleSheet);
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                        document.head.removeChild(styleSheet);
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}
