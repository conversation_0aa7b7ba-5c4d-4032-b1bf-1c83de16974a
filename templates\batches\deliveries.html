{% extends 'base.html' %}

{% block title %}Batch Deliveries{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Batch Deliveries</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('batches') }}" class="btn btn-secondary">
                                <i class="fas fa-list"></i> View All Batches
                            </a>
                            <a href="{{ url_for('batches', view='by_product') }}" class="btn btn-secondary">
                                <i class="fas fa-pills"></i> View Batches by Product
                            </a>
                            <a href="{{ url_for('batches', view='details') }}" class="btn btn-secondary">
                                <i class="fas fa-info-circle"></i> View Batch Details
                            </a>
                            <a href="{{ url_for('batches', view='movements') }}" class="btn btn-secondary">
                                <i class="fas fa-exchange-alt"></i> View Batch Movements
                            </a>
                            <a href="{{ url_for('batches', view='expiring') }}" class="btn btn-secondary">
                                <i class="fas fa-exclamation-triangle"></i> View Expiring Batches
                            </a>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#recordDeliveryModal">
                                <i class="fas fa-shipping-fast"></i> Record Batch Delivery
                            </button>
                        </div>
                    </div>
                    
                    {% if batch_id %}
                    <!-- Batch-specific deliveries -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Deliveries for Batch #{{ batch.batch_number }} - {{ batch.product_name }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Delivery ID</th>
                                                    <th>Order ID</th>
                                                    <th>Customer</th>
                                                    <th>Date</th>
                                                    <th>Quantity</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if deliveries %}
                                                    {% for delivery in deliveries %}
                                                    <tr>
                                                        <td>{{ delivery.delivery_id }}</td>
                                                        <td>{{ delivery.order_id }}</td>
                                                        <td>{{ delivery.customer_name }}</td>
                                                        <td>{{ delivery.delivery_date }}</td>
                                                        <td>{{ delivery.quantity }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="5" class="text-center">No deliveries found for this batch</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <!-- All batch deliveries -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Delivery ID</th>
                                    <th>Product</th>
                                    <th>Batch Number</th>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if deliveries %}
                                    {% for delivery in deliveries %}
                                    <tr>
                                        <td>{{ delivery.delivery_id }}</td>
                                        <td>{{ delivery.product_name }}</td>
                                        <td>{{ delivery.batch_number }}</td>
                                        <td>{{ delivery.order_id }}</td>
                                        <td>{{ delivery.customer_name }}</td>
                                        <td>{{ delivery.delivery_date }}</td>
                                        <td>{{ delivery.quantity }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center">No batch deliveries found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Record Delivery Modal -->
<div class="modal fade" id="recordDeliveryModal" tabindex="-1" role="dialog" aria-labelledby="recordDeliveryModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="recordDeliveryModalLabel">Record Batch Delivery</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{{ url_for('batches', action='delivery') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="batch_id">Batch</label>
                        <select class="form-control" id="batch_id" name="batch_id" required>
                            <option value="">-- Select Batch --</option>
                            {% for batch in batches %}
                            <option value="{{ batch.batch_id }}">{{ batch.product_name }} - {{ batch.batch_number }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="order_id">Order</label>
                        <select class="form-control" id="order_id" name="order_id" required>
                            <option value="">-- Select Order --</option>
                            {% for order in orders %}
                            <option value="{{ order.order_id }}">{{ order.order_id }} - {{ order.customer }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" required>
                    </div>
                    <div class="form-group">
                        <label for="delivery_date">Delivery Date</label>
                        <input type="date" class="form-control" id="delivery_date" name="delivery_date" value="{{ now.strftime('%Y-%m-%d') }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Record Delivery</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
