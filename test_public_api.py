"""
Test the public TCS API endpoint
"""

import requests
import json
import time

def test_public_api():
    """Test the public API endpoint"""
    
    url = 'http://localhost:3000/api/track-tcs-public'
    
    test_numbers = [
        '31442084041',
        '31442084039', 
        '31442083394',
        '31442083525'
    ]
    
    print("🚀 TESTING PUBLIC TCS API")
    print("=" * 50)
    
    for i, tracking_number in enumerate(test_numbers, 1):
        print(f"\n📦 Test {i}/{len(test_numbers)}: {tracking_number}")
        
        try:
            # Make API request
            response = requests.post(
                url,
                json={'tracking_number': tracking_number},
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Success: {data.get('success', False)}")
                    
                    if data.get('success'):
                        tracking_data = data.get('data', {})
                        print(f"   Status: {tracking_data.get('current_status', 'Unknown')}")
                        print(f"   Route: {tracking_data.get('origin', 'N/A')} → {tracking_data.get('destination', 'N/A')}")
                        print(f"   History: {len(tracking_data.get('track_history', []))} entries")
                        print("   ✅ API WORKING!")
                    else:
                        print(f"   Error: {data.get('error', 'Unknown error')}")
                        print("   ❌ API returned error")
                        
                except json.JSONDecodeError:
                    print("   ❌ Invalid JSON response")
                    print(f"   Raw response: {response.text[:200]}...")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {e}")
        
        # Wait between requests
        if i < len(test_numbers):
            time.sleep(2)
    
    print(f"\n{'=' * 50}")
    print("🏁 TEST COMPLETE")

if __name__ == "__main__":
    test_public_api()
