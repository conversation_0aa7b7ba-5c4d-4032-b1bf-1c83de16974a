<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCS Express Tracking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .shadow-card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        #tcsFrame {
            height: 80vh;
            width: 100%;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
        }
        /* Improved mobile responsiveness */
        @media (max-width: 640px) {
            .tracking-input {
                flex-direction: column;
            }
            .tracking-input input {
                width: 100%;
                border-radius: 0.375rem;
                margin-bottom: 0.5rem;
            }
            .tracking-input button {
                width: 100%;
                border-radius: 0.375rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4">
        <h1 class="text-3xl font-bold text-center mb-8 text-[#f0575d]">TCS Express Tracking</h1>
        
        <!-- Improved Tracking Input Form -->
        <div class="flex justify-center mb-8">
            <div class="flex tracking-input w-full max-w-2xl">
                <input 
                    type="number" 
                    id="trackingNumber" 
                    placeholder="Enter TCS Tracking Number" 
                    class="p-3 md:w-[27rem] sm:w-[20rem] w-full focus:outline-none opacity-75 rounded-l-sm border"
                    value="31442083394"
                >
                <button 
                    onclick="loadTCSTracking()"
                    class="bg-[#f0575d] text-white px-6 py-3 rounded-r-sm hover:bg-[#d84a50] transition whitespace-nowrap"
                >
                    Track Shipment
                </button>
            </div>
        </div>
        
        <!-- TCS Website Embed -->
        <div id="tcsContainer" class="hidden">
            <iframe 
                id="tcsFrame"
                src=""
                title="TCS Tracking"
                allow="fullscreen"
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
                class="w-full"
            ></iframe>
            <p class="text-sm text-gray-500 mt-2">
                Note: This displays the official TCS tracking page. Some features may be limited in the embedded view.
                <a href="#" id="openInNewTab" class="text-[#f0575d] underline">Open in new tab</a> for full functionality.
            </p>
        </div>
        
        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center hidden mt-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#f0575d]"></div>
            <p class="mt-2 text-gray-600">Loading tracking information...</p>
        </div>
        
        <!-- Error Message -->
        <div id="errorMessage" class="text-center text-red-600 hidden mt-4"></div>
    </div>

    <script>
        function loadTCSTracking() {
            const trackingNumber = document.getElementById('trackingNumber').value.trim();
            const tcsContainer = document.getElementById('tcsContainer');
            const tcsFrame = document.getElementById('tcsFrame');
            const openInNewTab = document.getElementById('openInNewTab');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const errorMessage = document.getElementById('errorMessage');
            
            if (!trackingNumber) {
                errorMessage.textContent = 'Please enter a tracking number';
                errorMessage.classList.remove('hidden');
                return;
            }
            
            // Show loading, hide previous results/errors
            loadingIndicator.classList.remove('hidden');
            errorMessage.classList.add('hidden');
            tcsContainer.classList.add('hidden');
            
            try {
                // Construct the TCS tracking URL
                const tcsUrl = `https://www.tcsexpress.com/track/${trackingNumber}`;
                
                // Load in iframe
                tcsFrame.src = tcsUrl;
                
                // When iframe loads, show it and hide loading
                tcsFrame.onload = function() {
                    loadingIndicator.classList.add('hidden');
                    tcsContainer.classList.remove('hidden');
                };
                
                // Set up new tab link
                openInNewTab.href = tcsUrl;
                openInNewTab.onclick = function(e) {
                    e.preventDefault();
                    window.open(tcsUrl, '_blank');
                };
                
            } catch (error) {
                loadingIndicator.classList.add('hidden');
                errorMessage.textContent = 'Error loading tracking information. Please try again.';
                errorMessage.classList.remove('hidden');
                console.error('Error:', error);
            }
        }
        
        // Load default tracking on page load
        window.onload = function() {
            const defaultTracking = document.getElementById('trackingNumber').value;
            if (defaultTracking) {
                loadTCSTracking();
            }
        };
    </script>
</body>
</html>