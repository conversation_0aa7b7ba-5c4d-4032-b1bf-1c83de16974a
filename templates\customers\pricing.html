{% extends 'base.html' %}

{% block title %}Customer Pricing{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Customer Pricing</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View All Customers
                            </a>
                            <a href="{{ url_for('customers', view='by_type') }}" class="btn btn-secondary">
                                <i class="fas fa-user-tag"></i> View Customers by Type
                            </a>
                            <a href="{{ url_for('customers', action='add') }}" class="btn btn-secondary">
                                <i class="fas fa-user-plus"></i> Add New Customer
                            </a>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#setPricingModal">
                                <i class="fas fa-dollar-sign"></i> Set Customer Pricing
                            </button>
                            <a href="{{ url_for('customers', view='orders') }}" class="btn btn-secondary">
                                <i class="fas fa-history"></i> View Customer Order History
                            </a>
                        </div>
                    </div>
                    
                    {% if customer_id %}
                        <!-- Customer-specific pricing -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">Pricing for {{ customer.name }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="thead-dark">
                                                    <tr>
                                                        <th>Product</th>
                                                        <th>Strength</th>
                                                        <th>Standard Price</th>
                                                        <th>Customer Price</th>
                                                        <th>Discount %</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% if customer_pricing %}
                                                        {% for price in customer_pricing %}
                                                        <tr>
                                                            <td>{{ price.product_name }}</td>
                                                            <td>{{ price.strength }}</td>
                                                            <td>{{ price.standard_price }}</td>
                                                            <td>{{ price.price }}</td>
                                                            <td>{{ ((price.standard_price - price.price) / price.standard_price * 100)|round(2) }}%</td>
                                                            <td>
                                                                <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editPricingModal{{ price.pricing_id }}">
                                                                    <i class="fas fa-edit"></i> Edit
                                                                </button>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    {% else %}
                                                        <tr>
                                                            <td colspan="6" class="text-center">No custom pricing found for this customer</td>
                                                        </tr>
                                                    {% endif %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- All customer pricing -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Customer</th>
                                        <th>Product</th>
                                        <th>Strength</th>
                                        <th>Standard Price</th>
                                        <th>Customer Price</th>
                                        <th>Discount %</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if pricing %}
                                        {% for price in pricing %}
                                        <tr>
                                            <td>{{ price.customer_name }}</td>
                                            <td>{{ price.product_name }}</td>
                                            <td>{{ price.strength }}</td>
                                            <td>{{ price.standard_price }}</td>
                                            <td>{{ price.price }}</td>
                                            <td>{{ ((price.standard_price - price.price) / price.standard_price * 100)|round(2) }}%</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editPricingModal{{ price.pricing_id }}">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">No custom pricing found</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Set Pricing Modal -->
<div class="modal fade" id="setPricingModal" tabindex="-1" role="dialog" aria-labelledby="setPricingModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="setPricingModalLabel">Set Customer Pricing</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{{ url_for('customers', action='set_pricing') }}">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="customer_id">Customer</label>
                        <select class="form-control" id="customer_id" name="customer_id" required>
                            <option value="">-- Select Customer --</option>
                            {% for customer in customers %}
                            <option value="{{ customer.customer_id }}">{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="product_id">Product</label>
                        <select class="form-control" id="product_id" name="product_id" required>
                            <option value="">-- Select Product --</option>
                            {% for product in products %}
                            <option value="{{ product.product_id }}">{{ product.name }} ({{ product.strength }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="price">Price</label>
                        <input type="number" step="0.01" class="form-control" id="price" name="price" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Set Price</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Pricing Modals -->
{% if pricing %}
    {% for price in pricing %}
    <div class="modal fade" id="editPricingModal{{ price.pricing_id }}" tabindex="-1" role="dialog" aria-labelledby="editPricingModalLabel{{ price.pricing_id }}" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editPricingModalLabel{{ price.pricing_id }}">Edit Pricing</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="post" action="{{ url_for('customers', action='edit_pricing', pricing_id=price.pricing_id) }}">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Customer</label>
                            <input type="text" class="form-control" value="{{ price.customer_name }}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Product</label>
                            <input type="text" class="form-control" value="{{ price.product_name }} ({{ price.strength }})" readonly>
                        </div>
                        <div class="form-group">
                            <label>Standard Price</label>
                            <input type="text" class="form-control" value="{{ price.standard_price }}" readonly>
                        </div>
                        <div class="form-group">
                            <label for="price{{ price.pricing_id }}">Customer Price</label>
                            <input type="number" step="0.01" class="form-control" id="price{{ price.pricing_id }}" name="price" value="{{ price.price }}" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endfor %}
{% endif %}
{% endblock %}
