{% extends "base.html" %}

{% block title %}Products Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-pills"></i> Products Management
                            </h4>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('new_product') }}" class="btn btn-light">
                                <i class="fas fa-plus"></i> Add Product
                            </a>
                            <a href="{{ url_for('inventory') }}" class="btn btn-info">
                                <i class="fas fa-warehouse"></i> Inventory
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ url_for('product_management') }}" class="form-inline">
                                <div class="input-group" style="width: 100%;">
                                    <input type="text" name="search" class="form-control"
                                           placeholder="Search products by name, generic name, category..."
                                           value="{{ request.args.get('search', '') }}" id="search-input">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-outline-success">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        {% if request.args.get('search') %}
                                        <a href="{{ url_for('product_management') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}">All Products</a>
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}?category=Tablets">Tablets</a>
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}?category=Capsules">Capsules</a>
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}?category=Syrup">Syrups</a>
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}?category=Injections">Injections</a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}?status=active">Active Only</a>
                                    <a class="dropdown-item" href="{{ url_for('product_management') }}?status=inactive">Inactive Only</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|length if products else 0 }}</h5>
                                    <small>Total Products</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.in_stock or 0 }}</h5>
                                    <small>In Stock</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.low_stock or 0 }}</h5>
                                    <small>Low Stock</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.out_of_stock or 0 }}</h5>
                                    <small>Out of Stock</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Product Name</th>
                                    <th>Generic Name</th>
                                    <th>Type</th>
                                    <th>Strength</th>
                                    <th>Manufacturer</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if products %}
                                    {% for product in products %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary font-weight-bold" style="font-size: 0.9em;">
                                                {{ product.product_id }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if product.image_url %}
                                                    <img src="{{ product.image_url }}" alt="{{ product.name }}"
                                                         class="product-thumbnail me-3" style="width: 40px; height: 40px; object-fit: contain; border-radius: 6px;">
                                                {% else %}
                                                    <div class="product-placeholder me-3 d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 6px; border: 1px solid #dee2e6;">
                                                        <i class="fas fa-pills text-muted" style="font-size: 0.8em;"></i>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <strong>{{ product.name }}</strong>
                                                    {% if product.brand_name %}
                                                        <br><small class="text-muted">{{ product.brand_name }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ product.generic_name or product.category or '-' }}</td>
                                        <td>
                                            <span class="badge badge-info">{{ product.unit_of_measure or 'N/A' }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ product.strength or '-' }}</strong>
                                        </td>
                                        <td>{{ product.manufacturer or '-' }}</td>
                                        <td>
                                            {% if product.is_active is none or product.is_active %}
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check-circle"></i> Active
                                                </span>
                                            {% else %}
                                                <span class="badge badge-secondary">
                                                    <i class="fas fa-pause-circle"></i> Inactive
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('view_product', product_id=product.product_id) }}"
                                                   class="btn btn-outline-info" title="View Details" data-toggle="tooltip">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('update_product', product_id=product.product_id) }}"
                                                   class="btn btn-outline-warning" title="Edit Product" data-toggle="tooltip">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-outline-danger" title="Delete Product" data-toggle="tooltip"
                                                        onclick="confirmDelete('{{ product.product_id }}', '{{ product.name }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center text-muted py-4">
                                            <i class="fas fa-pills fa-3x mb-3"></i>
                                            <br>No products found.
                                            <br><a href="{{ url_for('new_product') }}" class="btn btn-success mt-2">
                                                <i class="fas fa-plus"></i> Add First Product
                                            </a>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav aria-label="Product pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('product_management', page=pagination.prev_num, **request.args) }}">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                            {% endif %}

                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('product_management', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('product_management', page=pagination.next_num, **request.args) }}">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(productId, productName) {
    if (confirm(`Are you sure you want to delete "${productName}"?\n\nThis action cannot be undone.`)) {
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        fetch(`/products/${productId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and reload
                alert('Product deleted successfully!');
                location.reload();
            } else {
                alert('Error deleting product: ' + (data.message || 'Unknown error'));
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error. Please check your connection and try again.');
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}

// Add search functionality
function searchProducts() {
    const searchTerm = document.getElementById('search-input').value;
    const currentUrl = new URL(window.location);

    if (searchTerm.trim()) {
        currentUrl.searchParams.set('search', searchTerm);
    } else {
        currentUrl.searchParams.delete('search');
    }

    currentUrl.searchParams.delete('page'); // Reset to first page
    window.location.href = currentUrl.toString();
}

// Add filter functionality
function applyFilter(filterType, filterValue) {
    const currentUrl = new URL(window.location);

    if (filterValue && filterValue !== 'all') {
        currentUrl.searchParams.set(filterType, filterValue);
    } else {
        currentUrl.searchParams.delete(filterType);
    }

    currentUrl.searchParams.delete('page'); // Reset to first page
    window.location.href = currentUrl.toString();
}

// Handle Enter key in search
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchProducts();
            }
        });
    }

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add hover effects to product rows
    $('.table tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
});

// Auto-submit search form on input
document.getElementById('search-input').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>

<style>
.product-thumbnail {
    transition: transform 0.2s ease;
}

.product-thumbnail:hover {
    transform: scale(1.1);
    cursor: pointer;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.badge {
    font-size: 0.85em;
}

.btn-group-sm .btn {
    transition: all 0.2s ease;
}

.btn-group-sm .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.stats-card {
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.product-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.thead-dark th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    border: none;
}
</style>
{% endblock %}
