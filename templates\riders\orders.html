{% extends "base.html" %}

{% block title %}Rider Orders - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="h3 mb-4">🚚 Delivery Orders Management</h1>
            <p class="text-muted">Manage pickup, dispatch, and delivery of orders</p>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">🔍 Filter Orders</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('riders_orders') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="status">Status:</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="Ready for Pickup" {% if request.args.get('status') == 'Ready for Pickup' %}selected{% endif %}>Ready for Pickup</option>
                                    <option value="Dispatched" {% if request.args.get('status') == 'Dispatched' %}selected{% endif %}>Dispatched</option>
                                    <option value="Delivered" {% if request.args.get('status') == 'Delivered' %}selected{% endif %}>Delivered</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="search">Search Order/Customer:</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="{{ request.args.get('search', '') }}" placeholder="Order ID or Customer">
                            </div>
                            <div class="col-md-3">
                                <label for="date">Date:</label>
                                <input type="date" name="date" id="date" class="form-control" 
                                       value="{{ request.args.get('date', '') }}">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">Filter</button>
                                    <a href="{{ url_for('riders_orders') }}" class="btn btn-secondary ml-2">Clear</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">📦 Delivery Orders ({{ orders|length }} orders)</h5>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="ordersTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Order Date</th>
                                    <th>Delivery Address</th>
                                    <th>Phone</th>
                                    <th>Sales Agent</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr id="order-{{ order.order_id }}">
                                    <td>
                                        <strong class="text-primary">{{ order.order_id }}</strong>
                                        <br><small class="text-muted">{{ order.order_date }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ order.customer_name }}</strong>
                                    </td>
                                    <td>
                                        <strong class="text-success">Rs.{{ "{:,.0f}".format(order.order_amount) }}</strong>
                                    </td>
                                    <td>{{ order.order_date }}</td>
                                    <td>
                                        {% if order.delivery_address %}
                                            <small>{{ order.delivery_address }}</small>
                                        {% else %}
                                            <span class="text-muted">No address</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.phone_number %}
                                            <a href="tel:{{ order.phone_number }}" class="text-primary">
                                                {{ order.phone_number }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">No phone</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.sales_agent or 'N/A' }}</td>
                                    <td>
                                        {% if order.status == 'Ready for Pickup' %}
                                            <span class="badge badge-warning badge-lg">{{ order.status }}</span>
                                        {% elif order.status == 'Dispatched' %}
                                            <span class="badge badge-info badge-lg">{{ order.status }}</span>
                                        {% elif order.status == 'Delivered' %}
                                            <span class="badge badge-success badge-lg">{{ order.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary badge-lg">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <!-- Pickup Action -->
                                            {% if order.status == 'Ready for Pickup' %}
                                                <button type="button" class="btn btn-primary btn-sm mb-1"
                                                        onclick="showPickupForm('{{ order.order_id }}', '{{ order.customer_name }}', '{{ order.order_amount }}')">
                                                    <i class="fas fa-hand-paper"></i> Claim Pickup
                                                </button>
                                            {% endif %}
                                            
                                            <!-- Dispatch Action -->
                                            {% if order.status == 'Ready for Pickup' %}
                                                <form method="POST" action="{{ url_for('update_delivery_status') }}" style="display: inline;">
                                                    <input type="hidden" name="order_id" value="{{ order.order_id }}">
                                                    <input type="hidden" name="status" value="Dispatched">
                                                    <button type="submit" class="btn btn-info btn-sm mb-1">
                                                        <i class="fas fa-truck"></i> Mark Dispatched
                                                    </button>
                                                </form>
                                            {% endif %}
                                            
                                            <!-- Delivery Actions -->
                                            {% if order.status == 'Dispatched' %}
                                                <button type="button" class="btn btn-success btn-sm mb-1" 
                                                        onclick="markDelivered('{{ order.order_id }}')">
                                                    <i class="fas fa-check"></i> Mark Delivered
                                                </button>
                                                <button type="button" class="btn btn-warning btn-sm mb-1" 
                                                        onclick="markCancelled('{{ order.order_id }}', 'customer')">
                                                    <i class="fas fa-times"></i> Customer Cancel
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm mb-1" 
                                                        onclick="markCancelled('{{ order.order_id }}', 'delay')">
                                                    <i class="fas fa-clock"></i> Delay Cancel
                                                </button>
                                            {% endif %}
                                            
                                            <!-- View Details -->
                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                    onclick="viewOrderDetails('{{ order.order_id }}')">
                                                <i class="fas fa-eye"></i> Details
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-truck fa-4x mb-3"></i>
                        <h4>No delivery orders found</h4>
                        <p>No orders match your current filter criteria.</p>
                        <a href="{{ url_for('riders_orders') }}" class="btn btn-primary">View All Orders</a>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">Total: {{ orders|length }} orders</small>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('riders_dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <a href="{{ url_for('workflow') }}" class="btn btn-primary ml-2">
                                <i class="fas fa-tasks"></i> Order Workflow
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pickup Form Modal -->
<div class="modal fade" id="pickupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">🚚 Claim Order for Pickup</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="pickupForm" method="POST" action="{{ url_for('claim_pickup') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Order Information</h6>
                            <div class="form-group">
                                <label>Order ID:</label>
                                <input type="text" id="pickup_order_id" name="order_id" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label>Customer Name:</label>
                                <input type="text" id="pickup_customer_name" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label>Order Amount:</label>
                                <input type="text" id="pickup_order_amount" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Rider Information</h6>
                            <div class="form-group">
                                <label>Rider Name: <span class="text-danger">*</span></label>
                                <input type="text" name="rider_name" class="form-control" required
                                       placeholder="Enter your name">
                            </div>
                            <div class="form-group">
                                <label>Rider ID/Phone: <span class="text-danger">*</span></label>
                                <input type="text" name="rider_id" class="form-control" required
                                       placeholder="Enter your ID or phone">
                            </div>
                            <div class="form-group">
                                <label>Vehicle Type:</label>
                                <select name="vehicle_type" class="form-control">
                                    <option value="bike">Motorcycle</option>
                                    <option value="car">Car</option>
                                    <option value="van">Van</option>
                                    <option value="truck">Truck</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <h6>Pickup Details</h6>
                            <div class="form-group">
                                <label>Expected Pickup Time:</label>
                                <input type="datetime-local" name="expected_pickup_time" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>Notes:</label>
                                <textarea name="pickup_notes" class="form-control" rows="3"
                                         placeholder="Any special instructions or notes..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-hand-paper"></i> Claim Order
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Order Details</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- Content loaded via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Show pickup form
function showPickupForm(orderId, customerName, orderAmount) {
    document.getElementById('pickup_order_id').value = orderId;
    document.getElementById('pickup_customer_name').value = customerName;
    document.getElementById('pickup_order_amount').value = 'Rs.' + parseFloat(orderAmount).toLocaleString();
    $('#pickupModal').modal('show');
}

// Handle pickup form submission
document.getElementById('pickupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('/riders/claim_pickup', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Order claimed successfully!');
            $('#pickupModal').modal('hide');
            location.reload();
        } else {
            alert('Error claiming order: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error claiming order');
    });
});

// Mark as delivered
function markDelivered(orderId) {
    if (confirm('Mark this order as delivered?')) {
        updateOrderStatus(orderId, 'Delivered');
    }
}

// Mark as cancelled
function markCancelled(orderId, reason) {
    const reasonText = reason === 'customer' ? 'cancelled by customer' : 'cancelled due to delay';
    if (confirm(`Mark this order as ${reasonText}?`)) {
        updateOrderStatus(orderId, 'Cancelled', reason);
    }
}

// Update order status
function updateOrderStatus(orderId, status, reason = null) {
    const formData = new FormData();
    formData.append('order_id', orderId);
    formData.append('status', status);
    if (reason) {
        formData.append('cancel_reason', reason);
    }
    
    fetch('/riders/update_status', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            alert('Order status updated successfully!');
            location.reload();
        } else {
            alert('Error updating order status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating order status');
    });
}

// View order details
function viewOrderDetails(orderId) {
    fetch(`/api/order-details/${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('orderDetailsContent').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Order Information</h6>
                            <p><strong>Order ID:</strong> ${data.order.order_id}</p>
                            <p><strong>Customer:</strong> ${data.order.customer_name}</p>
                            <p><strong>Amount:</strong> Rs.${data.order.order_amount.toLocaleString()}</p>
                            <p><strong>Status:</strong> ${data.order.status}</p>
                            <p><strong>Order Date:</strong> ${data.order.order_date}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Delivery Information</h6>
                            <p><strong>Address:</strong> ${data.order.delivery_address || 'N/A'}</p>
                            <p><strong>Phone:</strong> ${data.order.phone_number || 'N/A'}</p>
                            <p><strong>Sales Agent:</strong> ${data.order.sales_agent || 'N/A'}</p>
                        </div>
                    </div>
                `;
                $('#orderDetailsModal').modal('show');
            } else {
                alert('Error loading order details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading order details');
        });
}

// Initialize DataTable for better UX
$(document).ready(function() {
    if ($.fn.DataTable) {
        $('#ordersTable').DataTable({
            "pageLength": 25,
            "order": [[ 3, "desc" ]], // Sort by date
            "responsive": true
        });
    }
});
</script>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.8em;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

.table td {
    vertical-align: middle;
}
</style>
{% endblock %}
