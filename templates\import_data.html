{% extends 'base.html' %}

{% block title %}Import Data{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Import Data</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Import from Excel</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('import_data') }}" enctype="multipart/form-data">
                                        <div class="form-group">
                                            <label for="file">Select Excel File</label>
                                            <input type="file" class="form-control-file" id="file" name="file" accept=".xlsx, .xls" required>
                                            <small class="form-text text-muted">
                                                File name should include "order", "product", or "customer" to identify the data type.
                                            </small>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-file-import"></i> Import Data
                                        </button>
                                    </form>
                                </div>
                            </div>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Import Instructions</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Order Data Format</h6>
                                    <p>Excel file should have the following columns:</p>
                                    <ul>
                                        <li>Order ID</li>
                                        <li>Customer Name</li>
                                        <li>Customer Address</li>
                                        <li>Customer Phone</li>
                                        <li>Order Date</li>
                                        <li>Status</li>
                                        <li>Sales Agent</li>
                                        <li>Order Amount</li>
                                        <li>Payment Method</li>
                                    </ul>
                                    
                                    <h6>Product Data Format</h6>
                                    <p>Excel file should have the following columns:</p>
                                    <ul>
                                        <li>Product ID</li>
                                        <li>Name</li>
                                        <li>Strength</li>
                                        <li>Manufacturer</li>
                                        <li>Category</li>
                                        <li>Division ID</li>
                                        <li>Unit of Measure</li>
                                        <li>Unit Price</li>
                                        <li>Min Stock Level</li>
                                    </ul>
                                    
                                    <h6>Customer Data Format</h6>
                                    <p>Excel file should have the following columns:</p>
                                    <ul>
                                        <li>Customer ID</li>
                                        <li>Name</li>
                                        <li>Contact</li>
                                        <li>Address</li>
                                        <li>Type</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">Import History</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Date</th>
                                                    <th>File</th>
                                                    <th>Type</th>
                                                    <th>Records</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                                    <td>orders_data.xlsx</td>
                                                    <td>Orders</td>
                                                    <td>150</td>
                                                    <td><span class="badge badge-success">Success</span></td>
                                                </tr>
                                                <tr>
                                                    <td>{{ (now - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                                    <td>products_data.xlsx</td>
                                                    <td>Products</td>
                                                    <td>75</td>
                                                    <td><span class="badge badge-success">Success</span></td>
                                                </tr>
                                                <tr>
                                                    <td>{{ (now - timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                                    <td>customers_data.xlsx</td>
                                                    <td>Customers</td>
                                                    <td>50</td>
                                                    <td><span class="badge badge-success">Success</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">Data Validation</h5>
                                </div>
                                <div class="card-body">
                                    <p>Before importing data, the system will validate:</p>
                                    <ul>
                                        <li>Required fields are present</li>
                                        <li>Data types are correct</li>
                                        <li>Duplicate records are identified</li>
                                        <li>Foreign key relationships are valid</li>
                                    </ul>
                                    
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> Importing large datasets may take some time. Please be patient and do not refresh the page during import.
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> For order data, related products must already exist in the system.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
