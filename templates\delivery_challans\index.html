{% extends 'base.html' %}

{% block title %}Delivery Challans - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Delivery Challans</h1>
        <a href="{{ url_for('orders') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Orders
        </a>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Challans</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ challans|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₨{{ "{:,.2f}".format(challans|sum(attribute='order_amount')|default(0)) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">This Month</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set current_month = now.strftime('%Y-%m') %}
                                {% set this_month_count = 0 %}
                                {% for challan in challans %}
                                    {% if challan.date_generated and challan.date_generated[:7] == current_month %}
                                        {% set this_month_count = this_month_count + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {{ this_month_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Dispatch</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ challans|selectattr('status', 'equalto', 'Generated')|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Challans Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Delivery Challans List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>DC Number</th>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Date Generated</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for challan in challans %}
                        <tr>
                            <td>{{ challan.dc_number if challan.dc_number else 'DC-' + challan.order_id|string }}</td>
                            <td>{{ challan.order_id }}</td>
                            <td>{{ challan.customer_name }}</td>
                            <td>{{ challan.date_generated if challan.date_generated else challan.order_date }}</td>
                            <td>₨{{ "{:,.2f}".format(challan.order_amount) }}</td>
                            <td>
                                <span class="badge badge-{% if challan.status == 'Dispatched' %}success{% elif challan.status == 'Generated' %}warning{% else %}info{% endif %}">
                                    {{ challan.status if challan.status else 'Generated' }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('view_order_history', order_id=challan.order_id) }}"
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View Order
                                </a>
                                <a href="{{ url_for('view_challan', order_id=challan.order_id) }}"
                                   class="btn btn-sm btn-info" target="_blank">
                                    <i class="fas fa-print"></i> View DC
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">No delivery challans found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly DC Generation Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyTrendChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">DC Status Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 3, "desc" ]], // Sort by date descending
        "pageLength": 25
    });

    // Monthly Trend Chart (mock data)
    var ctx1 = document.getElementById('monthlyTrendChart').getContext('2d');
    var monthlyTrendChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'DCs Generated',
                data: [],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Status Distribution Chart
    var statusCounts = {};
    {% for challan in challans %}
    var status = '{{ challan.status if challan.status else "Generated" }}';
    if (statusCounts[status]) {
        statusCounts[status]++;
    } else {
        statusCounts[status] = 1;
    }
    {% endfor %}

    var ctx2 = document.getElementById('statusChart').getContext('2d');
    var statusChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: Object.keys(statusCounts),
            datasets: [{
                data: Object.values(statusCounts),
                backgroundColor: ['#1cc88a', '#f6c23e', '#e74a3b', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %}
