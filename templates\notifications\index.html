<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Center - Medivent ERP</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
    :root {
        --primary-blue: #007bff;
        --light-blue: #e3f2fd;
        --dark-blue: #0056b3;
        --success-green: #28a745;
        --warning-orange: #ffc107;
        --danger-red: #dc3545;
        --light-gray: #f8f9fa;
        --border-color: #e9ecef;
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
    }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        /* Navigation Bar */
        .top-nav {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-decoration: none;
        }

        .nav-brand:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Bell Icon Notification Styles */
        .notification-bell {
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        .notification-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .notification-dropdown-menu .dropdown-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-radius: 15px 15px 0 0;
        }

        .notification-dropdown-menu .dropdown-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .notification-dropdown-menu .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .notification-dropdown-menu .dropdown-item.unread {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-blue);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Screenshot-style Notification Styles */
        .screenshot-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .screenshot-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .screenshot-notification-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .screenshot-notification-header {
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            position: relative;
        }

        .screenshot-notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .screenshot-notification-icon.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .screenshot-notification-icon.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .screenshot-notification-icon.danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }

        .screenshot-notification-icon.primary {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
        }

        .screenshot-notification-icon.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .screenshot-notification-details {
            flex: 1;
        }

        .screenshot-notification-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 8px 0;
        }

        .screenshot-notification-message {
            color: #6c757d;
            margin: 0 0 8px 0;
            line-height: 1.4;
        }

        .screenshot-notification-time {
            color: #adb5bd;
            font-size: 0.85rem;
        }

        .screenshot-notification-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            color: #adb5bd;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .screenshot-notification-close:hover {
            background: #f8f9fa;
            color: #6c757d;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .top-bar {
            padding: 25px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .page-title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .title-icon {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .content-area {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            padding: 30px;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            height: fit-content;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .filter-item {
            margin-bottom: 8px;
        }

        .filter-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            text-decoration: none;
            color: #6c757d;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-link:hover, .filter-link.active {
            background: var(--primary-blue);
            color: white;
            transform: translateX(5px);
        }

        .filter-count {
            background: #e9ecef;
            color: #6c757d;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .filter-link.active .filter-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .notifications-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #adb5bd;
            font-size: 1.1rem;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary-blue), var(--light-blue));
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            transform: translateX(10px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -38px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-blue);
            border: 3px solid white;
            box-shadow: 0 0 0 3px var(--primary-blue);
        }

        .timeline-item.unread::before {
            background: var(--success-green);
            box-shadow: 0 0 0 3px var(--success-green);
            animation: pulse 2s infinite;
        }

        .timeline-item.warning::before {
            background: var(--warning-orange);
            box-shadow: 0 0 0 3px var(--warning-orange);
        }

        .timeline-item.error::before {
            background: var(--danger-red);
            box-shadow: 0 0 0 3px var(--danger-red);
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .notification-header {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 15px;
        }

        .notification-avatar {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            flex-shrink: 0;
        }

        .notification-avatar.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .notification-avatar.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .notification-avatar.error {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }

        .notification-avatar.info {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
        }

        .notification-details {
            flex: 1;
        }

        .notification-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-message {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .notification-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #f8f9fa;
        }

        .notification-time {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #adb5bd;
            font-size: 0.9rem;
        }

        .notification-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-button.primary {
            background: var(--primary-blue);
            color: white;
        }

        .action-button.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #e9ecef;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .unread-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--success-green);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        .load-more {
            text-align: center;
            margin-top: 30px;
        }

        .load-more-btn {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .load-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }

        @media (max-width: 768px) {
            .content-area {
                grid-template-columns: 1fr;
            }

            .sidebar {
                order: 2;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <a href="/" class="nav-brand">
                    <i class="fas fa-heartbeat me-2"></i>Medivent ERP
                </a>
                <div class="nav-links">
                    <a href="/dashboard" class="nav-link">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="/orders" class="nav-link">
                        <i class="fas fa-shopping-cart me-2"></i>Orders
                    </a>
                    <a href="/products" class="nav-link">
                        <i class="fas fa-box me-2"></i>Products
                    </a>
                    <a href="/notifications" class="nav-link" style="background: rgba(255, 255, 255, 0.1);">
                        <i class="fas fa-bell me-2"></i>Notifications
                    </a>

                    <!-- Bell Icon Notification -->
                    <div class="nav-link notification-bell" style="position: relative; cursor: pointer;" onclick="toggleNotificationDropdown()">
                        <i class="fas fa-bell" style="font-size: 1.2rem;"></i>
                        <span id="notification-badge" class="notification-badge" style="display: none;">0</span>

                        <!-- Notification Dropdown -->
                        <div id="notification-dropdown" class="notification-dropdown-menu" style="display: none;">
                            <div class="dropdown-header">
                                <span><i class="fas fa-bell"></i> Recent Notifications</span>
                                <button onclick="markAllAsReadFromDropdown()" class="btn btn-sm btn-outline-primary">Mark All Read</button>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div id="dropdown-notification-list">
                                <div class="dropdown-item text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading...
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/logout" class="nav-link">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>
<div class="main-container">
    <!-- Top Bar -->
    <div class="glass-card">
        <div class="top-bar">
            <h1 class="page-title">
                <div class="title-icon">
                    <i class="fas fa-bell"></i>
                </div>
                Notification Center
            </h1>
            <p style="color: rgba(255, 255, 255, 0.8); margin-top: 10px; font-size: 1.1rem;">
                Stay updated with real-time system notifications and alerts
            </p>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number" id="total-count">{{ notifications|length }}</span>
                    <span>Total Notifications</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="unread-count">{{ unread_count }}</span>
                    <span>Unread</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="critical-count">{{ notifications|selectattr('notification_type', 'equalto', 'warning')|list|length + notifications|selectattr('notification_type', 'equalto', 'error')|list|length }}</span>
                    <span>Critical</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="today-count">
                        {% set today_count = 0 %}
                        {% if now %}
                            {% for notification in notifications %}
                                {% if notification.created_at %}
                                    {% set created_date = notification.created_at[:10] if notification.created_at is string else notification.created_at.strftime('%Y-%m-%d') %}
                                    {% if created_date == now.strftime('%Y-%m-%d') %}
                                        {% set today_count = today_count + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                        {{ today_count }}
                    </span>
                    <span>Today</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-title">
                <i class="fas fa-filter"></i>
                Filter Notifications
            </div>

            <ul class="filter-list">
                <li class="filter-item">
                    <a href="#" class="filter-link active" data-filter="all">
                        <span><i class="fas fa-inbox me-2"></i>All Notifications</span>
                        <span class="filter-count">{{ notifications|length }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-filter="unread">
                        <span><i class="fas fa-circle me-2"></i>Unread</span>
                        <span class="filter-count">{{ unread_count }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-filter="order">
                        <span><i class="fas fa-shopping-cart me-2"></i>Orders</span>
                        <span class="filter-count">{{ notifications|selectattr('notification_type', 'equalto', 'order')|list|length }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-filter="warning">
                        <span><i class="fas fa-exclamation-triangle me-2"></i>Warnings</span>
                        <span class="filter-count">{{ notifications|selectattr('notification_type', 'equalto', 'warning')|list|length }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-filter="system">
                        <span><i class="fas fa-cog me-2"></i>System</span>
                        <span class="filter-count">{{ notifications|selectattr('notification_type', 'equalto', 'system')|list|length }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-filter="success">
                        <span><i class="fas fa-check-circle me-2"></i>Success</span>
                        <span class="filter-count">{{ notifications|selectattr('notification_type', 'equalto', 'success')|list|length }}</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Notifications Area -->
        <div class="notifications-area">
            <div class="notifications-header">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search notifications..." id="notificationSearch">
                </div>
                <div class="d-flex gap-3">
                    {% if unread_count > 0 %}
                    <button class="action-button primary" onclick="markAllAsRead()">
                        <i class="fas fa-check-double"></i>Mark All Read
                    </button>
                    {% endif %}
                    <button class="action-button secondary" onclick="refreshNotifications()">
                        <i class="fas fa-sync"></i>Refresh
                    </button>

                    <!-- Test Real-Time Notification Button -->
                    <button class="action-button" onclick="createTestNotification()" style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                        <i class="fas fa-plus"></i>Test Notification
                    </button>
                </div>
            </div>

            <!-- Timeline -->
            <div class="timeline" id="notificationTimeline">
                {% if notifications %}
                    {% for notification in notifications %}
                    <div class="timeline-item {% if not notification.is_read %}unread{% endif %} {{ notification.notification_type }}"
                         data-notification-id="{{ notification.id }}"
                         data-type="{{ notification.notification_type }}"
                         data-read="{{ notification.is_read|lower }}">

                        {% if not notification.is_read %}
                        <div class="unread-indicator">NEW</div>
                        {% endif %}

                        <div class="notification-header">
                            <div class="notification-avatar {{ notification.notification_type }}">
                                {% if notification.icon %}
                                    <i class="{{ notification.icon }}"></i>
                                {% elif notification.notification_type == 'order' %}
                                    <i class="fas fa-shopping-cart"></i>
                                {% elif notification.notification_type == 'warning' %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% elif notification.notification_type == 'error' %}
                                    <i class="fas fa-times-circle"></i>
                                {% elif notification.notification_type == 'success' %}
                                    <i class="fas fa-check-circle"></i>
                                {% elif notification.notification_type == 'system' %}
                                    <i class="fas fa-cog"></i>
                                {% else %}
                                    <i class="fas fa-info-circle"></i>
                                {% endif %}
                            </div>
                            <div class="notification-details">
                                <div class="notification-title">
                                    {{ notification.title }}
                                    {% if notification.notification_type == 'warning' %}
                                        <span class="badge bg-warning text-dark">Warning</span>
                                    {% elif notification.notification_type == 'error' %}
                                        <span class="badge bg-danger">Critical</span>
                                    {% elif notification.notification_type == 'success' %}
                                        <span class="badge bg-success">Success</span>
                                    {% elif notification.notification_type == 'order' %}
                                        <span class="badge bg-primary">Order</span>
                                    {% endif %}
                                </div>
                                <div class="notification-message">
                                    {{ notification.message }}
                                    {% if notification.entity_type %}
                                    <br><small class="text-muted">
                                        <i class="fas fa-tag"></i> {{ notification.entity_type|title }}
                                        {% if notification.entity_id %} - {{ notification.entity_id }}{% endif %}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="notification-footer">
                            <div class="notification-time">
                                <i class="fas fa-clock"></i>
                                <span>
                                    {% if notification.created_at %}
                                        {% if notification.created_at is string %}
                                            {{ notification.created_at }}
                                        {% else %}
                                            {{ notification.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                        {% endif %}
                                    {% else %}
                                        Unknown time
                                    {% endif %}
                                </span>
                            </div>
                            <div class="notification-actions">
                                {% if notification.action_url %}
                                <a href="{{ notification.action_url }}" class="action-button primary">
                                    <i class="fas fa-external-link-alt"></i>View Details
                                </a>
                                {% endif %}
                                {% if not notification.is_read %}
                                <button class="action-button secondary" onclick="markAsRead('{{ notification.id }}')">
                                    <i class="fas fa-check"></i>Mark as Read
                                </button>
                                {% else %}
                                <button class="action-button secondary" onclick="markAsUnread('{{ notification.id }}')">
                                    <i class="fas fa-undo"></i>Mark as Unread
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-bell-slash" style="font-size: 4rem; color: #e9ecef;"></i>
                        </div>
                        <h4 class="text-muted mb-3">No notifications found</h4>
                        <p class="text-muted mb-4">You're all caught up! Check back later for new notifications.</p>
                        <button class="action-button primary" onclick="refreshNotifications()">
                            <i class="fas fa-sync"></i>Refresh Notifications
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Clean notification management system
let notificationInterval;
let allNotifications = [];
let sessionExpired = false;

$(document).ready(function() {
    // Store all notifications for filtering
    allNotifications = Array.from($('.timeline-item')).map(item => ({
        element: $(item),
        type: $(item).data('type'),
        read: $(item).data('read') === 'true',
        text: $(item).text().toLowerCase()
    }));

    // Initialize functionality
    initializeFilters();
    initializeSearch();
    startNotificationUpdates();
    requestNotificationPermission();

    // Add smooth animations
    $('.timeline-item').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('fadeInUp');
    });

    // Add real-time connection status indicator
    addConnectionStatusIndicator();

    // Initialize bell notifications
    loadBellNotifications();
});

function initializeFilters() {
    $('.filter-link').on('click', function(e) {
        e.preventDefault();

        // Update active filter
        $('.filter-link').removeClass('active');
        $(this).addClass('active');

        const filter = $(this).data('filter');
        filterNotifications(filter);
    });
}

function initializeSearch() {
    $('#notificationSearch').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        searchNotifications(searchTerm);
    });
}

function startNotificationUpdates() {
    // Try WebSocket first, fallback to polling
    if (typeof(WebSocket) !== "undefined") {
        initializeWebSocket();
    } else {
        // Fallback to polling every 5 seconds to avoid session issues
        notificationInterval = setInterval(refreshNotifications, 5000);
        updateConnectionStatus('polling');
    }
}

function initializeWebSocket() {
    try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/notifications`;
        const socket = new WebSocket(wsUrl);

        socket.onopen = function(event) {
            console.log('WebSocket connected for real-time notifications');
            showToast('Real-time notifications connected', 'success');
            updateConnectionStatus('websocket');
        };

        socket.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleRealTimeNotification(data);
        };

        socket.onclose = function(event) {
            console.log('WebSocket disconnected, falling back to polling');
            // Fallback to polling every 5 seconds to avoid session issues
            notificationInterval = setInterval(refreshNotifications, 5000);
            updateConnectionStatus('polling');
        };

        socket.onerror = function(error) {
            console.error('WebSocket error:', error);
            // Fallback to polling every 5 seconds
            notificationInterval = setInterval(refreshNotifications, 5000);
            updateConnectionStatus('polling');
        };

    } catch (error) {
        console.error('WebSocket initialization failed:', error);
        // Fallback to polling every 5 seconds to avoid session issues
        notificationInterval = setInterval(refreshNotifications, 5000);
        updateConnectionStatus('polling');
    }
}

function handleRealTimeNotification(data) {
    if (data.type === 'new_notification') {
        // Add new notification to the timeline
        addNewNotificationToTimeline(data.notification);
        updateNotificationCounts();

        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
            new Notification(data.notification.title, {
                body: data.notification.message,
                icon: '/static/favicon.ico',
                tag: 'medivent-notification'
            });
        }

        // Play notification sound
        playNotificationSound();

    } else if (data.type === 'notification_update') {
        // Update existing notification
        updateExistingNotification(data.notification);
        updateNotificationCounts();
    }
}

function refreshNotifications() {
    // Don't make API calls if session has expired
    if (sessionExpired) {
        console.log('Session expired, skipping notification refresh');
        return;
    }

    console.log('Attempting to refresh notifications...');
    console.log('Making fetch request to /api/notifications');
    fetch('/api/notifications', {
        method: 'GET',
        credentials: 'same-origin',  // Include cookies for authentication
        headers: {
            'Content-Type': 'application/json',
        }
    })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response URL:', response.url);

            // Check if we were redirected to login page
            if (response.url.includes('/login')) {
                console.log('Redirected to login page, user session expired');
                sessionExpired = true;
                clearInterval(notificationInterval);
                window.location.href = '/login?next=/notifications';
                return null;
            }

            if (response.status === 401 || response.status === 403) {
                console.log('Authentication required, redirecting to login...');
                sessionExpired = true;
                clearInterval(notificationInterval);
                window.location.href = '/login?next=/notifications';
                return null;
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is actually JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.log('Response is not JSON, likely redirected to login');
                sessionExpired = true;
                clearInterval(notificationInterval);
                window.location.href = '/login?next=/notifications';
                return null;
            }

            return response.json();
        })
        .then(data => {
            if (!data) {
                console.log('No data returned (likely redirected to login)');
                return; // Handle redirect case
            }

            console.log('API response:', data);
            if (data.success && data.notifications) {
                updateNotificationsUI(data.notifications);
                updateNotificationCounts(data);
                console.log('Notifications refreshed successfully');

                // Clear any previous error messages
                const existingErrors = document.querySelectorAll('.toast.error');
                existingErrors.forEach(error => error.remove());

            } else {
                console.error('API returned error:', data);
                if (data.error) {
                    showToast(`Error: ${data.error}`, 'error');
                } else {
                    showToast('Failed to load notifications. Please refresh the page.', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing notifications:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            // Handle different types of errors
            if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
                console.log('JSON parsing error - likely got HTML instead of JSON (redirected to login)');
                window.location.href = '/login?next=/notifications';
                return;
            }

            if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                showToast('Network error. Please check your connection and try again.', 'error');
                return;
            }

            // Show user-friendly error message only if not redirecting
            if (!error.message.includes('401') && !error.message.includes('403')) {
                showToast(`Failed to load notifications: ${error.message}. Please refresh the page.`, 'error');
            }
        });
}

function updateNotificationsUI(notifications) {
    const timeline = document.querySelector('.timeline');
    const currentNotifications = Array.from(timeline.querySelectorAll('.timeline-item'));
    const currentIds = currentNotifications.map(item => item.getAttribute('data-notification-id'));

    // Check for new notifications
    notifications.forEach(notification => {
        if (!currentIds.includes(notification.id.toString())) {
            // This is a new notification
            addNewNotificationToTimeline(notification);

            // Show browser notification if permission granted
            if (Notification.permission === 'granted') {
                new Notification(notification.title, {
                    body: notification.message,
                    icon: '/static/favicon.ico',
                    tag: 'medivent-notification'
                });
            }

            // Play notification sound
            playNotificationSound();

            // Show toast notification
            showToast(`New notification: ${notification.title}`, 'success');

            // Show screenshot-style notification
            showScreenshotNotification(notification);
        }
    });

    // Update existing notifications that may have changed
    notifications.forEach(notification => {
        const existingItem = document.querySelector(`[data-notification-id="${notification.id}"]`);
        if (existingItem) {
            const currentReadStatus = !existingItem.classList.contains('unread');
            if (currentReadStatus !== notification.is_read) {
                // Read status changed, update the item
                updateExistingNotification(notification);
            }
        }
    });
}

function filterNotifications(filter) {
    $('.timeline-item').hide();

    let visibleCount = 0;
    allNotifications.forEach(notification => {
        let shouldShow = false;

        switch(filter) {
            case 'all':
                shouldShow = true;
                break;
            case 'unread':
                shouldShow = !notification.read;
                break;
            default:
                shouldShow = notification.type === filter;
                break;
        }

        if (shouldShow) {
            notification.element.show();
            visibleCount++;
        }
    });

    // Show empty state if no notifications match filter
    if (visibleCount === 0) {
        showEmptyState(filter);
    } else {
        hideEmptyState();
    }
}

function searchNotifications(searchTerm) {
    if (!searchTerm) {
        // If no search term, show all notifications based on current filter
        const activeFilter = $('.filter-link.active').data('filter');
        filterNotifications(activeFilter);
        return;
    }

    $('.timeline-item').hide();
    let visibleCount = 0;

    allNotifications.forEach(notification => {
        if (notification.text.includes(searchTerm)) {
            notification.element.show();
            visibleCount++;
        }
    });

    if (visibleCount === 0) {
        showEmptyState('search');
    } else {
        hideEmptyState();
    }
}

function showEmptyState(type) {
    hideEmptyState();

    let message, icon;
    switch(type) {
        case 'unread':
            message = 'No unread notifications';
            icon = 'fas fa-check-circle';
            break;
        case 'search':
            message = 'No notifications match your search';
            icon = 'fas fa-search';
            break;
        default:
            message = `No ${type} notifications found`;
            icon = 'fas fa-bell-slash';
            break;
    }

    const emptyState = $(`
        <div class="empty-state">
            <i class="${icon}"></i>
            <h4>${message}</h4>
            <p>Try adjusting your filters or search terms</p>
        </div>
    `);

    $('#notificationTimeline').append(emptyState);
}

function hideEmptyState() {
    $('.empty-state').remove();
}

function markAsRead(notificationId) {
    $.post(`/api/notifications/${notificationId}/read`)
        .done(function() {
            // Update UI
            const item = $(`.timeline-item[data-notification-id="${notificationId}"]`);
            item.removeClass('unread');
            item.find('.unread-indicator').remove();
            item.data('read', 'true');

            // Update the notification in our array
            const notification = allNotifications.find(n =>
                n.element.data('notification-id') == notificationId
            );
            if (notification) {
                notification.read = true;
            }

            // Update button
            const button = item.find('.action-button.secondary');
            button.html('<i class="fas fa-undo"></i>Mark as Unread');
            button.attr('onclick', `markAsUnread('${notificationId}')`);

            showToast('Notification marked as read', 'success');
        })
        .fail(function() {
            console.error('Failed to mark notification as read');
            showToast('Failed to mark as read', 'error');
        });
}

function markAsUnread(notificationId) {
    $.post(`/api/notifications/${notificationId}/unread`)
        .done(function() {
            // Update UI
            const item = $(`.timeline-item[data-notification-id="${notificationId}"]`);
            item.addClass('unread');
            item.prepend('<div class="unread-indicator">NEW</div>');
            item.data('read', 'false');

            // Update the notification in our array
            const notification = allNotifications.find(n =>
                n.element.data('notification-id') == notificationId
            );
            if (notification) {
                notification.read = false;
            }

            // Update button
            const button = item.find('.action-button.secondary');
            button.html('<i class="fas fa-check"></i>Mark as Read');
            button.attr('onclick', `markAsRead('${notificationId}')`);

            showToast('Notification marked as unread', 'success');
        })
        .fail(function() {
            console.error('Failed to mark notification as unread');
            showToast('Failed to mark as unread', 'error');
        });
}

function markAllAsRead() {
    if (confirm('Mark all notifications as read?')) {
        $.post('/api/notifications/mark-all-read')
            .done(function() {
                // Update all notifications in UI
                $('.timeline-item').removeClass('unread');
                $('.unread-indicator').remove();

                // Update all notifications in our array
                allNotifications.forEach(notification => {
                    notification.read = true;
                    notification.element.data('read', 'true');
                });

                // Update all buttons
                $('.timeline-item .action-button.secondary').each(function() {
                    const notificationId = $(this).closest('.timeline-item').data('notification-id');
                    $(this).html('<i class="fas fa-undo"></i>Mark as Unread');
                    $(this).attr('onclick', `markAsUnread('${notificationId}')`);
                });

                showToast('All notifications marked as read', 'success');
            })
            .fail(function() {
                console.error('Failed to mark all notifications as read');
                showToast('Failed to mark all as read', 'error');
            });
    }
}

function addNewNotificationToTimeline(notification) {
    const timeline = document.querySelector('.timeline');
    const newItem = createNotificationElement(notification);

    // Add entrance animation
    newItem.style.opacity = '0';
    newItem.style.transform = 'translateX(-50px)';

    // Insert at the beginning of timeline
    timeline.insertBefore(newItem, timeline.firstChild);

    // Animate in
    setTimeout(() => {
        newItem.style.transition = 'all 0.5s ease';
        newItem.style.opacity = '1';
        newItem.style.transform = 'translateX(0)';
    }, 100);

    // Flash effect for new notification
    setTimeout(() => {
        newItem.style.background = 'linear-gradient(135deg, #e3f2fd, #ffffff)';
        setTimeout(() => {
            newItem.style.background = 'white';
        }, 2000);
    }, 600);
}

function updateExistingNotification(notification) {
    const existingItem = document.querySelector(`[data-notification-id="${notification.id}"]`);
    if (existingItem) {
        // Update the notification content
        const newItem = createNotificationElement(notification);
        existingItem.replaceWith(newItem);

        // Flash effect for updated notification
        newItem.style.background = 'linear-gradient(135deg, #fff3cd, #ffffff)';
        setTimeout(() => {
            newItem.style.background = 'white';
        }, 1500);
    }
}

function createNotificationElement(notification) {
    const item = document.createElement('div');
    item.className = `timeline-item ${notification.is_read ? '' : 'unread'} ${notification.type}`;
    item.setAttribute('data-notification-id', notification.id);

    const typeIcons = {
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle',
        'info': 'info-circle',
        'order': 'shopping-cart'
    };

    const typeColors = {
        'success': 'success',
        'warning': 'warning',
        'error': 'error',
        'info': 'info',
        'order': 'info'
    };

    item.innerHTML = `
        ${!notification.is_read ? '<div class="unread-indicator">New</div>' : ''}
        <div class="notification-header">
            <div class="notification-avatar ${typeColors[notification.type] || 'info'}">
                <i class="fas fa-${typeIcons[notification.type] || 'bell'}"></i>
            </div>
            <div class="notification-details">
                <div class="notification-title">
                    ${notification.title}
                    ${notification.priority === 'critical' ? '<span class="badge badge-danger">Critical</span>' : ''}
                    ${notification.priority === 'high' ? '<span class="badge badge-warning">High</span>' : ''}
                </div>
                <div class="notification-message">${notification.message}</div>
            </div>
        </div>
        <div class="notification-footer">
            <div class="notification-time">
                <i class="fas fa-clock"></i>
                <span>${formatTimeAgo(notification.created_at)}</span>
            </div>
            <div class="notification-actions">
                <button class="action-button secondary" onclick="markAsRead(${notification.id}, ${!notification.is_read})">
                    <i class="fas fa-${notification.is_read ? 'envelope' : 'envelope-open'}"></i>
                    ${notification.is_read ? 'Mark Unread' : 'Mark Read'}
                </button>
                ${notification.action_url ? `<a href="${notification.action_url}" class="action-button primary">
                    <i class="fas fa-external-link-alt"></i>
                    View Details
                </a>` : ''}
            </div>
        </div>
    `;

    return item;
}

function playNotificationSound() {
    try {
        // Create a subtle notification sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
        console.log('Audio notification not supported');
    }
}

function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(function(permission) {
            if (permission === 'granted') {
                showToast('Browser notifications enabled', 'success');
            }
        });
    }
}

function updateNotificationCounts(data) {
    console.log('Updating notification counts:', data);

    // Update the statistics cards with safe element selection
    const totalCountEl = document.querySelector('#total-count');
    if (totalCountEl && data.notifications) {
        totalCountEl.textContent = data.notifications.length;
    }

    const unreadCountEl = document.querySelector('#unread-count');
    if (unreadCountEl && data.unread_count !== undefined) {
        unreadCountEl.textContent = data.unread_count;
        // Update bell badge
        updateBellNotificationBadge(data.unread_count);
    }

    // Calculate critical count from notifications
    const criticalCountEl = document.querySelector('#critical-count');
    if (criticalCountEl && data.notifications) {
        const criticalCount = data.notifications.filter(n =>
            n.notification_type === 'warning' || n.notification_type === 'error'
        ).length;
        criticalCountEl.textContent = criticalCount;
    }

    // Calculate today count from notifications
    const todayCountEl = document.querySelector('#today-count');
    if (todayCountEl && data.notifications) {
        const today = new Date().toISOString().split('T')[0];
        const todayCount = data.notifications.filter(n => {
            if (n.created_at) {
                const notificationDate = n.created_at.split(' ')[0] || n.created_at.split('T')[0];
                return notificationDate === today;
            }
            return false;
        }).length;
        todayCountEl.textContent = todayCount;
    }

    // Update filter counts
    if (data.notifications) {
        const typeCounts = {};
        data.notifications.forEach(n => {
            typeCounts[n.notification_type] = (typeCounts[n.notification_type] || 0) + 1;
        });

        Object.keys(typeCounts).forEach(type => {
            const filterElement = document.querySelector(`[data-filter="${type}"] .filter-count`);
            if (filterElement) {
                filterElement.textContent = typeCounts[type];
            }
        });

        // Update 'all' filter count
        const allFilterElement = document.querySelector(`[data-filter="all"] .filter-count`);
        if (allFilterElement) {
            allFilterElement.textContent = data.notifications.length;
        }

        // Update 'unread' filter count
        const unreadFilterElement = document.querySelector(`[data-filter="unread"] .filter-count`);
        if (unreadFilterElement) {
            unreadFilterElement.textContent = data.unread_count || 0;
        }
    }
}

function updateConnectionStatus(status) {
    const indicator = document.getElementById('connection-status');
    if (indicator) {
        const badge = indicator.querySelector('.badge');
        if (status === 'websocket') {
            badge.className = 'badge badge-success';
            badge.innerHTML = '<i class="fas fa-wifi"></i> WebSocket Connected';
            badge.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        } else if (status === 'polling') {
            badge.className = 'badge badge-warning';
            badge.innerHTML = '<i class="fas fa-sync-alt"></i> Real-time Polling';
            badge.style.background = 'linear-gradient(135deg, #ffc107, #fd7e14)';
        }
    }
}

function addConnectionStatusIndicator() {
    const indicator = $(`
        <div id="connection-status" class="position-fixed" style="bottom: 20px; right: 20px; z-index: 1000;">
            <div class="badge badge-success" style="padding: 8px 12px; border-radius: 20px; font-size: 0.8rem; background: linear-gradient(135deg, #28a745, #20c997); border: none; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                <i class="fas fa-sync-alt"></i> Real-time Polling (1s)
            </div>
        </div>
    `);
    $('body').append(indicator);

    // Hide after 5 seconds
    setTimeout(() => {
        indicator.fadeOut();
    }, 5000);
}

// Bell Icon Notification Functions
function toggleNotificationDropdown() {
    const dropdown = document.getElementById('notification-dropdown');
    const isVisible = dropdown.style.display !== 'none';

    if (isVisible) {
        dropdown.style.display = 'none';
    } else {
        dropdown.style.display = 'block';
        loadBellNotifications();
    }
}

function loadBellNotifications() {
    console.log('Loading bell notifications...');
    fetch('/api/notifications', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
        }
    })
        .then(response => {
            // Check if we were redirected to login page
            if (response.url.includes('/login')) {
                console.log('Bell notifications: Redirected to login page');
                return null;
            }

            if (response.status === 401 || response.status === 403) {
                console.log('Authentication required for bell notifications');
                return null;
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is actually JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.log('Bell notifications: Response is not JSON');
                return null;
            }

            return response.json();
        })
        .then(data => {
            if (!data) return;
            console.log('Bell notifications data:', data);
            if (data.success) {
                updateBellNotificationDropdown(data.notifications);
                updateBellNotificationBadge(data.unread_count);
            } else {
                console.error('Bell notifications API error:', data);
            }
        })
        .catch(error => {
            console.error('Error loading bell notifications:', error);
            // Show fallback content in dropdown
            const dropdownList = document.getElementById('dropdown-notification-list');
            if (dropdownList) {
                dropdownList.innerHTML = `
                    <div class="dropdown-item text-center text-muted">
                        <i class="fas fa-exclamation-triangle"></i><br>
                        Please refresh the page
                    </div>
                `;
            }
        });
}

function updateBellNotificationBadge(unreadCount) {
    const badge = document.getElementById('notification-badge');
    if (unreadCount > 0) {
        badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
        badge.style.display = 'flex';
    } else {
        badge.style.display = 'none';
    }
}

function updateBellNotificationDropdown(notifications) {
    const dropdownList = document.getElementById('dropdown-notification-list');

    if (notifications.length === 0) {
        dropdownList.innerHTML = `
            <div class="dropdown-item text-center text-muted">
                <i class="fas fa-bell-slash"></i><br>
                No notifications
            </div>
        `;
        return;
    }

    let html = '';
    notifications.slice(0, 5).forEach(notification => {
        const timeAgo = formatTimeAgo(notification.created_at);
        const isUnread = !notification.is_read;

        html += `
            <div class="dropdown-item ${isUnread ? 'unread' : ''}" onclick="handleBellNotificationClick(${notification.id}, '${notification.action_url || '#'}')">
                <div class="d-flex align-items-start">
                    <div class="mr-2">
                        <i class="${getNotificationIcon(notification.type)} text-${getNotificationColor(notification.type)}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 font-weight-bold" style="font-size: 0.85rem;">${notification.title}</h6>
                        <p class="mb-1 text-muted" style="font-size: 0.75rem;">${notification.message}</p>
                        <small class="text-muted">${timeAgo}</small>
                    </div>
                    ${isUnread ? '<div class="ml-1"><span class="badge badge-primary badge-sm">New</span></div>' : ''}
                </div>
            </div>
        `;
    });

    dropdownList.innerHTML = html;
}

function handleBellNotificationClick(notificationId, actionUrl) {
    // Mark as read
    fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadBellNotifications();
            refreshNotifications(); // Update main timeline
        }
    });

    // Close dropdown
    document.getElementById('notification-dropdown').style.display = 'none';

    // Navigate to action URL
    if (actionUrl && actionUrl !== '#') {
        setTimeout(() => {
            window.location.href = actionUrl;
        }, 200);
    }
}

function markAllAsReadFromDropdown() {
    fetch('/api/notifications/mark-all-read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadBellNotifications();
            refreshNotifications(); // Update main timeline
            showToast('All notifications marked as read', 'success');
        }
    });
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'info': 'fas fa-info-circle',
        'order': 'fas fa-shopping-cart',
        'system': 'fas fa-cog'
    };
    return icons[type] || 'fas fa-bell';
}

function getNotificationColor(type) {
    const colors = {
        'success': 'success',
        'warning': 'warning',
        'error': 'danger',
        'info': 'primary',
        'order': 'info',
        'system': 'secondary'
    };
    return colors[type] || 'primary';
}

// Screenshot-style notification that appears prominently
function showScreenshotNotification(notification) {
    const screenshotNotification = document.createElement('div');
    screenshotNotification.className = 'screenshot-notification';
    screenshotNotification.innerHTML = `
        <div class="screenshot-notification-content">
            <div class="screenshot-notification-header">
                <div class="screenshot-notification-icon ${getNotificationColor(notification.type)}">
                    <i class="${getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="screenshot-notification-details">
                    <h4 class="screenshot-notification-title">${notification.title}</h4>
                    <p class="screenshot-notification-message">${notification.message}</p>
                    <small class="screenshot-notification-time">Just now</small>
                </div>
                <button class="screenshot-notification-close" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(screenshotNotification);

    // Animate in
    setTimeout(() => {
        screenshotNotification.classList.add('show');
    }, 100);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        screenshotNotification.classList.remove('show');
        setTimeout(() => {
            if (screenshotNotification.parentElement) {
                screenshotNotification.remove();
            }
        }, 300);
    }, 5000);
}



// Test notification function - creates real notification in database
function createTestNotification() {
    console.log('Creating test notification...');

    fetch('/api/notifications/create-test', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (response.status === 401 || response.status === 403) {
            showToast('Please log in to create test notifications', 'error');
            return null;
        }
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (!data) return;

        if (data.success) {
            showToast(`Test notification created: ${data.message}`, 'success');

            // Refresh notifications to show the new one
            setTimeout(() => {
                refreshNotifications();
                loadBellNotifications();
            }, 500);

            // Show screenshot notification for immediate feedback
            const testNotification = {
                id: Date.now(),
                title: 'Test Notification Created!',
                message: `A real test notification has been created in the database. Type: ${data.type}`,
                type: 'success',
                is_read: false,
                created_at: new Date().toISOString(),
                action_url: '/dashboard'
            };

            showScreenshotNotification(testNotification);

            // Show browser notification if permission granted
            if (Notification.permission === 'granted') {
                new Notification(testNotification.title, {
                    body: testNotification.message,
                    icon: '/static/favicon.ico',
                    tag: 'medivent-notification'
                });
            }

            // Play notification sound
            playNotificationSound();

        } else {
            showToast(`Error creating test notification: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error creating test notification:', error);
        showToast('Failed to create test notification. Please try again.', 'error');
    });
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('notification-dropdown');
    const bell = document.querySelector('.notification-bell');

    if (dropdown && bell && !bell.contains(event.target)) {
        dropdown.style.display = 'none';
    }
});

function formatTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        // For older dates, show the actual date
        return date.toLocaleDateString();
    }
}

function showToast(message, type) {
    const toastId = 'toast-' + Date.now();
    const toast = $(`
        <div id="${toastId}" class="position-fixed" style="top: 30px; right: 30px; z-index: 9999;">
            <div class="alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show shadow"
                 style="border-radius: 10px;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    `);

    $('body').append(toast);

    // Auto-remove after 3 seconds
    setTimeout(function() {
        $(`#${toastId}`).fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}

// Cleanup on page unload
$(window).on('beforeunload', function() {
    if (notificationInterval) {
        clearInterval(notificationInterval);
    }
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate__animated {
        animation-duration: 0.6s;
        animation-fill-mode: both;
    }

    .animate__fadeInUp {
        animation-name: fadeInUp;
    }
`;
document.head.appendChild(style);
</script>

</body>
</html>
