{% extends 'base.html' %}

{% block title %}Add New Product - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Add New Product</h4>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('new_product') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Product Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="strength">Strength</label>
                                    <input type="text" class="form-control" id="strength" name="strength" placeholder="e.g., 10mg, 100ml" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="manufacturer">Manufacturer</label>
                                    <input type="text" class="form-control" id="manufacturer" name="manufacturer" value="Medivent">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="category">Category</label>
                                    <input type="text" class="form-control" id="category" name="category">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="division_id">Division</label>
                                    <select class="form-control" id="division_id" name="division_id">
                                        <option value="">-- Select Division --</option>
                                        {% for division in divisions %}
                                        <option value="{{ division.division_id }}">{{ division.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="unit_of_measure">Unit of Measure</label>
                                    <select class="form-control" id="unit_of_measure" name="unit_of_measure">
                                        <option value="Tablet">Tablet</option>
                                        <option value="Capsule">Capsule</option>
                                        <option value="Bottle">Bottle</option>
                                        <option value="Vial">Vial</option>
                                        <option value="Ampule">Ampule</option>
                                        <option value="Box">Box</option>
                                        <option value="Pack">Pack</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="unit_price">Unit Price (Rs.)</label>
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="min_stock_level">Minimum Stock Level</label>
                                    <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12 text-right">
                                <a href="{{ url_for('products') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Add Product</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
