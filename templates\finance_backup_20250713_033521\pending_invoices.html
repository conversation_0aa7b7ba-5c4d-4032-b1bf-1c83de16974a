{% extends 'base.html' %}

{% block title %}Finance - Pending Invoices{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-file-invoice-dollar"></i> Finance - Pending Invoice Generation
                    </h4>
                    <small>Orders with Delivery Challans awaiting invoice generation</small>
                </div>
                <div class="card-body">

                    {% if pending_invoices %}
                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ summary.total_pending }}</h4>
                                    <p class="mb-0">Total Pending</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>{{ summary.urgent_count }}</h4>
                                    <p class="mb-0">Urgent</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h4>{{ summary.high_count }}</h4>
                                    <p class="mb-0">High Priority</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ "{:,.0f}".format(summary.total_amount) }}</h4>
                                    <p class="mb-0">Total Amount</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>NEW Finance Workflow:</strong> Review customer financial history, verify credit limits, and generate invoices with enhanced controls.
                    </div>

                    <!-- Enhanced Pending Invoices Cards -->
                    {% for invoice in pending_invoices %}
                    <div class="card mb-3 border-left-warning">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="mb-0">
                                        Order {{ invoice.order_id }} - DC {{ invoice.dc_number }}
                                        <span class="badge
                                            {% if invoice.priority_level == 'urgent' %}badge-danger
                                            {% elif invoice.priority_level == 'high' %}badge-warning
                                            {% else %}badge-success{% endif %}">
                                            {{ invoice.priority_level|title }}
                                        </span>
                                        {% if invoice.is_credit_hold %}
                                        <span class="badge badge-danger">Credit Hold</span>
                                        {% endif %}
                                    </h5>
                                    <p class="mb-0 text-muted">
                                        DC Generated: {{ invoice.dc_generated_date }} by {{ invoice.dc_generated_by }}
                                    </p>
                                </div>
                                <div class="col-md-4 text-right">
                                    <h4 class="text-primary">₹{{ "{:,.2f}".format(invoice.total_amount) }}</h4>
                                    <small class="text-muted">{{ invoice.branch_code }} Branch</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Customer Information -->
                                <div class="col-md-6">
                                    <h6><i class="fas fa-user"></i> Customer Information</h6>
                                    <p class="mb-1"><strong>{{ invoice.customer_name }}</strong></p>
                                    <p class="mb-1">{{ invoice.customer_address }}</p>
                                    <p class="mb-1">📞 {{ invoice.customer_phone }}</p>

                                    {% if invoice.credit_limit %}
                                    <div class="mt-2 p-2 bg-light rounded">
                                        <small class="text-muted">Credit Limit: ₹{{ "{:,.0f}".format(invoice.credit_limit) }}</small><br>
                                        <small class="text-muted">Outstanding: ₹{{ "{:,.0f}".format(invoice.total_outstanding or 0) }}</small><br>
                                        <small class="text-muted">Rating: {{ invoice.credit_rating or 'B' }}</small>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Financial History -->
                                <div class="col-md-6">
                                    <h6><i class="fas fa-history"></i> Recent Payment History</h6>
                                    {% if invoice.payment_history %}
                                        {% for payment in invoice.payment_history[:3] %}
                                        <div class="d-flex justify-content-between">
                                            <span>{{ payment.payment_date }}</span>
                                            <span class="text-success">₹{{ "{:,.0f}".format(payment.amount) }}</span>
                                            <span class="badge badge-info">{{ payment.payment_method|title }}</span>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <p class="text-muted">No recent payment history</p>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="btn-group float-right">
                                        <a href="{{ url_for('finance_pending_invoice_details', pending_id=invoice.pending_id) }}"
                                           class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>

                                        {% if not invoice.is_credit_hold %}
                                        <form method="POST" action="{{ url_for('finance_generate_invoice_from_pending', pending_id=invoice.pending_id) }}"
                                              class="d-inline">
                                            <button type="submit" class="btn btn-success btn-sm"
                                                    onclick="return confirm('Generate invoice for {{ invoice.customer_name }}?\n\nOrder: {{ invoice.order_id }}\nAmount: ₹{{ \"{:,.2f}\".format(invoice.total_amount) }}')">
                                                <i class="fas fa-file-invoice"></i> Generate Invoice
                                            </button>
                                        </form>
                                        {% endif %}

                                        <button type="button" class="btn btn-warning btn-sm"
                                                data-toggle="modal" data-target="#holdModal{{ invoice.pending_id }}">
                                            <i class="fas fa-pause"></i> Hold
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    {% else %}
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>No Pending Invoices</h5>
                        <p>All orders with delivery challans have invoices generated.</p>
                        <a href="{{ url_for('workflow') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Workflow
                        </a>
                    </div>
                    {% endif %}

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Finance Workflow Information Modal -->
<div class="modal fade" id="workflowInfoModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">Finance Workflow Process</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <h6><i class="fas fa-list-ol"></i> Complete Order Workflow:</h6>
                <ol class="list-group list-group-numbered">
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Boss Approval</div>
                            Order gets approved by management
                        </div>
                        <span class="badge bg-success rounded-pill">✓</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Warehouse Processing</div>
                            Warehouse selects batches and generates Delivery Challan
                        </div>
                        <span class="badge bg-success rounded-pill">✓</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Finance Review</div>
                            Finance reviews customer ledger, verifies pricing, generates Invoice
                        </div>
                        <span class="badge bg-warning rounded-pill">Current</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Dispatch</div>
                            Order dispatched to customer with invoice
                        </div>
                        <span class="badge bg-secondary rounded-pill">Pending</span>
                    </li>
                </ol>

                <hr>

                <h6><i class="fas fa-clipboard-check"></i> Finance Checklist:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Review customer payment history</li>
                    <li><i class="fas fa-check text-success"></i> Verify product pricing accuracy</li>
                    <li><i class="fas fa-check text-success"></i> Confirm delivery challan details</li>
                    <li><i class="fas fa-check text-success"></i> Check for any special discounts</li>
                    <li><i class="fas fa-check text-success"></i> Generate professional invoice</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // No DataTable initialization since we're using cards, not tables

    // Auto-refresh every 30 seconds (optional)
    // setTimeout(function() {
    //     location.reload();
    // }, 30000);

    console.log('Pending invoices page loaded successfully');
});
</script>
{% endblock %}
