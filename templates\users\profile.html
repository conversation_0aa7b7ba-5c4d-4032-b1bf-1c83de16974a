{% extends 'base.html' %}

{% block title %}User Profile{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">User Profile</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user-circle fa-6x text-primary"></i>
                                    </div>
                                    <h4>{{ user.full_name or user.username }}</h4>
                                    <p class="text-muted">{{ user.role|title }}</p>
                                    <p><i class="fas fa-envelope mr-2"></i>{{ user.email or 'No email provided' }}</p>
                                    <p><i class="fas fa-clock mr-2"></i>Last login: {{ user.last_login|default('Never', true) }}</p>
                                    <p><i class="fas fa-calendar-alt mr-2"></i>Account created: {{ user.created_at|default('Unknown', true) }}</p>
                                    <div class="mt-3">
                                        <span class="badge badge-{{ 'success' if user.status == 'active' else 'danger' }} p-2">
                                            {{ user.status|title }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="edit-tab" data-toggle="tab" href="#edit" role="tab" aria-controls="edit" aria-selected="true">
                                        <i class="fas fa-edit mr-2"></i>Edit Profile
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="password-tab" data-toggle="tab" href="#password" role="tab" aria-controls="password" aria-selected="false">
                                        <i class="fas fa-key mr-2"></i>Change Password
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="activity-tab" data-toggle="tab" href="#activity" role="tab" aria-controls="activity" aria-selected="false">
                                        <i class="fas fa-history mr-2"></i>Recent Activity
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content p-3 border border-top-0 rounded-bottom" id="profileTabsContent">
                                <div class="tab-pane fade show active" id="edit" role="tabpanel" aria-labelledby="edit-tab">
                                    <form method="post" action="{{ url_for('users', action='update_profile') }}">
                                        <div class="form-group">
                                            <label for="username">Username</label>
                                            <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" readonly>
                                            <small class="form-text text-muted">Username cannot be changed</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="full_name">Full Name</label>
                                            <input type="text" class="form-control" id="full_name" name="full_name" value="{{ user.full_name or '' }}">
                                        </div>
                                        <div class="form-group">
                                            <label for="email">Email</label>
                                            <input type="email" class="form-control" id="email" name="email" value="{{ user.email or '' }}">
                                        </div>
                                        <button type="submit" class="btn btn-primary">Update Profile</button>
                                    </form>
                                </div>
                                <div class="tab-pane fade" id="password" role="tabpanel" aria-labelledby="password-tab">
                                    <form method="post" action="{{ url_for('users', action='change_password') }}">
                                        <div class="form-group">
                                            <label for="current_password">Current Password</label>
                                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="new_password">New Password</label>
                                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                                            <small class="form-text text-muted">Password must be at least 8 characters long and include uppercase, lowercase, numbers and special characters</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="confirm_password">Confirm New Password</label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        </div>
                                        <div class="password-strength-meter mt-2 mb-3">
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <small class="form-text text-muted password-strength-text">Password strength: Too weak</small>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Change Password</button>
                                    </form>
                                </div>
                                <div class="tab-pane fade" id="activity" role="tabpanel" aria-labelledby="activity-tab">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Timestamp</th>
                                                    <th>Action</th>
                                                    <th>Details</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if activity_logs %}
                                                    {% for log in activity_logs %}
                                                    <tr>
                                                        <td>{{ safe_strftime(log.timestamp, '%Y-%m-%d %H:%M:%S') if log.timestamp else 'Unknown' }}</td>
                                                        <td>{{ log.action }}</td>
                                                        <td>{{ log.details }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="3" class="text-center">No activity logs found</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Password strength meter
        $('#new_password').on('input', function() {
            var password = $(this).val();
            var strength = 0;

            if (password.length >= 8) strength += 25;
            if (password.match(/[A-Z]/)) strength += 25;
            if (password.match(/[a-z]/)) strength += 25;
            if (password.match(/[0-9]/) || password.match(/[^A-Za-z0-9]/)) strength += 25;

            var progressBar = $('.password-strength-meter .progress-bar');
            progressBar.css('width', strength + '%');
            progressBar.attr('aria-valuenow', strength);

            var strengthText = $('.password-strength-text');
            if (strength <= 25) {
                progressBar.removeClass().addClass('progress-bar bg-danger');
                strengthText.text('Password strength: Too weak');
            } else if (strength <= 50) {
                progressBar.removeClass().addClass('progress-bar bg-warning');
                strengthText.text('Password strength: Weak');
            } else if (strength <= 75) {
                progressBar.removeClass().addClass('progress-bar bg-info');
                strengthText.text('Password strength: Good');
            } else {
                progressBar.removeClass().addClass('progress-bar bg-success');
                strengthText.text('Password strength: Strong');
            }
        });

        // Password confirmation validation
        $('#confirm_password').on('input', function() {
            if ($(this).val() !== $('#new_password').val()) {
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
    });
</script>
{% endblock %}
