{% extends 'base.html' %}

{% block title %}🚀 Ultra Modern Pending Invoices - Medivent ERP 2025{% endblock %}

{% block content %}
<style>
/* 🎨 ULTRA MODERN PENDING INVOICES STYLES */
.ultra-pending-invoices {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.ultra-header-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.ultra-header-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #f6c23e, #e74a3b, #dc3545);
}

.ultra-title {
    background: linear-gradient(45deg, #f6c23e, #e74a3b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 3rem;
    margin: 0;
}

.ultra-filter-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ultra-stat-mini {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.ultra-stat-mini:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.ultra-stat-mini.urgent {
    border-left: 5px solid #e74a3b;
}

.ultra-stat-mini.high {
    border-left: 5px solid #f6c23e;
}

.ultra-stat-mini.normal {
    border-left: 5px solid #1cc88a;
}

.ultra-stat-mini.total {
    border-left: 5px solid #4e73df;
}

.stat-number-mini {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 10px;
}

.stat-number-mini.urgent { color: #e74a3b; }
.stat-number-mini.high { color: #f6c23e; }
.stat-number-mini.normal { color: #1cc88a; }
.stat-number-mini.total { color: #4e73df; }

.ultra-invoice-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.ultra-invoice-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.ultra-invoice-card.priority-urgent {
    border-left: 6px solid #e74a3b;
    background: linear-gradient(135deg, rgba(231, 74, 59, 0.05), rgba(255, 255, 255, 0.95));
}

.ultra-invoice-card.priority-high {
    border-left: 6px solid #f6c23e;
    background: linear-gradient(135deg, rgba(246, 194, 62, 0.05), rgba(255, 255, 255, 0.95));
}

.ultra-invoice-card.priority-normal {
    border-left: 6px solid #1cc88a;
    background: linear-gradient(135deg, rgba(28, 200, 138, 0.05), rgba(255, 255, 255, 0.95));
}

.priority-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.priority-badge.urgent {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    animation: pulse 2s infinite;
}

.priority-badge.high {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    color: white;
}

.priority-badge.normal {
    background: linear-gradient(135deg, #1cc88a, #17a673);
    color: white;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.ultra-btn {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
}

.ultra-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.ultra-btn.success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.ultra-btn.warning {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    box-shadow: 0 10px 20px rgba(246, 194, 62, 0.3);
}

.ultra-btn.danger {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    box-shadow: 0 10px 20px rgba(231, 74, 59, 0.3);
}

.ultra-btn.info {
    background: linear-gradient(135deg, #36b9cc, #258391);
    box-shadow: 0 10px 20px rgba(54, 185, 204, 0.3);
}

.filter-form {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.days-pending {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 12px;
    display: inline-block;
}

.days-pending.urgent {
    background: rgba(231, 74, 59, 0.1);
    color: #e74a3b;
}

.days-pending.high {
    background: rgba(246, 194, 62, 0.1);
    color: #f6c23e;
}

.days-pending.normal {
    background: rgba(28, 200, 138, 0.1);
    color: #1cc88a;
}

@media (max-width: 768px) {
    .ultra-title {
        font-size: 2rem;
    }
    .stat-number-mini {
        font-size: 2rem;
    }
}
</style>

<div class="ultra-pending-invoices">
    <div class="container-fluid">
        <!-- 🎯 HEADER SECTION -->
        <div class="ultra-header-card">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="ultra-title">
                        <i class="fas fa-file-invoice-dollar mr-3"></i>Pending Invoices
                    </h1>
                    <p class="text-muted mb-0 mt-2 h5">Advanced filtering and priority management system</p>
                </div>
                <div>
                    <a href="{{ url_for('new_modern_finance_dashboard') }}" class="ultra-btn">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Finance
                    </a>
                    <button class="ultra-btn success" onclick="bulkGenerateInvoices()">
                        <i class="fas fa-magic mr-2"></i>Bulk Generate
                    </button>
                </div>
            </div>
        </div>

        <!-- 📊 SUMMARY STATISTICS -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="ultra-stat-mini total">
                    <div class="stat-number-mini total">{{ summary.total_pending }}</div>
                    <div class="font-weight-bold text-primary">Total Pending</div>
                    <small class="text-muted">All invoices awaiting processing</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="ultra-stat-mini urgent">
                    <div class="stat-number-mini urgent">{{ summary.urgent_count }}</div>
                    <div class="font-weight-bold text-danger">Urgent (7+ days)</div>
                    <small class="text-muted">Requires immediate attention</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="ultra-stat-mini high">
                    <div class="stat-number-mini high">{{ summary.high_count }}</div>
                    <div class="font-weight-bold text-warning">High Priority (3-7 days)</div>
                    <small class="text-muted">Process soon</small>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="ultra-stat-mini normal">
                    <div class="stat-number-mini normal">{{ summary.normal_count }}</div>
                    <div class="font-weight-bold text-success">Normal (0-3 days)</div>
                    <small class="text-muted">Standard processing</small>
                </div>
            </div>
        </div>

        <!-- 🔍 ADVANCED FILTERS -->
        <div class="ultra-filter-card">
            <h5 class="font-weight-bold text-primary mb-4">
                <i class="fas fa-filter mr-2"></i>Advanced Filters
            </h5>
            <form method="GET" class="filter-form">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="font-weight-bold">Priority Level</label>
                        <select name="priority" class="form-control">
                            <option value="all" {{ 'selected' if filters.priority == 'all' else '' }}>All Priorities</option>
                            <option value="urgent" {{ 'selected' if filters.priority == 'urgent' else '' }}>Urgent Only</option>
                            <option value="high" {{ 'selected' if filters.priority == 'high' else '' }}>High Priority</option>
                            <option value="normal" {{ 'selected' if filters.priority == 'normal' else '' }}>Normal</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="font-weight-bold">Customer</label>
                        <select name="customer" class="form-control">
                            <option value="">All Customers</option>
                            {% for customer in customers %}
                            <option value="{{ customer.name }}" {{ 'selected' if filters.customer == customer.name else '' }}>
                                {{ customer.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="font-weight-bold">Date From</label>
                        <input type="date" name="date_from" class="form-control" value="{{ filters.date_from }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="font-weight-bold">Date To</label>
                        <input type="date" name="date_to" class="form-control" value="{{ filters.date_to }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="font-weight-bold">Actions</label><br>
                        <button type="submit" class="ultra-btn info">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="font-weight-bold">Min Amount</label>
                        <input type="number" name="amount_min" class="form-control" placeholder="0" value="{{ filters.amount_min }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="font-weight-bold">Max Amount</label>
                        <input type="number" name="amount_max" class="form-control" placeholder="999999" value="{{ filters.amount_max }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="font-weight-bold">Total Amount</label>
                        <div class="h5 text-success font-weight-bold mt-2">
                            Rs. {{ "{:,.0f}"|format(summary.total_amount) }}
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="font-weight-bold">Avg Days Pending</label>
                        <div class="h5 text-info font-weight-bold mt-2">
                            {{ "{:.1f}"|format(summary.avg_days_pending) }} days
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 📋 PENDING INVOICES LIST -->
        <div class="ultra-filter-card">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <h5 class="font-weight-bold text-primary mb-0">
                    <i class="fas fa-list mr-2"></i>Pending Invoices ({{ summary.total_pending }})
                </h5>
                <div>
                    <button class="ultra-btn warning" onclick="exportToExcel()">
                        <i class="fas fa-file-excel mr-2"></i>Export Excel
                    </button>
                    <button class="ultra-btn info" onclick="printInvoices()">
                        <i class="fas fa-print mr-2"></i>Print List
                    </button>
                </div>
            </div>

            {% if pending_invoices %}
                {% for invoice in pending_invoices %}
                <div class="ultra-invoice-card priority-{{ invoice.priority }}">
                    <div class="priority-badge {{ invoice.priority }}">
                        {{ invoice.priority|title }}
                        {% if invoice.priority == 'urgent' %}
                        <i class="fas fa-fire ml-1"></i>
                        {% endif %}
                    </div>
                    
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="font-weight-bold text-primary mb-1">{{ invoice.order_id }}</h6>
                            <small class="text-muted">{{ invoice.order_date[:10] if invoice.order_date else 'N/A' }}</small>
                            <div class="days-pending {{ invoice.priority }} mt-1">
                                <i class="fas fa-clock mr-1"></i>{{ "{:.0f}"|format(invoice.days_pending) }} days
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <h6 class="font-weight-bold text-dark mb-1">{{ invoice.customer_name }}</h6>
                            <small class="text-muted d-block">ID: {{ invoice.customer_id }}</small>
                            {% if invoice.customer_phone %}
                            <small class="text-muted d-block">
                                <i class="fas fa-phone mr-1"></i>{{ invoice.customer_phone }}
                            </small>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-2">
                            <div class="font-weight-bold text-success h5 mb-1">
                                Rs. {{ "{:,.0f}"|format(invoice.order_amount) }}
                            </div>
                            <small class="text-muted">Order Amount</small>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="btn-group-vertical w-100" role="group">
                                <button class="ultra-btn success mb-2" onclick="generateInvoice('{{ invoice.order_id }}')">
                                    <i class="fas fa-file-invoice mr-2"></i>Generate Invoice
                                </button>
                                <div class="btn-group" role="group">
                                    <button class="ultra-btn" onclick="viewOrderDetails('{{ invoice.order_id }}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="ultra-btn warning" onclick="holdInvoice('{{ invoice.order_id }}')">
                                        <i class="fas fa-pause"></i> Hold
                                    </button>
                                    <button class="ultra-btn info" onclick="contactCustomer('{{ invoice.customer_phone }}')">
                                        <i class="fas fa-phone"></i> Call
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                    <h4 class="text-muted">No Pending Invoices Found</h4>
                    <p class="text-muted">All invoices have been processed or no invoices match your filter criteria.</p>
                    <a href="{{ url_for('new_modern_finance_dashboard') }}" class="ultra-btn">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Finance Dashboard
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// 🚀 ULTRA MODERN PENDING INVOICES JAVASCRIPT
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on load
    const cards = document.querySelectorAll('.ultra-invoice-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

function generateInvoice(orderId) {
    if (confirm(`Generate invoice for order ${orderId}?`)) {
        showNotification('Generating invoice...', 'info');
        
        fetch(`/finance/generate-invoice/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Invoice generated successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification(data.message || 'Error generating invoice', 'danger');
            }
        })
        .catch(error => {
            showNotification('Error generating invoice', 'danger');
        });
    }
}

function viewOrderDetails(orderId) {
    window.open(`/orders/${orderId}/view`, '_blank');
}

function holdInvoice(orderId) {
    const reason = prompt('Enter reason for holding this invoice:');
    if (reason) {
        showNotification('Invoice held successfully!', 'warning');
        // Implement hold logic
    }
}

function contactCustomer(phone) {
    if (phone) {
        window.open(`tel:${phone}`);
    } else {
        showNotification('No phone number available', 'warning');
    }
}

function bulkGenerateInvoices() {
    if (confirm('Generate all visible pending invoices? This action cannot be undone.')) {
        showNotification('Bulk generating invoices...', 'info');
        // Implement bulk generation logic
    }
}

function exportToExcel() {
    showNotification('Exporting to Excel...', 'info');
    window.location.href = '/finance/export-pending-invoices?format=excel';
}

function printInvoices() {
    window.print();
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; border-radius: 15px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'info' ? 'info-circle' : 'times-circle'} mr-2"></i>
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
