<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notifications - Medivent ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-bell"></i> Notification System Test</h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Test different types of notifications to ensure the real-time system is working correctly.</p>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-success w-100" onclick="createTestNotification('success')">
                                    <i class="fas fa-check-circle"></i> Success Notification
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-warning w-100" onclick="createTestNotification('warning')">
                                    <i class="fas fa-exclamation-triangle"></i> Warning Notification
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-danger w-100" onclick="createTestNotification('error')">
                                    <i class="fas fa-times-circle"></i> Error Notification
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-info w-100" onclick="createTestNotification('info')">
                                    <i class="fas fa-info-circle"></i> Info Notification
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-primary w-100" onclick="createTestNotification('order')">
                                    <i class="fas fa-shopping-cart"></i> Order Notification
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-secondary w-100" onclick="testGlobalNotification()">
                                    <i class="fas fa-globe"></i> Global Notification
                                </button>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <h5>Quick Actions</h5>
                                <div class="btn-group w-100" role="group">
                                    <a href="/notifications" class="btn btn-outline-primary">
                                        <i class="fas fa-bell"></i> View Notifications
                                    </a>
                                    <a href="/dashboard" class="btn btn-outline-secondary">
                                        <i class="fas fa-tachometer-alt"></i> Dashboard
                                    </a>
                                    <button class="btn btn-outline-success" onclick="requestNotificationPermission()">
                                        <i class="fas fa-check"></i> Enable Browser Notifications
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> How to Test:</h6>
                                <ol>
                                    <li>Click any notification type button above</li>
                                    <li>Check if a notification appears on screen (screenshot-style)</li>
                                    <li>Visit the <a href="/notifications">Notifications Page</a> to see if it appears in the timeline</li>
                                    <li>Check if the bell icon shows a notification badge</li>
                                    <li>Enable browser notifications for full testing</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function createTestNotification(type) {
            console.log(`Creating ${type} test notification...`);
            
            fetch('/api/notifications/create-test', {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (response.status === 401 || response.status === 403) {
                    alert('Please log in first at /login');
                    return null;
                }
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data) return;
                
                if (data.success) {
                    // Show global notification
                    if (window.showGlobalNotification) {
                        window.showGlobalNotification(
                            'Test Notification Created!',
                            `A ${data.type} notification has been created and should appear in the notifications page.`,
                            'success'
                        );
                    } else {
                        alert(`Test notification created: ${data.message}`);
                    }
                } else {
                    alert(`Error: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to create test notification. Make sure you are logged in.');
            });
        }
        
        function testGlobalNotification() {
            if (window.showGlobalNotification) {
                window.showGlobalNotification(
                    'Global Notification Test',
                    'This is a test of the global notification system that appears on all pages.',
                    'info'
                );
            } else {
                alert('Global notification function not available');
            }
        }
        
        function requestNotificationPermission() {
            if ('Notification' in window) {
                Notification.requestPermission().then(function(permission) {
                    if (permission === 'granted') {
                        alert('Browser notifications enabled!');
                        new Notification('Test Browser Notification', {
                            body: 'Browser notifications are now enabled for Medivent ERP',
                            icon: '/static/favicon.ico'
                        });
                    } else {
                        alert('Browser notifications permission denied');
                    }
                });
            } else {
                alert('Browser notifications not supported');
            }
        }
    </script>
</body>
</html>
