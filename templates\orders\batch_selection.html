{% extends 'base.html' %}

{% block title %}Batch Selection - Order #{{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Batch Selection - Order #{{ order.order_id }}</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Order Details</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Order ID:</th>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <th>Customer:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Address:</th>
                                    <td>{{ order.customer_address }}</td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>{{ order.customer_phone }}</td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Batch Selection Method</h5>
                            <form id="batch-selection-method-form" method="post" action="{{ url_for('select_batch_method', order_id=order.order_id) }}">
                                <div class="form-group">
                                    <label for="selection_method">Select Method:</label>
                                    <select class="form-control" id="selection_method" name="selection_method">
                                        <option value="fifo" {% if selection_method == 'fifo' %}selected{% endif %}>FIFO (First In, First Out)</option>
                                        <option value="lifo" {% if selection_method == 'lifo' %}selected{% endif %}>LIFO (Last In, First Out)</option>
                                        <option value="manual" {% if selection_method == 'manual' %}selected{% endif %}>Manual Selection</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse:</label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id">
                                        <option value="">All Warehouses</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}" {% if warehouse_id == warehouse.warehouse_id %}selected{% endif %}>
                                            {{ warehouse.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                    </div>

                    <form method="post" action="{{ url_for('approve_order_with_batches', order_id=order.order_id) }}">
                        <input type="hidden" name="selection_method" value="{{ selection_method }}">
                        <input type="hidden" name="warehouse_id" value="{{ warehouse_id }}">

                        <div class="table-responsive">
                            {% for item in order_items %}
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <strong>{{ item.product_name }}</strong> ({{ item.product_id }})
                                    </h5>
                                    <span class="badge badge-light">
                                        Ordered Quantity: {{ item.quantity }}
                                    </span>
                                    <input type="hidden" name="product_ids[]" value="{{ item.product_id }}">
                                    <input type="hidden" name="ordered_quantities[]" value="{{ item.quantity }}">
                                </div>
                                <div class="card-body">
                                    {% if item.batches %}
                                        <div class="alert alert-info mb-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>Total Available:</strong> {{ item.batches|sum(attribute='stock_quantity') - item.batches|sum(attribute='allocated_quantity') }}
                                                </div>
                                                <div class="col-md-6 text-right">
                                                    <strong>Batch Count:</strong> {{ item.batches|length }}
                                                </div>
                                            </div>
                                        </div>

                                        <table class="table table-striped table-bordered">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Batch Number</th>
                                                    <th>Mfg Date</th>
                                                    <th>Exp Date</th>
                                                    <th>Warehouse</th>
                                                    <th>Available</th>
                                                    <th>Quantity to Use</th>
                                                    {% if selection_method == 'manual' %}
                                                    <th>Actions</th>
                                                    {% endif %}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for batch in item.batches %}
                                                <tr class="batch-row">
                                                    <td>
                                                        {{ batch.batch_number }}
                                                        <input type="hidden" name="batch_ids[{{ item.product_id }}][]" value="{{ batch.id }}">
                                                    </td>
                                                    <td>{{ batch.manufacturing_date }}</td>
                                                    <td>{{ batch.expiry_date }}</td>
                                                    <td>{{ batch.warehouse_name }}</td>
                                                    <td>{{ batch.stock_quantity - batch.allocated_quantity }}</td>
                                                    <td>
                                                        {% if selection_method == 'manual' %}
                                                        <input type="number" class="form-control batch-quantity"
                                                               name="batch_quantities[{{ item.product_id }}][]"
                                                               min="0" max="{{ batch.stock_quantity - batch.allocated_quantity }}"
                                                               value="{{ batch.allocation_quantity }}"
                                                               data-product-id="{{ item.product_id }}"
                                                               data-ordered-quantity="{{ item.quantity }}"
                                                               data-available="{{ batch.stock_quantity - batch.allocated_quantity }}">
                                                        {% else %}
                                                        <input type="number" class="form-control"
                                                               name="batch_quantities[{{ item.product_id }}][]"
                                                               value="{{ batch.allocation_quantity }}" readonly>
                                                        {% endif %}
                                                    </td>
                                                    {% if selection_method == 'manual' %}
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary use-max-btn"
                                                                data-max="{{ batch.stock_quantity - batch.allocated_quantity }}"
                                                                data-remaining="{{ item.quantity - item.batches|sum(attribute='allocation_quantity') }}">
                                                            Use Max
                                                        </button>
                                                    </td>
                                                    {% endif %}
                                                </tr>
                                                {% endfor %}

                                                <!-- Summary row -->
                                                <tr class="table-info">
                                                    <td colspan="4" class="text-right">
                                                        <strong>Total Allocated:</strong>
                                                    </td>
                                                    <td colspan="{% if selection_method == 'manual' %}3{% else %}2{% endif %}">
                                                        <span class="total-allocated">{{ item.batches|sum(attribute='allocation_quantity') }}</span> / {{ item.quantity }}

                                                        {% if item.batches|sum(attribute='allocation_quantity') < item.quantity %}
                                                        <span class="badge badge-warning ml-2">
                                                            Remaining: {{ item.quantity - item.batches|sum(attribute='allocation_quantity') }}
                                                        </span>
                                                        {% else %}
                                                        <span class="badge badge-success ml-2">Fully Allocated</span>
                                                        {% endif %}

                                                        {% if selection_method == 'manual' %}
                                                        <button type="button" class="btn btn-sm btn-success ml-3 auto-allocate-btn"
                                                                data-product-id="{{ item.product_id }}"
                                                                data-ordered-quantity="{{ item.quantity }}">
                                                            Auto-Allocate Remaining
                                                        </button>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    {% else %}
                                        <div class="alert alert-danger">
                                            No available batches for this product
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="form-group mt-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="approve_with_pending" name="approve_with_pending" checked>
                                <label class="form-check-label" for="approve_with_pending">
                                    Approve order even if some products have insufficient stock
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                If checked, the order will be approved and a delivery challan will be generated for available products.
                                Unavailable products will be marked as pending orders.
                            </small>
                        </div>

                        <div class="form-group text-right mt-4">
                            <a href="{{ url_for('workflow') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-success">Approve Order & Generate Challan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    $(document).ready(function() {
        // Update total allocated when batch quantities change
        function updateTotalAllocated(productId) {
            let totalAllocated = 0;

            // Sum all allocations for this product
            $(`input[name="batch_quantities[${productId}][]"]`).each(function() {
                totalAllocated += parseInt($(this).val() || 0);
            });

            // Update the total display
            const orderedQuantity = parseInt($(`[data-product-id="${productId}"]`).first().data('ordered-quantity'));
            const totalElement = $(`[data-product-id="${productId}"]`).closest('.card').find('.total-allocated');
            totalElement.text(totalAllocated);

            // Update remaining badge
            const badgeElement = totalElement.siblings('.badge');
            if (totalAllocated < orderedQuantity) {
                badgeElement.removeClass('badge-success').addClass('badge-warning');
                badgeElement.html(`Remaining: ${orderedQuantity - totalAllocated}`);
            } else {
                badgeElement.removeClass('badge-warning').addClass('badge-success');
                badgeElement.html('Fully Allocated');
            }

            // Update remaining values for Use Max buttons
            $(`.use-max-btn[data-product-id="${productId}"]`).each(function() {
                $(this).attr('data-remaining', orderedQuantity - totalAllocated);
            });

            return { totalAllocated, orderedQuantity };
        }

        // For manual selection, validate that quantities don't exceed ordered amount
        $('.batch-quantity').on('change', function() {
            const productId = $(this).data('product-id');
            const { totalAllocated, orderedQuantity } = updateTotalAllocated(productId);

            // If total exceeds ordered quantity, show warning
            if (totalAllocated > orderedQuantity) {
                alert(`Warning: Total allocated quantity (${totalAllocated}) exceeds ordered quantity (${orderedQuantity})`);
            }
        });

        // Use Max button functionality
        $('.use-max-btn').on('click', function() {
            const max = parseInt($(this).data('max'));
            const remaining = parseInt($(this).data('remaining'));
            const input = $(this).closest('tr').find('.batch-quantity');
            const productId = input.data('product-id');

            // Use either max available or remaining needed, whichever is smaller
            const valueToUse = Math.min(max, remaining);
            input.val(valueToUse);

            // Update totals
            updateTotalAllocated(productId);
        });

        // Auto-allocate remaining functionality
        $('.auto-allocate-btn').on('click', function() {
            const productId = $(this).data('product-id');
            const orderedQuantity = parseInt($(this).data('ordered-quantity'));
            let { totalAllocated } = updateTotalAllocated(productId);

            // If already fully allocated, do nothing
            if (totalAllocated >= orderedQuantity) {
                return;
            }

            // Get remaining quantity needed
            let remaining = orderedQuantity - totalAllocated;

            // Find all batch inputs for this product
            const inputs = $(`input[name="batch_quantities[${productId}][]"]`);

            // Sort inputs by expiry date (FIFO approach)
            const rows = inputs.map(function() {
                return {
                    input: $(this),
                    available: parseInt($(this).data('available')) - parseInt($(this).val() || 0)
                };
            }).get().sort(function(a, b) {
                // Sort by available quantity (descending)
                return b.available - a.available;
            });

            // Allocate remaining quantity
            for (let i = 0; i < rows.length && remaining > 0; i++) {
                const row = rows[i];
                if (row.available <= 0) continue;

                const currentValue = parseInt(row.input.val() || 0);
                const toAllocate = Math.min(remaining, row.available);

                row.input.val(currentValue + toAllocate);
                remaining -= toAllocate;
            }

            // Update totals
            updateTotalAllocated(productId);
        });

        // Auto-submit form when selection method or warehouse changes
        $('#selection_method, #warehouse_id').on('change', function() {
            $('#batch-selection-method-form').submit();
        });
    });
</script>
{% endblock %}

{% endblock %}
