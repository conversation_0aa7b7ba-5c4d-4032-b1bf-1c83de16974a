{% extends 'base.html' %}

{% block title %}Orders - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Order Management</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('orders.index') }}" method="get" class="form-inline">
                                <div class="input-group w-100">
                                    <input type="text" name="q" class="form-control" placeholder="Search by Order ID, Customer, or Phone">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <!-- Export functionality temporarily disabled -->
                            <!-- <a href="#" class="btn btn-info mr-2" onclick="alert('Export functionality temporarily disabled')">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a> -->
                            <a href="{{ url_for('orders.new_order') }}" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> Place New Order
                            </a>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="ordersTable" class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID / Invoice #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_id }}</strong>
                                        {% if order.invoice_number %}
                                        <br><small class="text-muted">{{ order.invoice_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ order.customer_name }}</strong>
                                        {% if order.customer_phone %}
                                        <br><small class="text-muted">{{ order.customer_phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.order_date.strftime('%Y-%m-%d') if order.order_date else 'N/A' }}</td>
                                    <td>
                                        {% if order.status == 'Placed' %}
                                            <span class="badge badge-warning">{{ order.status }}</span>
                                        {% elif order.status == 'Approved' %}
                                            <span class="badge badge-success">{{ order.status }}</span>
                                        {% elif order.status == 'Processing' %}
                                            <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == 'Dispatched' %}
                                            <span class="badge badge-primary">{{ order.status }}</span>
                                        {% elif order.status == 'Delivered' %}
                                            <span class="badge badge-success">{{ order.status }}</span>
                                        {% elif order.status == 'Cancelled' %}
                                            <span class="badge badge-danger">{{ order.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>₹{{ "%.2f"|format(order.order_amount|float) if order.order_amount else '0.00' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('orders.view_order', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if order.status == 'Placed' %}
                                            <form action="{{ url_for('orders.approve_order', order_id=order.order_id) }}" method="post" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-outline-success" title="Approve Order"
                                                        onclick="return confirm('Are you sure you want to approve this order?')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                            {% if order.status == 'Approved' %}
                                            <a href="{{ url_for('generate_challan', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Generate DC">
                                                <i class="fas fa-truck"></i>
                                            </a>
                                            {% endif %}
                                            {% if order.status == 'Placed' %}
                                            <a href="{{ url_for('orders.update_order', order_id=order.order_id) }}" 
                                               class="btn btn-sm btn-outline-info" title="Edit Order">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>No orders found.
                                        <br><a href="{{ url_for('orders.new_order') }}" class="btn btn-primary mt-2">Place First Order</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include DataTables for enhanced table functionality -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    $('#ordersTable').DataTable({
        "pageLength": 25,
        "order": [[ 2, "desc" ]], // Sort by date column descending
        "columnDefs": [
            { "orderable": false, "targets": 5 } // Disable sorting on Actions column
        ]
    });
});
</script>
{% endblock %}
