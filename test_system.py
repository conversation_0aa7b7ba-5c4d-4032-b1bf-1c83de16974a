#!/usr/bin/env python3
"""
Medivent ERP System - Validation Test Script
This script validates that the system is properly configured and ready to run.
"""

import sys
import os
import subprocess
import importlib.util

def test_python_version():
    """Test if Python version is compatible"""
    print("🔍 Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is too old. Need Python 3.7+")
        return False

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("\n🔍 Testing dependencies...")
    
    required_packages = [
        ('flask', 'flask'), ('pandas', 'pandas'), ('matplotlib', 'matplotlib'),
        ('plotly', 'plotly'), ('reportlab', 'reportlab'), ('PIL', 'pillow'),
        ('xlsxwriter', 'xlsxwriter'), ('openpyxl', 'openpyxl'),
        ('seaborn', 'seaborn'), ('bleach', 'bleach')
    ]
    
    missing_packages = []

    for import_name, display_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name} - MISSING")
            missing_packages.append(display_name)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All dependencies are installed")
        return True

def test_application_import():
    """Test if the main application can be imported"""
    print("\n🔍 Testing application import...")
    try:
        import app
        print("✅ Main application imports successfully")
        return True
    except Exception as e:
        print(f"❌ Application import failed: {e}")
        return False

def test_file_structure():
    """Test if required files and directories exist"""
    print("\n🔍 Testing file structure...")
    
    required_files = [
        'app.py', 'run_app.py', 'requirements.txt', 
        'start_erp.bat', 'setup_erp.bat'
    ]
    
    required_dirs = [
        'static', 'templates', 'routes', 'utils'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    for dir in required_dirs:
        if os.path.exists(dir):
            print(f"✅ {dir}/")
        else:
            print(f"❌ {dir}/ - MISSING")
            missing_dirs.append(dir)
    
    # Check instance directory (will be created if missing)
    if not os.path.exists('instance'):
        print("⚠️  instance/ - Will be created on first run")
        os.makedirs('instance', exist_ok=True)
    else:
        print("✅ instance/")
    
    if missing_files or missing_dirs:
        print(f"\n❌ Missing files: {missing_files}")
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    else:
        print("✅ All required files and directories present")
        return True

def test_database():
    """Test database accessibility"""
    print("\n🔍 Testing database...")
    
    db_path = os.path.join('instance', 'medivent.db')
    if os.path.exists(db_path):
        print(f"✅ Database found at {db_path}")
        # Test database connection
        try:
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            print(f"✅ Database accessible with {len(tables)} tables")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    else:
        print("⚠️  Database not found - will be created on first run")
        return True

def test_port_availability():
    """Test if port 3000 is available"""
    print("\n🔍 Testing port availability...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', 3000))
    sock.close()
    
    if result == 0:
        print("⚠️  Port 3000 is already in use - application will use port 8080")
        return True
    else:
        print("✅ Port 3000 is available")
        return True

def main():
    """Run all validation tests"""
    print("=" * 60)
    print("    MEDIVENT ERP SYSTEM - VALIDATION TEST")
    print("=" * 60)
    
    tests = [
        test_python_version,
        test_dependencies,
        test_file_structure,
        test_application_import,
        test_database,
        test_port_availability
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"    VALIDATION RESULTS: {passed}/{total} TESTS PASSED")
    print("=" * 60)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready to run.")
        print("\nTo start the system:")
        print("  • Double-click 'start_erp.bat'")
        print("  • Or run: python run_app.py")
        print("\nSystem will be available at: http://localhost:3000")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        print("\nFor help:")
        print("  • Check SETUP_INSTRUCTIONS.md")
        print("  • Run setup_erp.bat for automated setup")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
