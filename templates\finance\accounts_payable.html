{% extends "base.html" %}

{% block title %}Accounts Payable - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .payables-dashboard {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .payables-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .payables-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .aging-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid;
    }

    .aging-current { border-left-color: #28a745; }
    .aging-30 { border-left-color: #ffc107; }
    .aging-60 { border-left-color: #fd7e14; }
    .aging-90 { border-left-color: #dc3545; }
    .aging-90plus { border-left-color: #6f42c1; }

    .payables-table {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .table-modern {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background: #f8f9fa;
        transform: scale(1.01);
    }

    .table-modern tbody td {
        padding: 15px;
        border: none;
        border-bottom: 1px solid #e9ecef;
    }

    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-pending { background: #fff3cd; color: #856404; }
    .status-approved { background: #d4edda; color: #155724; }
    .status-paid { background: #cce5ff; color: #004085; }
    .status-overdue { background: #f8d7da; color: #721c24; }

    .priority-badge {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .priority-low { background: #d4edda; color: #155724; }
    .priority-medium { background: #fff3cd; color: #856404; }
    .priority-high { background: #f8d7da; color: #721c24; }
    .priority-urgent { background: #fd79a8; color: #e84393; }

    .action-btn {
        padding: 5px 12px;
        border-radius: 8px;
        font-size: 0.8rem;
        margin: 2px;
        transition: all 0.3s ease;
    }

    .btn-pay {
        background: #28a745;
        color: white;
        border: none;
    }

    .btn-pay:hover {
        background: #218838;
        color: white;
    }

    .btn-approve {
        background: #007bff;
        color: white;
        border: none;
    }

    .btn-approve:hover {
        background: #0056b3;
        color: white;
    }

    .btn-view {
        background: #6c757d;
        color: white;
        border: none;
    }

    .btn-view:hover {
        background: #545b62;
        color: white;
    }

    .filters-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>

<div class="payables-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="payables-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="payables-title">
                        <i class="fas fa-file-invoice me-3"></i>Accounts Payable
                    </h1>
                    <p class="text-white-50 mb-0">Comprehensive payables management with aging analysis</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="exportPayables()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <a href="/finance" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left me-2"></i>Back to Finance
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-danger">₹{{ "{:,.0f}".format(total_outstanding) }}</div>
                    <div class="stat-label">Total Outstanding</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-info">{{ total_bills }}</div>
                    <div class="stat-label">Outstanding Bills</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-warning">₹{{ "{:,.0f}".format(aging_summary.get('90_plus', {}).get('amount', 0)) }}</div>
                    <div class="stat-label">90+ Days Overdue</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value text-success">{{ "{:.1f}".format((aging_summary.get('current', {}).get('amount', 0) / total_outstanding * 100) if total_outstanding > 0 else 0) }}%</div>
                    <div class="stat-label">Current Ratio</div>
                </div>
            </div>
        </div>

        <!-- Aging Analysis -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>Payables Aging Analysis
                    </h5>
                    <div style="position: relative; height: 300px;">
                        <canvas id="payablesAgingChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="aging-card aging-current">
                    <h6>Current</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary.current.count }} bills</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary.current.amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-30">
                    <h6>1-30 Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['1_30'].count }} bills</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['1_30'].amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-60">
                    <h6>31-60 Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['31_60'].count }} bills</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['31_60'].amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-90">
                    <h6>61-90 Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['61_90'].count }} bills</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['61_90'].amount) }}</strong>
                    </div>
                </div>
                <div class="aging-card aging-90plus">
                    <h6>90+ Days</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ aging_summary['90_plus'].count }} bills</span>
                        <strong>₹{{ "{:,.0f}".format(aging_summary['90_plus'].amount) }}</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Filters & Search
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label fw-bold">Vendor</label>
                    <input type="text" class="form-control" id="vendorFilter" placeholder="Search vendor...">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Status</label>
                    <select class="form-control" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Due Date</label>
                    <select class="form-control" id="dueDateFilter">
                        <option value="">All Dates</option>
                        <option value="overdue">Overdue</option>
                        <option value="due_today">Due Today</option>
                        <option value="due_week">Due This Week</option>
                        <option value="due_month">Due This Month</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Amount Range</label>
                    <select class="form-control" id="amountFilter">
                        <option value="">All Amounts</option>
                        <option value="0-10000">₹0 - ₹10,000</option>
                        <option value="10000-50000">₹10,000 - ₹50,000</option>
                        <option value="50000-100000">₹50,000 - ₹1,00,000</option>
                        <option value="100000+">₹1,00,000+</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Payables Table -->
        <div class="payables-table">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>Outstanding Payables
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-success btn-sm" onclick="bulkPayment()">
                        <i class="fas fa-credit-card me-1"></i>Bulk Payment
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="generatePayablesReport()">
                        <i class="fas fa-file-pdf me-1"></i>Payables Report
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-modern" id="payablesTable">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>Bill #</th>
                            <th>Vendor</th>
                            <th>Bill Date</th>
                            <th>Due Date</th>
                            <th>Original Amount</th>
                            <th>Paid Amount</th>
                            <th>Outstanding</th>
                            <th>Days Overdue</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payable in payables %}
                        <tr data-vendor="{{ payable.vendor_name }}" 
                            data-status="{{ payable.status }}"
                            data-amount="{{ payable.outstanding_amount }}"
                            data-due-date="{{ payable.due_date }}">
                            <td><input type="checkbox" class="payable-checkbox" value="{{ payable.bill_id }}"></td>
                            <td><strong>{{ payable.bill_id }}</strong></td>
                            <td>{{ payable.vendor_name }}</td>
                            <td>{{ payable.bill_date }}</td>
                            <td>{{ payable.due_date }}</td>
                            <td>₹{{ "{:,.2f}".format(payable.original_amount) }}</td>
                            <td>₹{{ "{:,.2f}".format(payable.paid_amount) }}</td>
                            <td><strong>₹{{ "{:,.2f}".format(payable.outstanding_amount) }}</strong></td>
                            <td>
                                {% if payable.days_overdue > 0 %}
                                    <span class="text-danger">{{ "{:.0f}".format(payable.days_overdue) }} days</span>
                                {% else %}
                                    <span class="text-success">Current</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="status-badge status-{{ payable.status }}">
                                    {{ payable.status.title() }}
                                </span>
                            </td>
                            <td>
                                <button class="btn action-btn btn-pay" onclick="payBill('{{ payable.bill_id }}')">
                                    <i class="fas fa-credit-card"></i> Pay
                                </button>
                                <button class="btn action-btn btn-view" onclick="viewBill('{{ payable.bill_id }}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize payables aging chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('payablesAgingChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Current', '1-30 Days', '31-60 Days', '61-90 Days', '90+ Days'],
            datasets: [{
                label: 'Outstanding Amount (₹)',
                data: [
                    {{ aging_summary.current.amount }},
                    {{ aging_summary['1_30'].amount }},
                    {{ aging_summary['31_60'].amount }},
                    {{ aging_summary['61_90'].amount }},
                    {{ aging_summary['90_plus'].amount }}
                ],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(255, 126, 20, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(111, 66, 193, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(255, 126, 20, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(111, 66, 193, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Initialize filters
    initializeFilters();
});

function initializeFilters() {
    const vendorFilter = document.getElementById('vendorFilter');
    const statusFilter = document.getElementById('statusFilter');
    const dueDateFilter = document.getElementById('dueDateFilter');
    const amountFilter = document.getElementById('amountFilter');

    [vendorFilter, statusFilter, dueDateFilter, amountFilter].forEach(filter => {
        filter.addEventListener('change', applyFilters);
        filter.addEventListener('keyup', applyFilters);
    });
}

function applyFilters() {
    const vendorFilter = document.getElementById('vendorFilter').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const dueDateFilter = document.getElementById('dueDateFilter').value;
    const amountFilter = document.getElementById('amountFilter').value;

    const rows = document.querySelectorAll('#payablesTable tbody tr');

    rows.forEach(row => {
        const vendor = row.dataset.vendor.toLowerCase();
        const status = row.dataset.status;
        const amount = parseFloat(row.dataset.amount);
        const dueDate = new Date(row.dataset.dueDate);
        const today = new Date();

        let show = true;

        // Vendor filter
        if (vendorFilter && !vendor.includes(vendorFilter)) {
            show = false;
        }

        // Status filter
        if (statusFilter && status !== statusFilter) {
            show = false;
        }

        // Due date filter
        if (dueDateFilter) {
            const daysDiff = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

            switch(dueDateFilter) {
                case 'overdue':
                    if (daysDiff >= 0) show = false;
                    break;
                case 'due_today':
                    if (daysDiff !== 0) show = false;
                    break;
                case 'due_week':
                    if (daysDiff < 0 || daysDiff > 7) show = false;
                    break;
                case 'due_month':
                    if (daysDiff < 0 || daysDiff > 30) show = false;
                    break;
            }
        }

        // Amount filter
        if (amountFilter) {
            const [min, max] = amountFilter.split('-').map(v => v.replace('+', ''));
            const minAmount = parseFloat(min) || 0;
            const maxAmount = max ? parseFloat(max) : Infinity;

            if (amount < minAmount || amount > maxAmount) {
                show = false;
            }
        }

        row.style.display = show ? '' : 'none';
    });
}

function payBill(billId) {
    // Open payment processing modal/page
    window.open(`/finance/pay-bill?bill_id=${billId}`, '_blank');
}

function viewBill(billId) {
    // Open bill details
    window.open(`/finance/bill/${billId}`, '_blank');
}

function bulkPayment() {
    const selectedBills = Array.from(document.querySelectorAll('.payable-checkbox:checked'))
        .map(cb => cb.value);

    if (selectedBills.length === 0) {
        alert('Please select bills to pay.');
        return;
    }

    if (confirm(`Process payment for ${selectedBills.length} selected bills?`)) {
        // Implement bulk payment functionality
        fetch('/finance/api/bulk-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                bill_ids: selectedBills
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Payment processed for ${data.processed_count} bills.`);
                location.reload();
            } else {
                alert('Error processing payments: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error processing payments.');
        });
    }
}

function generatePayablesReport() {
    // Generate and download payables report
    window.open('/finance/reports/payables-analysis?format=pdf', '_blank');
}

function exportPayables() {
    // Export payables data
    window.open('/finance/export/payables?format=excel', '_blank');
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.payable-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});
</script>

{% endblock %}
