# tcs_tracker.py
from flask import Flask, request, jsonify, render_template_string
import requests, re
from bs4 import BeautifulSoup

app = Flask(__name__)

PROXY_URL = "https://api.allorigins.win/raw?url={}"

def fetch_tcs_card(number: str):
    url = f"https://www.tcsexpress.com/track/{number}"
    html = requests.get(PROXY_URL.format(url), timeout=15).text
    soup = BeautifulSoup(html, "lxml")
    card = soup.select_one("#main-content-app > div > div:nth-child(3) > div")
    return str(card) if card else None

@app.route("/")
def index():
    return render_template_string("""
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <title>TCS Result Only</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-4">
  <h1 class="text-2xl font-bold text-center mb-4 text-[#f0575d]">TCS Express – Result Only</h1>

  <!-- search bar -->
  <div class="flex justify-center mb-6">
    <input id="tn" type="text" placeholder="TCS tracking #" class="p-2 border rounded-l w-64">
    <button onclick="load()" class="bg-[#f0575d] text-white px-4 rounded-r">Track</button>
  </div>

  <!-- ONLY the result card -->
  <div id="card" class="max-w-3xl mx-auto"></div>

  <script>
    async function load() {
      const n = tn.value.trim();
      if (!n) return;
      card.innerHTML = '<div class="text-center">Loading…</div>';
      try {
        const res = await fetch(`/api/track/${n}`);
        const {html, error} = await res.json();
        if (error) throw new Error(error);
        card.innerHTML = html;
      } catch (e) {
        card.innerHTML = `<div class="bg-white p-4 shadow text-red-600">⚠️ ${e.message}</div>`;
      }
    }
  </script>
</body>
</html>
""")

@app.route("/api/track/<number>")
def api_track(number):
    if not re.fullmatch(r"\d{11,}", number):
        return jsonify({"error": "Invalid tracking number"}), 400
    try:
        html = fetch_tcs_card(number)
        if not html:
            return jsonify({"error": "No data found"}), 404
        return jsonify({"html": html})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True)