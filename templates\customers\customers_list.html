{% extends "base.html" %}

{% block title %}Customer Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-users"></i> Customer Management
                            </h4>
                            <small>Manage customer information, pricing, and order history</small>
                        </div>
                        <div class="col-md-4 text-right">
                            <a href="{{ url_for('customers', action='add') }}" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Add New Customer
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Customers</h6>
                                    <h3 class="mb-0">{{ customer_stats.total_customers }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Customers</h6>
                                    <h3 class="mb-0">{{ customer_stats.active_customers }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">High Value Customers</h6>
                                    <h3 class="mb-0">{{ customer_stats.high_value_customers }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Outstanding Amount</h6>
                                    <h3 class="mb-0">₹{{ customer_stats.total_outstanding | format_currency }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-search"></i> Search & Filter</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('customers') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" name="search" id="search" class="form-control" 
                                           placeholder="Customer name, ID, phone..." 
                                           value="{{ request.args.get('search', '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <select name="city" id="city" class="form-control">
                                        <option value="">All Cities</option>
                                        {% for city in cities %}
                                        <option value="{{ city }}" {% if request.args.get('city') == city %}selected{% endif %}>
                                            {{ city }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="customer_type">Type</label>
                                    <select name="customer_type" id="customer_type" class="form-control">
                                        <option value="">All Types</option>
                                        <option value="Pharmacy" {% if request.args.get('customer_type') == 'Pharmacy' %}selected{% endif %}>Pharmacy</option>
                                        <option value="Hospital" {% if request.args.get('customer_type') == 'Hospital' %}selected{% endif %}>Hospital</option>
                                        <option value="Clinic" {% if request.args.get('customer_type') == 'Clinic' %}selected{% endif %}>Clinic</option>
                                        <option value="Distributor" {% if request.args.get('customer_type') == 'Distributor' %}selected{% endif %}>Distributor</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="risk_category">Risk Category</label>
                                    <select name="risk_category" id="risk_category" class="form-control">
                                        <option value="">All Risk Levels</option>
                                        <option value="LOW" {% if request.args.get('risk_category') == 'LOW' %}selected{% endif %}>Low</option>
                                        <option value="MEDIUM" {% if request.args.get('risk_category') == 'MEDIUM' %}selected{% endif %}>Medium</option>
                                        <option value="HIGH" {% if request.args.get('risk_category') == 'HIGH' %}selected{% endif %}>High</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="sort_by">Sort By</label>
                                    <select name="sort_by" id="sort_by" class="form-control">
                                        <option value="name" {% if request.args.get('sort_by') == 'name' %}selected{% endif %}>Name</option>
                                        <option value="customer_id" {% if request.args.get('sort_by') == 'customer_id' %}selected{% endif %}>Customer ID</option>
                                        <option value="city" {% if request.args.get('sort_by') == 'city' %}selected{% endif %}>City</option>
                                        <option value="created_date" {% if request.args.get('sort_by') == 'created_date' %}selected{% endif %}>Date Added</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{{ url_for('customers', view='by_type') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-user-tag"></i><br>
                                View by Type
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('customers', view='pricing') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-tags"></i><br>
                                Customer Pricing
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('customers', action='set_pricing') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-dollar-sign"></i><br>
                                Set Pricing
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('customers', view='orders') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-history"></i><br>
                                Order History
                            </a>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary btn-block" onclick="exportCustomers()">
                                <i class="fas fa-download"></i><br>
                                Export Data
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-dark btn-block" onclick="bulkActions()">
                                <i class="fas fa-tasks"></i><br>
                                Bulk Actions
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0"><i class="fas fa-list"></i> Customers List</h5>
                        </div>
                        <div class="col-auto">
                            <span class="badge badge-light">{{ customers|length }} customers</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="customersTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Customer ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>City</th>
                                    <th>Phone</th>
                                    <th>Credit Limit</th>
                                    <th>Outstanding</th>
                                    <th>Risk</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="customer-checkbox" value="{{ customer.customer_id }}">
                                    </td>
                                    <td><strong>{{ customer.customer_id }}</strong></td>
                                    <td>
                                        <div>
                                            <strong>{{ customer.name }}</strong>
                                            {% if customer.email %}
                                            <br><small class="text-muted">{{ customer.email }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if customer.customer_type == 'Hospital' %}primary{% elif customer.customer_type == 'Pharmacy' %}success{% elif customer.customer_type == 'Clinic' %}info{% else %}secondary{% endif %}">
                                            {{ customer.customer_type or 'N/A' }}
                                        </span>
                                    </td>
                                    <td>{{ customer.city }}</td>
                                    <td>
                                        {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}">{{ customer.phone }}</a>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.credit_limit %}
                                        ₹{{ customer.credit_limit | format_currency }}
                                        {% else %}
                                        <span class="text-muted">No Limit</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.outstanding_amount %}
                                        <span class="badge badge-{% if customer.outstanding_amount > 100000 %}danger{% elif customer.outstanding_amount > 50000 %}warning{% else %}info{% endif %}">
                                            ₹{{ customer.outstanding_amount | format_currency }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-success">₹0.00</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if customer.risk_category == 'HIGH' %}danger{% elif customer.risk_category == 'MEDIUM' %}warning{% else %}success{% endif %}">
                                            {{ customer.risk_category or 'LOW' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if customer.status == 'active' %}success{% else %}danger{% endif %}">
                                            {{ customer.status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-primary" onclick="viewCustomer('{{ customer.customer_id }}')" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="editCustomer('{{ customer.customer_id }}')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="viewOrders('{{ customer.customer_id }}')" title="Order History">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="viewLedger('{{ customer.customer_id }}')" title="Financial Ledger">
                                                <i class="fas fa-book"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if pagination %}
                    <nav aria-label="Customer pagination">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=pagination.prev_num, **request.args) }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                            {% if page_num != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=pagination.next_num, **request.args) }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Customer actions
function viewCustomer(customerId) {
    window.location.href = `/customers/${customerId}/view`;
}

function editCustomer(customerId) {
    window.location.href = `/customers/${customerId}/edit`;
}

function viewOrders(customerId) {
    window.location.href = `/customers/${customerId}/orders`;
}

function viewLedger(customerId) {
    window.location.href = `/finance/customer-statement/${customerId}`;
}

// Bulk actions
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.customer-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function getSelectedCustomers() {
    const checkboxes = document.querySelectorAll('.customer-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function bulkActions() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        alert('Please select customers first');
        return;
    }

    const action = prompt('Enter action (activate/deactivate/delete):');
    if (action && ['activate', 'deactivate', 'delete'].includes(action)) {
        if (action === 'delete' && !confirm('Are you sure you want to delete selected customers?')) {
            return;
        }

        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/customers/bulk-action';

        // Add action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;
        form.appendChild(actionInput);

        // Add customer IDs
        selected.forEach(id => {
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'customer_ids';
            idInput.value = id;
            form.appendChild(idInput);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

function exportCustomers() {
    const selected = getSelectedCustomers();
    let url = '/customers/export';

    if (selected.length > 0) {
        url += '?customers=' + selected.join(',');
    }

    window.location.href = url;
}

// Initialize DataTable
$(document).ready(function() {
    $('#customersTable').DataTable({
        "pageLength": 25,
        "order": [[ 1, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 10] }
        ]
    });
});
</script>
{% endblock %}
