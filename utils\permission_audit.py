"""
Permission Audit Logging for Medivent Pharmaceuticals Web Portal
This module provides functions for tracking permission changes
"""

import sqlite3
from flask import g, request, current_app
from flask_login import current_user

def get_db():
    """Get database connection"""
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

def log_permission_change(role, permission_code, permission_name, action, previous_state=None, new_state=None):
    """
    Log a permission change to the audit log
    
    Args:
        role (str): The role being modified
        permission_code (str): The permission code being changed
        permission_name (str): The human-readable name of the permission
        action (str): The action being performed (add, remove, update)
        previous_state (str, optional): The previous state of the permission
        new_state (str, optional): The new state of the permission
    
    Returns:
        bool: True if logging was successful, False otherwise
    """
    db = get_db()
    
    try:
        # Get the current user's username
        username = current_user.username if current_user.is_authenticated else 'system'
        
        # Get the client's IP address
        ip_address = request.remote_addr if request else None
        
        # Insert the audit log entry
        db.execute('''
            INSERT INTO permission_audit_logs (
                username, action, role, permission_code, permission_name,
                previous_state, new_state, ip_address
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            username, action, role, permission_code, permission_name,
            previous_state, new_state, ip_address
        ))
        
        db.commit()
        return True
    except Exception as e:
        current_app.logger.error(f"Error logging permission change: {str(e)}")
        return False

def get_permission_audit_logs(role=None, permission_code=None, username=None, limit=100):
    """
    Get permission audit logs with optional filtering
    
    Args:
        role (str, optional): Filter by role
        permission_code (str, optional): Filter by permission code
        username (str, optional): Filter by username
        limit (int, optional): Maximum number of logs to return
    
    Returns:
        list: List of audit log entries
    """
    db = get_db()
    
    query = '''
        SELECT * FROM permission_audit_logs
        WHERE 1=1
    '''
    params = []
    
    if role:
        query += ' AND role = ?'
        params.append(role)
    
    if permission_code:
        query += ' AND permission_code = ?'
        params.append(permission_code)
    
    if username:
        query += ' AND username = ?'
        params.append(username)
    
    query += ' ORDER BY timestamp DESC LIMIT ?'
    params.append(limit)
    
    logs = db.execute(query, params).fetchall()
    return [dict(log) for log in logs]

def get_role_permission_history(role):
    """
    Get the complete history of permission changes for a role
    
    Args:
        role (str): The role to get history for
    
    Returns:
        list: List of audit log entries for the role
    """
    db = get_db()
    
    logs = db.execute('''
        SELECT * FROM permission_audit_logs
        WHERE role = ?
        ORDER BY timestamp DESC
    ''', (role,)).fetchall()
    
    return [dict(log) for log in logs]

def get_user_permission_activity(username):
    """
    Get all permission changes made by a specific user
    
    Args:
        username (str): The username to get activity for
    
    Returns:
        list: List of audit log entries by the user
    """
    db = get_db()
    
    logs = db.execute('''
        SELECT * FROM permission_audit_logs
        WHERE username = ?
        ORDER BY timestamp DESC
    ''', (username,)).fetchall()
    
    return [dict(log) for log in logs]

def get_recent_permission_changes(limit=20):
    """
    Get the most recent permission changes
    
    Args:
        limit (int, optional): Maximum number of changes to return
    
    Returns:
        list: List of recent audit log entries
    """
    db = get_db()
    
    logs = db.execute('''
        SELECT * FROM permission_audit_logs
        ORDER BY timestamp DESC
        LIMIT ?
    ''', (limit,)).fetchall()
    
    return [dict(log) for log in logs]
