/**
 * python_charts.js
 * This file handles rendering charts with data processed by Python backend
 * It replaces the client-side data processing in product_performance_charts.js,
 * sales_team_charts.js, and other chart files
 */

// Function to fetch data from Python API and render charts
function fetchAndRenderProductCharts(startDate = null, endDate = null) {
    // Build query parameters
    let params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    // Fetch data from Python API
    fetch(`/api/data/products?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            // Render charts with the Python-processed data
            renderProductSalesChart(data.top_products_by_sales);
            renderProductQuantityChart(data.top_products_by_quantity);
            renderDivisionSalesChart(data.division_sales);
            renderDivisionQuantityChart(data.division_quantity);
            renderMonthlyTrendsChart(data.monthly_trends);
        })
        .catch(error => {
            console.error('Error fetching product data:', error);
        });
}

/**
 * python_charts.js
 * This file contains functions for rendering Python-processed data into charts
 * using Chart.js and Plotly.js
 */

function fetchAndRenderSalesTeamCharts(startDate = null, endDate = null) {
    console.log('Fetching and rendering sales team charts...');

    // Build query parameters
    let params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    // Fetch data from Python API
    fetch(`/api/data/sales-team?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            console.log('Sales team data received:', data);

            // Render charts with the Python-processed data
            if (data.team_performance) {
                renderTeamPerformanceChart(data.team_performance);
                renderAgentSalesChart(data.team_performance);
            }

            if (data.division_by_agent) {
                renderDivisionByAgentChart(data.division_by_agent);
            }

            // Resize charts after rendering
            setTimeout(function() {
                console.log('Resizing charts after rendering');
                window.dispatchEvent(new Event('resize'));
            }, 500);
        })
        .catch(error => {
            console.error('Error fetching sales team data:', error);

            // Show empty state messages instead of fallback data
            showEmptyStateMessage('teamPerformanceChart', 'No team performance data available');
            showEmptyStateMessage('agentSalesChart', 'No agent sales data available');
            showEmptyStateMessage('divisionByAgentChart', 'No division data available');
        });
}

function fetchAndRenderFinancialCharts(startDate = null, endDate = null) {
    // Build query parameters
    let params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    // Fetch data from Python API
    fetch(`/api/data/financial?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            // Render charts with the Python-processed data
            renderDailyRevenueChart(data.daily_revenue);
            renderPaymentMethodsChart(data.payment_methods);
            renderDivisionRevenueChart(data.division_revenue);
        })
        .catch(error => {
            console.error('Error fetching financial data:', error);
        });
}

// Chart rendering functions
function renderProductSalesChart(products) {
    if (!products || products.length === 0 || !document.getElementById('productSalesChart')) return;

    const ctx = document.getElementById('productSalesChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: products.map(p => p.product_name + (p.strength ? ` (${p.strength})` : '')),
            datasets: [{
                label: 'Sales (PKR)',
                data: products.map(p => p.total_sales),
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function renderProductQuantityChart(products) {
    if (!products || products.length === 0 || !document.getElementById('productQuantityChart')) return;

    const ctx = document.getElementById('productQuantityChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: products.map(p => p.product_name + (p.strength ? ` (${p.strength})` : '')),
            datasets: [{
                label: 'Quantity Sold',
                data: products.map(p => p.total_quantity),
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function renderDivisionSalesChart(divisions) {
    if (!divisions || divisions.length === 0 || !document.getElementById('divisionSalesChart')) return;

    const ctx = document.getElementById('divisionSalesChart').getContext('2d');
    const colors = generateColors(divisions.length);

    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: divisions.map(d => d.division_name),
            datasets: [{
                data: divisions.map(d => d.total_sales),
                backgroundColor: colors,
                borderColor: 'white',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Helper function to generate random colors
function generateColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
        const r = Math.floor(Math.random() * 255);
        const g = Math.floor(Math.random() * 255);
        const b = Math.floor(Math.random() * 255);
        colors.push(`rgba(${r}, ${g}, ${b}, 0.7)`);
    }
    return colors;
}

// Function to show empty state message
function showEmptyStateMessage(chartId, message) {
    const chartElement = document.getElementById(chartId);
    if (chartElement) {
        chartElement.parentElement.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="fas fa-chart-line fa-3x mb-3"></i><br>
                ${message}
            </div>
        `;
    }
}

// Initialize charts when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check which page we're on and initialize appropriate charts
    if (document.getElementById('productSalesChart')) {
        fetchAndRenderProductCharts();
    }

    if (document.getElementById('teamPerformanceChart') || document.getElementById('agentSalesChart')) {
        fetchAndRenderSalesTeamCharts();
    }

    if (document.getElementById('revenueChart')) {
        fetchAndRenderFinancialCharts();
    }

    // Set up date range filters if they exist
    const dateRangeForm = document.getElementById('dateRangeForm');
    if (dateRangeForm) {
        dateRangeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;

            // Determine which charts to refresh based on the page
            if (document.getElementById('productSalesChart')) {
                fetchAndRenderProductCharts(startDate, endDate);
            }

            if (document.getElementById('teamPerformanceChart') || document.getElementById('agentSalesChart')) {
                fetchAndRenderSalesTeamCharts(startDate, endDate);
            }

            if (document.getElementById('revenueChart')) {
                fetchAndRenderFinancialCharts(startDate, endDate);
            }
        });
    }
});
