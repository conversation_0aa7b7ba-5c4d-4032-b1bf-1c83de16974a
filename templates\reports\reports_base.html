{% extends "base.html" %}

{% block scripts %}
<!-- Debug script to help diagnose chart issues -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Reports base template loaded');

        // Check if Chart.js is loaded
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js is loaded successfully');
        } else {
            console.error('Chart.js is not loaded!');
        }

        // Check if Plotly is loaded
        if (typeof Plotly !== 'undefined') {
            console.log('Plotly.js is loaded successfully');
        } else {
            console.error('Plotly.js is not loaded!');
        }

        // Check for canvas elements
        const canvasElements = document.querySelectorAll('canvas');
        console.log(`Found ${canvasElements.length} canvas elements:`, canvasElements);

        canvasElements.forEach(canvas => {
            console.log(`Canvas #${canvas.id}: width=${canvas.width}, height=${canvas.height}`);
        });
    });
</script>

<!-- Custom chart initialization script -->
<script src="{{ url_for('static', filename='js/python_charts_init.js') }}"></script>

<!-- Additional report-specific scripts -->
{% block report_scripts %}{% endblock %}
{% endblock %}

{% block styles %}
<!-- Report-specific styles -->
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
        text-align: center;
    }

    .chart-subtitle {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 15px;
        text-align: center;
    }

    @media print {
        .chart-container {
            page-break-inside: avoid;
        }
    }
</style>
{% block report_styles %}{% endblock %}
{% endblock %}
