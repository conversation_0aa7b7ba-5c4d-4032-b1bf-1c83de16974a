<!-- Intelligent Search Component -->
<div class="intelligent-search-container">
    <div class="search-input-wrapper">
        <div class="input-group">
            <span class="input-group-text search-icon">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" 
                   class="form-control intelligent-search-input" 
                   id="intelligentSearchInput"
                   placeholder="Search orders, customers, products, finance..." 
                   autocomplete="off">
            <div class="input-group-append">
                <button class="btn btn-outline-secondary dropdown-toggle" 
                        type="button" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <span id="searchTypeLabel">All</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-type="all">All Results</a></li>
                    <li><a class="dropdown-item" href="#" data-type="customers">Customers</a></li>
                    <li><a class="dropdown-item" href="#" data-type="orders">Orders</a></li>
                    <li><a class="dropdown-item" href="#" data-type="products">Products</a></li>
                    <li><a class="dropdown-item" href="#" data-type="finance">Finance</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Search Results Dropdown -->
    <div class="search-results-dropdown" id="searchResultsDropdown" style="display: none;">
        <div class="search-results-header">
            <div class="d-flex justify-content-between align-items-center">
                <span class="search-results-title">Search Results</span>
                <span class="search-results-count" id="searchResultsCount">0 results</span>
            </div>
        </div>
        
        <!-- Suggestions Section -->
        <div class="search-suggestions-section" id="searchSuggestionsSection" style="display: none;">
            <div class="search-section-title">
                <i class="fas fa-lightbulb me-2"></i>Suggestions
            </div>
            <div class="search-suggestions" id="searchSuggestions"></div>
        </div>
        
        <!-- Results Section -->
        <div class="search-results-section" id="searchResultsSection">
            <div class="search-results" id="searchResults"></div>
        </div>
        
        <!-- No Results -->
        <div class="no-search-results" id="noSearchResults" style="display: none;">
            <div class="text-center py-4">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No results found</h6>
                <p class="text-muted small">Try different keywords or check spelling</p>
            </div>
        </div>
        
        <!-- Loading -->
        <div class="search-loading" id="searchLoading" style="display: none;">
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                <span>Searching...</span>
            </div>
        </div>
    </div>
</div>

<style>
.intelligent-search-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.search-input-wrapper {
    position: relative;
}

.intelligent-search-input {
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 14px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.intelligent-search-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-icon {
    background: transparent;
    border: none;
    color: #6c757d;
}

.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    z-index: 1050;
    max-height: 500px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-results-header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f8f9fa;
    background: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

.search-results-title {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.search-results-count {
    font-size: 12px;
    color: #6c757d;
}

.search-section-title {
    padding: 10px 20px 5px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-suggestions {
    padding: 0 10px 10px;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    margin: 2px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.search-suggestion-item:hover {
    background: #f8f9fa;
}

.search-suggestion-icon {
    width: 20px;
    margin-right: 10px;
    color: #6c757d;
    font-size: 12px;
}

.search-results {
    padding: 0 10px 10px;
}

.search-result-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 15px;
    margin: 2px 0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.search-result-item:hover {
    background: #f8f9fa;
    text-decoration: none;
    color: inherit;
    transform: translateY(-1px);
}

.search-result-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.search-result-icon.customer { background: linear-gradient(135deg, #007bff, #0056b3); }
.search-result-icon.order { background: linear-gradient(135deg, #28a745, #1e7e34); }
.search-result-icon.product { background: linear-gradient(135deg, #ffc107, #e0a800); }
.search-result-icon.invoice { background: linear-gradient(135deg, #dc3545, #c82333); }
.search-result-icon.payment { background: linear-gradient(135deg, #6f42c1, #5a32a3); }

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-title {
    font-weight: 600;
    font-size: 14px;
    color: #212529;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-subtitle {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-description {
    font-size: 11px;
    color: #868e96;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-type {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--primary);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 500;
}

.no-search-results {
    padding: 20px;
}

.search-loading {
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .intelligent-search-container {
        max-width: 100%;
    }
    
    .search-results-dropdown {
        max-height: 400px;
    }
}

/* Highlight matching text */
.search-highlight {
    background: rgba(255, 193, 7, 0.3);
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Animation for dropdown */
.search-results-dropdown {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
class IntelligentSearch {
    constructor() {
        this.searchInput = document.getElementById('intelligentSearchInput');
        this.searchResults = document.getElementById('searchResults');
        this.searchSuggestions = document.getElementById('searchSuggestions');
        this.searchResultsDropdown = document.getElementById('searchResultsDropdown');
        this.searchResultsCount = document.getElementById('searchResultsCount');
        this.searchSuggestionsSection = document.getElementById('searchSuggestionsSection');
        this.searchResultsSection = document.getElementById('searchResultsSection');
        this.noSearchResults = document.getElementById('noSearchResults');
        this.searchLoading = document.getElementById('searchLoading');
        this.searchTypeLabel = document.getElementById('searchTypeLabel');
        
        this.currentSearchType = 'all';
        this.searchTimeout = null;
        this.currentQuery = '';
        
        this.init();
    }
    
    init() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });
        
        this.searchInput.addEventListener('focus', () => {
            if (this.currentQuery.length >= 2) {
                this.showDropdown();
            }
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // Search type dropdown
        document.querySelectorAll('.dropdown-item[data-type]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.setSearchType(e.target.dataset.type, e.target.textContent);
            });
        });
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.searchInput.contains(e.target) && !this.searchResultsDropdown.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }
    
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        if (this.currentQuery.length < 2) {
            this.hideDropdown();
            return;
        }
        
        // Debounce search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, 300);
    }
    
    async performSearch(query) {
        this.showLoading();
        
        try {
            const response = await fetch(`/api/intelligent-search?q=${encodeURIComponent(query)}&type=${this.currentSearchType}&limit=10`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.displayResults(data);
            
        } catch (error) {
            console.error('Search error:', error);
            this.showNoResults();
        }
    }
    
    displayResults(data) {
        this.hideLoading();
        
        // Update results count
        this.searchResultsCount.textContent = `${data.total_found} result${data.total_found !== 1 ? 's' : ''}`;
        
        // Display suggestions
        if (data.suggestions && data.suggestions.length > 0) {
            this.displaySuggestions(data.suggestions);
            this.searchSuggestionsSection.style.display = 'block';
        } else {
            this.searchSuggestionsSection.style.display = 'none';
        }
        
        // Display results
        if (data.results && data.results.length > 0) {
            this.displaySearchResults(data.results);
            this.searchResultsSection.style.display = 'block';
            this.noSearchResults.style.display = 'none';
        } else {
            this.searchResultsSection.style.display = 'none';
            this.noSearchResults.style.display = 'block';
        }
        
        this.showDropdown();
    }
    
    displaySuggestions(suggestions) {
        this.searchSuggestions.innerHTML = suggestions.map(suggestion => `
            <div class="search-suggestion-item" onclick="intelligentSearch.selectSuggestion('${suggestion.text}')">
                <div class="search-suggestion-icon">
                    <i class="${suggestion.icon}"></i>
                </div>
                <span>${this.highlightMatch(suggestion.text, this.currentQuery)}</span>
            </div>
        `).join('');
    }
    
    displaySearchResults(results) {
        this.searchResults.innerHTML = results.map(result => `
            <a href="${result.url}" class="search-result-item" onclick="intelligentSearch.hideDropdown()">
                <div class="search-result-icon ${result.type}">
                    <i class="${result.icon}"></i>
                </div>
                <div class="search-result-content">
                    <div class="search-result-title">${this.highlightMatch(result.title, this.currentQuery)}</div>
                    <div class="search-result-subtitle">${this.highlightMatch(result.subtitle, this.currentQuery)}</div>
                    <div class="search-result-description">${result.description}</div>
                </div>
            </a>
        `).join('');
    }
    
    highlightMatch(text, query) {
        if (!query || !text) return text;
        
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }
    
    selectSuggestion(suggestion) {
        this.searchInput.value = suggestion;
        this.currentQuery = suggestion;
        this.performSearch(suggestion);
    }
    
    setSearchType(type, label) {
        this.currentSearchType = type;
        this.searchTypeLabel.textContent = label;
        
        if (this.currentQuery.length >= 2) {
            this.performSearch(this.currentQuery);
        }
    }
    
    showDropdown() {
        this.searchResultsDropdown.style.display = 'block';
    }
    
    hideDropdown() {
        this.searchResultsDropdown.style.display = 'none';
    }
    
    showLoading() {
        this.searchLoading.style.display = 'block';
        this.searchResultsSection.style.display = 'none';
        this.searchSuggestionsSection.style.display = 'none';
        this.noSearchResults.style.display = 'none';
        this.showDropdown();
    }
    
    hideLoading() {
        this.searchLoading.style.display = 'none';
    }
    
    showNoResults() {
        this.hideLoading();
        this.searchResultsSection.style.display = 'none';
        this.searchSuggestionsSection.style.display = 'none';
        this.noSearchResults.style.display = 'block';
        this.searchResultsCount.textContent = '0 results';
        this.showDropdown();
    }
    
    handleKeydown(e) {
        if (e.key === 'Escape') {
            this.hideDropdown();
        }
        // Add arrow key navigation here if needed
    }
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.intelligentSearch = new IntelligentSearch();
});
</script>
