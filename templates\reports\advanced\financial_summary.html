{% extends 'base.html' %}

{% block title %}Financial Summary Report - Medivent Pharmaceuticals ERP{% endblock %}

{% block head %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Financial Summary Report</h4>
                    <div>
                        <button class="btn btn-light" id="printFinancialSummaryBtn">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <a href="{{ url_for('reports') }}" class="btn btn-light ml-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('financial_summary_report') }}" method="get" class="form-inline">
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">From</span>
                                    </div>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                                <div class="input-group mr-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">To</span>
                                    </div>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt"></i> Showing data from {{ start_date }} to {{ end_date }}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ summary.total_orders or 0 }}</h1>
                                    <p class="mb-0">Total Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ "%.2f"|format(summary.total_revenue or 0) }}</h1>
                                    <p class="mb-0">Total Revenue (PKR)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ "%.2f"|format(summary.average_order or 0) }}</h1>
                                    <p class="mb-0">Average Order (PKR)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4">{{ "%.2f"|format(summary.largest_order or 0) }}</h1>
                                    <p class="mb-0">Largest Order (PKR)</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if charts %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Daily Revenue Trend</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.daily_revenue %}
                                    <div id="daily-revenue-chart"></div>
                                    <script>
                                        var dailyRevenueData = {{ charts.daily_revenue|safe }};
                                        Plotly.newPlot('daily-revenue-chart', dailyRevenueData.data, dailyRevenueData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">Revenue Distribution Analysis</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_pie %}
                                    <div id="nested-pie-chart"></div>
                                    <script>
                                        var nestedPieData = {{ charts.nested_pie|safe }};
                                        Plotly.newPlot('nested-pie-chart', nestedPieData.data, nestedPieData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("financial_summary_report") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_pie) {
                                                        var chartData = JSON.parse(data.charts.nested_pie);
                                                        Plotly.react('nested-pie-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Revenue by Payment Method (Nested Donut)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_donut_outer %}
                                    <div id="nested-donut-outer-chart"></div>
                                    <script>
                                        var nestedDonutOuterData = {{ charts.nested_donut_outer|safe }};
                                        Plotly.newPlot('nested-donut-outer-chart', nestedDonutOuterData.data, nestedDonutOuterData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("financial_summary_report") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_donut_outer) {
                                                        var chartData = JSON.parse(data.charts.nested_donut_outer);
                                                        Plotly.react('nested-donut-outer-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Revenue by Division (Nested Donut)</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.nested_donut_inner %}
                                    <div id="nested-donut-inner-chart"></div>
                                    <script>
                                        var nestedDonutInnerData = {{ charts.nested_donut_inner|safe }};
                                        Plotly.newPlot('nested-donut-inner-chart', nestedDonutInnerData.data, nestedDonutInnerData.layout);

                                        // Add auto-refresh for real-time updates
                                        setInterval(function() {
                                            fetch('{{ url_for("financial_summary_report") }}?start_date={{ start_date }}&end_date={{ end_date }}&refresh=true')
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.charts && data.charts.nested_donut_inner) {
                                                        var chartData = JSON.parse(data.charts.nested_donut_inner);
                                                        Plotly.react('nested-donut-inner-chart', chartData.data, chartData.layout);
                                                    }
                                                });
                                        }, 30000); // Refresh every 30 seconds
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Payment Method Distribution</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.payment_pie %}
                                    <div id="payment-pie-chart"></div>
                                    <script>
                                        var paymentPieData = {{ charts.payment_pie|safe }};
                                        Plotly.newPlot('payment-pie-chart', paymentPieData.data, paymentPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Division Revenue Distribution</h5>
                                </div>
                                <div class="card-body">
                                    {% if charts.division_pie %}
                                    <div id="division-pie-chart"></div>
                                    <script>
                                        var divisionPieData = {{ charts.division_pie|safe }};
                                        Plotly.newPlot('division-pie-chart', divisionPieData.data, divisionPieData.layout);
                                    </script>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <h5>Daily Revenue</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Date</th>
                                            <th>Orders</th>
                                            <th>Revenue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if financial_data %}
                                            {% for day in financial_data %}
                                            <tr>
                                                <td>{{ day.sale_date }}</td>
                                                <td>{{ day.order_count }}</td>
                                                <td>{{ "%.2f"|format(day.daily_revenue) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center">No financial data available for the selected period</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-12 mb-4">
                                    <h5>Payment Method Breakdown</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Payment Method</th>
                                                    <th>Orders</th>
                                                    <th>Revenue</th>
                                                    <th>Percentage</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if payment_methods %}
                                                    {% for method in payment_methods %}
                                                    <tr>
                                                        <td>{{ method.payment_method }}</td>
                                                        <td>{{ method.order_count }}</td>
                                                        <td>{{ "%.2f"|format(method.total_amount) }}</td>
                                                        <td>
                                                            {% set total_revenue = summary.total_revenue or 1 %}
                                                            {% set percentage = (method.total_amount / total_revenue * 100) if total_revenue > 0 else 0 %}
                                                            {{ "%.1f"|format(percentage) }}%
                                                            <div class="progress">
                                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ percentage }}%"></div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="4" class="text-center">No payment method data available</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <h5>Product Category Breakdown</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Category</th>
                                                    <th>Revenue</th>
                                                    <th>Percentage</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if product_categories %}
                                                    {% for category in product_categories %}
                                                    <tr>
                                                        <td>{{ category.category }}</td>
                                                        <td>{{ "%.2f"|format(category.category_revenue) }}</td>
                                                        <td>
                                                            {{ "%.1f"|format(category.percentage) }}%
                                                            <div class="progress">
                                                                <div class="progress-bar bg-info" role="progressbar" style="width: {{ category.percentage }}%"></div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="3" class="text-center">No category data available</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .card-body, .card-body * {
            visibility: visible;
        }
        .card-body {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .card-header, .btn, form {
            display: none;
        }
    }
</style>
{% endblock %}


{% block scripts %}
<script>
    // Fix for print functionality - prevent double printing
    document.addEventListener('DOMContentLoaded', function() {
        const printBtn = document.getElementById('printFinancialSummaryBtn');
        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Disable button temporarily to prevent double clicks
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
                
                // Print after a short delay
                setTimeout(function() {
                    window.print();
                    
                    // Re-enable button after printing
                    setTimeout(function() {
                        printBtn.disabled = false;
                        printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
                    }, 1000);
                }, 100);
            });
        }
    });
</script>
{% endblock %}