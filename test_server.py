"""
Simple test server for TCS tracking API
"""

from flask import Flask, request, jsonify
import json

# Create a simple Flask app for testing
test_app = Flask(__name__)

@test_app.route('/api/track-tcs-public', methods=['POST'])
def test_track_tcs():
    """Test TCS tracking endpoint"""
    try:
        from tcs_scraper_demo import track_tcs_demo, validate_tracking_number
        
        data = request.get_json()
        tracking_number = data.get('tracking_number')
        
        if not tracking_number:
            return jsonify({'success': False, 'error': 'Tracking number is required'})
        
        # Validate tracking number format
        tracking_number = str(tracking_number).strip()
        if not validate_tracking_number(tracking_number):
            return jsonify({'success': False, 'error': 'Invalid tracking number format'})
        
        print(f"📦 Tracking: {tracking_number}")
        
        # Use demo scraper
        tracking_data = track_tcs_demo(tracking_number, headless=True)
        
        if tracking_data.get('success', False):
            print(f"✅ Success: {tracking_data.get('current_status')}")
            return jsonify({'success': True, 'data': tracking_data})
        else:
            print(f"❌ Failed: {tracking_data.get('error')}")
            return jsonify({
                'success': False,
                'error': tracking_data.get('error', 'Failed to fetch tracking information')
            })
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return jsonify({'success': False, 'error': 'TCS scraper module not available'})
    except Exception as e:
        print(f"❌ System error: {e}")
        return jsonify({'success': False, 'error': f'System error: {str(e)}'})

@test_app.route('/test')
def test_endpoint():
    """Simple test endpoint"""
    return jsonify({'message': 'Test server is working!', 'status': 'ok'})

if __name__ == '__main__':
    print("🚀 Starting TCS Test Server...")
    print("📱 URL: http://localhost:5000")
    print("🧪 Test endpoint: http://localhost:5000/test")
    print("📦 TCS API: http://localhost:5000/api/track-tcs-public")
    print("=" * 50)
    
    test_app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
