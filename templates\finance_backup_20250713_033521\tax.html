{% extends 'base.html' %}

{% block title %}Tax Reports{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Tax Reports</h5>
                        <button type="button" class="btn btn-light btn-sm" onclick="goBack()">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form action="{{ url_for('finance') }}" method="GET" class="form-inline">
                                <input type="hidden" name="view" value="tax">
                                <div class="form-group mr-2">
                                    <label for="start_date" class="mr-2">Start Date:</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', now.strftime('%Y-%m-%d')) }}">
                                </div>
                                <div class="form-group mr-2">
                                    <label for="end_date" class="mr-2">End Date:</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', now.strftime('%Y-%m-%d')) }}">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="{{ url_for('export_tax_report', format='excel') }}?start_date={{ request.args.get('start_date', '') }}&end_date={{ request.args.get('end_date', '') }}" class="btn btn-success ml-2">
                                    <i class="fas fa-file-excel"></i> Export to Excel
                                </a>
                                <a href="{{ url_for('export_tax_report', format='pdf') }}?start_date={{ request.args.get('start_date', '') }}&end_date={{ request.args.get('end_date', '') }}" class="btn btn-danger ml-2">
                                    <i class="fas fa-file-pdf"></i> Export to PDF
                                </a>
                            </form>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total Sales</h6>
                                    <h3 class="text-primary">
                                        {% if tax_report %}
                                        {{ tax_report|sum(attribute='total_amount')|round(2)|format_currency }}
                                        {% else %}
                                        {{ 0|format_currency }}
                                        {% endif %}
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total Tax</h6>
                                    <h3 class="text-danger">
                                        {% if tax_report %}
                                        {{ tax_report|sum(attribute='tax_amount')|round(2)|format_currency }}
                                        {% else %}
                                        {{ 0|format_currency }}
                                        {% endif %}
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total Invoices</h6>
                                    <h3 class="text-success">
                                        {% if tax_report %}
                                        {{ tax_report|length }}
                                        {% else %}
                                        0
                                        {% endif %}
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="taxTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Subtotal</th>
                                    <th>Tax Amount</th>
                                    <th>Tax Rate</th>
                                    <th>Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if tax_report %}
                                    {% for invoice in tax_report %}
                                    <tr>
                                        <td>{{ invoice.invoice_number }}</td>
                                        <td>{{ invoice.date_generated }}</td>
                                        <td>{{ invoice.customer_name }}</td>
                                        <td>{{ invoice.subtotal_amount|default(invoice.order_amount)|round(2)|format_currency }}</td>
                                        <td>{{ invoice.tax_amount|round(2)|format_currency }}</td>
                                        <td>{{ (invoice.tax_amount / invoice.subtotal_amount * 100)|round(2) if invoice.subtotal_amount and invoice.subtotal_amount > 0 else (invoice.tax_amount / invoice.order_amount * 100)|round(2) if invoice.order_amount and invoice.order_amount > 0 else 0 }}%</td>
                                        <td>{{ ((invoice.total_amount|default(0)) if invoice.total_amount else ((invoice.order_amount|default(0)) + (invoice.tax_amount|default(0))))|format_currency }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center">No tax data found for the selected period</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Tax report scripts
</script>
{% endblock %}
