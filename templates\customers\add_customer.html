{% extends "base.html" %}

{% block title %}Add New Customer{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus"></i> Add New Customer
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Customers
                            </a>
                        </div>
                    </div>

                    <form action="{{ url_for('add_customer') }}" method="POST" class="needs-validation" enctype="multipart/form-data" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">Customer Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">
                                        Please provide a customer name.
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                    <div class="invalid-feedback">
                                        Please provide a phone number.
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>

                                <div class="form-group mb-3">
                                    <label for="customer_type" class="form-label">Customer Type *</label>
                                    <select class="form-control" id="customer_type" name="customer_type" required>
                                        <option value="">Select Type</option>
                                        <option value="Retail">Retail</option>
                                        <option value="Wholesale">Wholesale</option>
                                        <option value="Corporate">Corporate</option>
                                        <option value="Government">Government</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a customer type.
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="category" class="form-label">Customer Category *</label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        <option value="Institute">Institute</option>
                                        <option value="Distributor">Distributor</option>
                                        <option value="Direct Customer" selected>Direct Customer</option>
                                        <option value="Doctor">Doctor</option>
                                        <option value="Private Customer">Private Customer</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a customer category.
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="ntn_number" class="form-label">NTN Number</label>
                                    <input type="text" class="form-control" id="ntn_number" name="ntn_number" placeholder="Pakistani Tax Number">
                                    <small class="form-text text-muted">National Tax Number (Pakistan)</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city">
                                </div>

                                <div class="form-group mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state">
                                </div>

                                <div class="form-group mb-3">
                                    <label for="credit_limit" class="form-label">Credit Limit</label>
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit" step="0.01" min="0">
                                </div>

                                <div class="form-group mb-3">
                                    <label for="risk_category" class="form-label">Risk Category</label>
                                    <select class="form-control" id="risk_category" name="risk_category">
                                        <option value="Low">Low Risk</option>
                                        <option value="Medium" selected>Medium Risk</option>
                                        <option value="High">High Risk</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Additional notes about the customer..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- File Upload Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-paperclip"></i> Customer Documents</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="customer_files" class="form-label">Upload Files</label>
                                            <div class="input-group">
                                                <input type="file" class="form-control" id="customer_files" name="customer_files" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-primary" onclick="addMoreFiles()">
                                                        <i class="fas fa-plus"></i> Add More
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">You can upload multiple files. Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG, TXT</small>
                                        </div>

                                        <!-- File Preview Area -->
                                        <div id="file-preview-area" class="mt-3" style="display: none;">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Selected Files:</h6>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllFiles()">
                                                    <i class="fas fa-trash"></i> Clear All
                                                </button>
                                            </div>
                                            <div id="file-list" class="row"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Add Customer
                                </button>
                                <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// File upload preview functionality with accumulation
let customerAccumulatedFiles = []; // Store all selected files

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('customer_files');
    const previewArea = document.getElementById('file-preview-area');
    const fileList = document.getElementById('file-list');

    fileInput.addEventListener('change', function(e) {
        const newFiles = Array.from(e.target.files);

        // Add new files to accumulated files
        customerAccumulatedFiles = customerAccumulatedFiles.concat(newFiles);

        // Update the file input with all accumulated files
        updateCustomerFileInput();

        // Display all files
        displayAllCustomerFiles();
    });

    function updateCustomerFileInput() {
        const dt = new DataTransfer();
        customerAccumulatedFiles.forEach(file => {
            dt.items.add(file);
        });
        fileInput.files = dt.files;
    }

    function displayAllCustomerFiles() {
        if (customerAccumulatedFiles.length > 0) {
            previewArea.style.display = 'block';
            fileList.innerHTML = '';

            customerAccumulatedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'col-md-4 mb-3';

                const fileCard = document.createElement('div');
                fileCard.className = 'card border-secondary';

                const fileIcon = getFileIcon(file.type);
                const fileSize = formatFileSize(file.size);

                fileCard.innerHTML = `
                    <div class="card-body text-center">
                        <i class="${fileIcon} fa-2x mb-2 text-primary"></i>
                        <h6 class="card-title">${file.name}</h6>
                        <p class="card-text small text-muted">${fileSize}</p>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                        ${file.type.startsWith('image/') ? `<button type="button" class="btn btn-sm btn-outline-info ml-1" onclick="previewImage(${index})"><i class="fas fa-eye"></i> Preview</button>` : ''}
                    </div>
                `;

                fileItem.appendChild(fileCard);
                fileList.appendChild(fileItem);
            });
        } else {
            previewArea.style.display = 'none';
        }
    }

    // Make functions globally accessible
    window.updateCustomerFileInput = updateCustomerFileInput;
    window.displayAllCustomerFiles = displayAllCustomerFiles;
});

function getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) return 'fas fa-image';
    if (mimeType.includes('pdf')) return 'fas fa-file-pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
    if (mimeType.includes('text')) return 'fas fa-file-alt';
    return 'fas fa-file';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeFile(index) {
    // Remove file from accumulated files array
    customerAccumulatedFiles.splice(index, 1);

    // Update file input and display
    updateCustomerFileInput();
    displayAllCustomerFiles();
}

function previewImage(index) {
    const file = customerAccumulatedFiles[index];

    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Create modal for image preview
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${file.name}</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="${e.target.result}" class="img-fluid" alt="${file.name}">
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            $(modal).modal('show');

            // Remove modal after hiding
            $(modal).on('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        };
        reader.readAsDataURL(file);
    }
}

function addMoreFiles() {
    const fileInput = document.getElementById('customer_files');
    fileInput.click();
}

function clearAllFiles() {
    const fileInput = document.getElementById('customer_files');
    const previewArea = document.getElementById('file-preview-area');

    // Clear accumulated files
    customerAccumulatedFiles = [];
    fileInput.value = '';
    previewArea.style.display = 'none';
}
</script>
{% endblock %}
