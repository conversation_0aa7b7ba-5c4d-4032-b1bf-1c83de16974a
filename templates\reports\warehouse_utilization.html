{% extends 'base.html' %}

{% block title %}Warehouse Utilization Report{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar text-primary"></i> Warehouse Utilization Report
        </h1>
        <div>
            <a href="{{ url_for('warehouse_reports') }}" class="btn btn-outline-primary btn-sm mr-2">
                <i class="fas fa-arrow-left"></i> Back to Reports
            </a>
            <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Export to Excel
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Warehouses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ warehouses|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Average Utilization</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set total_pct = 0 %}
                                {% for w in warehouse_utilization %}
                                    {% set total_pct = total_pct + w.utilization_pct %}
                                {% endfor %}
                                {{ (total_pct / warehouses|length)|round(1) if warehouses|length > 0 else 0 }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Most Utilized</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set max_util = 0 %}
                                {% set max_name = 'None' %}
                                {% for w in warehouse_utilization %}
                                    {% if w.utilization_pct > max_util %}
                                        {% set max_util = w.utilization_pct %}
                                        {% set max_name = w.name %}
                                    {% endif %}
                                {% endfor %}
                                {{ max_name }} ({{ max_util }}%)
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Least Utilized</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set min_util = 100 %}
                                {% set min_name = 'None' %}
                                {% for w in warehouse_utilization %}
                                    {% if w.utilization_pct < min_util %}
                                        {% set min_util = w.utilization_pct %}
                                        {% set min_name = w.name %}
                                    {% endif %}
                                {% endfor %}
                                {{ min_name }} ({{ min_util }}%)
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Utilization Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Warehouse Utilization Details</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="utilizationTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Warehouse</th>
                            <th>Capacity</th>
                            <th>Used Space</th>
                            <th>Available Space</th>
                            <th>Utilization</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for warehouse in warehouse_utilization %}
                        <tr>
                            <td>{{ warehouse.name }}</td>
                            <td>{{ warehouse.capacity|format_number }} units</td>
                            <td>{{ warehouse.used_space|format_number }} units</td>
                            <td>{{ warehouse.available_space|format_number }} units</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar 
                                        {% if warehouse.utilization_pct > 90 %}bg-danger
                                        {% elif warehouse.utilization_pct > 75 %}bg-warning
                                        {% elif warehouse.utilization_pct > 50 %}bg-info
                                        {% else %}bg-success{% endif %}" 
                                        role="progressbar" 
                                        style="width: {{ warehouse.utilization_pct }}%" 
                                        aria-valuenow="{{ warehouse.utilization_pct }}" 
                                        aria-valuemin="0" 
                                        aria-valuemax="100">{{ warehouse.utilization_pct }}%</div>
                                </div>
                            </td>
                            <td>
                                {% if warehouse.utilization_pct > 90 %}
                                <span class="badge badge-danger">Critical</span>
                                {% elif warehouse.utilization_pct > 75 %}
                                <span class="badge badge-warning">High</span>
                                {% elif warehouse.utilization_pct > 50 %}
                                <span class="badge badge-info">Moderate</span>
                                {% else %}
                                <span class="badge badge-success">Good</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#utilizationTable').DataTable({
            order: [[4, 'desc']]
        });
    });
    
    function exportToExcel() {
        // Create a workbook
        var wb = XLSX.utils.book_new();
        
        // Convert table to worksheet
        var ws = XLSX.utils.table_to_sheet(document.getElementById('utilizationTable'));
        
        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, "Warehouse Utilization");
        
        // Generate Excel file and trigger download
        XLSX.writeFile(wb, "Warehouse_Utilization_Report_{{ now.strftime('%Y%m%d') }}.xlsx");
    }
</script>
{% endblock %}
