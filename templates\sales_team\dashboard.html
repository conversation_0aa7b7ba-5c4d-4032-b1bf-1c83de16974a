{% extends "base.html" %}

{% block title %}Sales Team Dashboard - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="h3 mb-4">👥 Sales Team Dashboard</h1>
            <p class="text-muted">Division-wise sales performance and team analytics</p>
        </div>
    </div>

    <!-- Division Performance Cards -->
    <div class="row mb-4">
        {% if division_sales and division_sales|length > 0 %}
            {% for division in division_sales[:4] %}
            <div class="col-md-3">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">{{ division.division }}</h6>
                                <h4 class="mb-0">Rs.{{ "{:,.0f}".format(division.total_revenue) }}</h4>
                                <small>{{ division.order_count }} orders</small>
                            </div>
                            <i class="fas fa-building fa-2x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="{{ url_for('sales_division_detail', division_name=division.division) }}" class="text-white">
                            View Details <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <!-- Empty State for Division Performance -->
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-bar fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">No Division Sales Data</h4>
                        <p class="text-muted mb-4">No sales data available for divisions yet. Start creating orders to see division performance.</p>
                        <a href="{{ url_for('orders') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Order
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Monthly Targets vs Achievements -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">🎯 Monthly Targets vs Achievements</h5>
                </div>
                <div class="card-body">
                    {% if monthly_targets and monthly_targets|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Division</th>
                                    <th>Target</th>
                                    <th>Achieved</th>
                                    <th>Progress</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for target in monthly_targets %}
                                {% set progress = (target.achieved / target.target * 100) if target.target > 0 else 0 %}
                                <tr>
                                    <td><strong>{{ target.division }}</strong></td>
                                    <td>Rs.{{ "{:,.0f}".format(target.target) }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(target.achieved) }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar
                                                {% if progress >= 90 %}bg-success
                                                {% elif progress >= 70 %}bg-warning
                                                {% else %}bg-danger{% endif %}"
                                                role="progressbar"
                                                style="width: {{ progress }}%">
                                                {{ "%.1f"|format(progress) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if progress >= 90 %}
                                            <span class="badge badge-success">Excellent</span>
                                        {% elif progress >= 70 %}
                                            <span class="badge badge-warning">Good</span>
                                        {% else %}
                                            <span class="badge badge-danger">Needs Improvement</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <!-- Empty State for Monthly Targets -->
                    <div class="text-center py-5">
                        <i class="fas fa-target fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">No Monthly Targets Set</h4>
                        <p class="text-muted mb-4">No monthly targets have been configured for divisions yet. Set up targets to track performance.</p>
                        <button class="btn btn-primary" onclick="alert('Target setting feature coming soon!')">
                            <i class="fas fa-plus"></i> Set Monthly Targets
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Performing Agents -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">🏆 Top Performing Agents</h5>
                </div>
                <div class="card-body">
                    {% if agent_performance and agent_performance|length > 0 %}
                        {% for agent in agent_performance[:8] %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1">{{ agent.sales_agent }}</h6>
                                <small class="text-muted">{{ agent.division }} • {{ agent.orders }} orders</small>
                            </div>
                            <div class="text-right">
                                <strong>Rs.{{ "{:,.0f}".format(agent.revenue) }}</strong>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <!-- Empty State for Top Agents -->
                        <div class="text-center py-4">
                            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-2">No Agent Performance Data</h5>
                            <p class="text-muted mb-0">No sales agent data available yet.</p>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    {% if agent_performance and agent_performance|length > 0 %}
                        <a href="{{ url_for('sales_by_agent_report') }}" class="btn btn-sm btn-info">View Full Report</a>
                    {% else %}
                        <button class="btn btn-sm btn-secondary" disabled>No Data Available</button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Division Performance Chart -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">📊 Division Revenue Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="divisionRevenueChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">📈 Division Order Count</h5>
                </div>
                <div class="card-body">
                    <canvas id="divisionOrderChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">📋 Recent Orders by Division</h5>
                </div>
                <div class="card-body">
                    {% if recent_orders and recent_orders|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Division</th>
                                    <th>Sales Agent</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td><strong>{{ order.order_id }}</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(order.order_amount) }}</td>
                                    <td>
                                        <a href="{{ url_for('sales_division_detail', division_name=order.division) }}"
                                           class="badge badge-primary">{{ order.division }}</a>
                                    </td>
                                    <td>{{ order.sales_agent }}</td>
                                    <td>{{ order.order_date }}</td>
                                    <td>
                                        {% if order.status == 'Delivered' %}
                                            <span class="badge badge-success">{{ order.status }}</span>
                                        {% elif order.status == 'Dispatched' %}
                                            <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == 'Processing' %}
                                            <span class="badge badge-warning">{{ order.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <!-- Empty State for Recent Orders -->
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">No Recent Orders</h4>
                        <p class="text-muted mb-4">No orders have been placed yet. Start creating orders to see recent activity.</p>
                        <a href="{{ url_for('orders') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Order
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    {% if recent_orders and recent_orders|length > 0 %}
                        <a href="{{ url_for('orders') }}" class="btn btn-primary">View All Orders</a>
                        <a href="{{ url_for('reports') }}" class="btn btn-outline-primary ml-2">Sales Reports</a>
                    {% else %}
                        <a href="{{ url_for('orders') }}" class="btn btn-primary">Create Orders</a>
                        <button class="btn btn-outline-secondary ml-2" disabled>No Reports Available</button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Division Revenue Chart
const divisionRevenueCtx = document.getElementById('divisionRevenueChart').getContext('2d');
const divisionRevenueChart = new Chart(divisionRevenueCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for division in division_sales %}
            '{{ division.division }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for division in division_sales %}
                {{ division.total_revenue }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': Rs.' + context.parsed.toLocaleString();
                    }
                }
            }
        }
    }
});

// Division Order Chart
const divisionOrderCtx = document.getElementById('divisionOrderChart').getContext('2d');
const divisionOrderChart = new Chart(divisionOrderCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for division in division_sales %}
            '{{ division.division }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Orders',
            data: [
                {% for division in division_sales %}
                {{ division.order_count }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: '#36A2EB',
            borderColor: '#1E88E5',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.opacity-50 {
    opacity: 0.5;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
