{% extends 'base.html' %}

{% block title %}Place New Order - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Place New Order</h4>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('orders.new_order') }}" id="orderForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_phone">Customer Phone</label>
                                    <input type="text" class="form-control" id="customer_phone" name="customer_phone" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="customer_address">Customer Address</label>
                            <input type="text" class="form-control" id="customer_address" name="customer_address" required>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="po_number">PO Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="po_number" name="po_number" placeholder="Enter Purchase Order Number" required>
                                    <small class="form-text text-muted">Purchase Order Number is mandatory</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="payment_mode">Payment Mode</label>
                                    <select class="form-control" id="payment_mode" name="payment_mode" required>
                                        <option value="">Select Payment Mode</option>
                                        <option value="cash">Cash</option>
                                        <option value="cheque">Cheque</option>
                                        <option value="online">Online</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="sales_agent">Sales Agent</label>
                                    <input type="text" class="form-control" id="sales_agent" name="sales_agent" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Warehouse field removed - automatic allocation based on inventory -->
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="delivery_date">Expected Delivery Date</label>
                                    <input type="date" class="form-control" id="delivery_date" name="delivery_date">
                                    <small class="form-text text-muted">Optional: Expected delivery date</small>
                                </div>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">Products</h5>

                        <div id="products-container">
                            <div class="product-row mb-3 p-3 border rounded">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Division</label>
                                            <select class="form-control division-select">
                                                <option value="">All Divisions</option>
                                                {% for division in divisions %}
                                                <option value="{{ division.division_id }}">{{ division.name }}</option>
                                                {% endfor %}
                                            </select>
                                            <input type="hidden" name="division_id[]" class="division-id">
                                            <input type="hidden" name="division_name[]" class="division-name">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Product</label>
                                            <select class="form-control product-select" name="product_id[]" required>
                                                <option value="">Select Product</option>
                                                {% for product in products %}
                                                {% if product.available_stock and product.available_stock > 0 %}
                                                <option value="{{ product.product_id }}"
                                                        data-name="{{ product.name }}"
                                                        data-strength="{{ product.strength }}"
                                                        data-price="{{ product.unit_price }}"
                                                        data-mrp="{{ product.mrp or 0 }}"
                                                        data-tp="{{ product.tp_rate or 0 }}"
                                                        data-asp="{{ product.unit_price or 0 }}"
                                                        data-pack-size="{{ product.pack_size or 'N/A' }}"
                                                        data-stock="{{ product.available_stock }}"
                                                        data-division-id="{{ product.division_id }}"
                                                        data-division-name="{{ product.division_name }}"
                                                        data-country="{{ product.country }}"
                                                        data-manufacturer="{{ product.manufacturer }}">
                                                    {{ product.name }} ({{ product.strength }}) - {{ product.division_name }} [Stock: {{ product.available_stock }}]
                                                </option>
                                                {% endif %}
                                                {% endfor %}
                                            </select>
                                            <input type="hidden" name="product_name[]" class="product-name">
                                            <input type="hidden" name="product_strength[]" class="product-strength">
                                            <div class="form-text text-muted product-info mt-2"></div>
                                            <small class="form-text text-info">Only products with available inventory from valid divisions are shown</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Quantity</label>
                                            <input type="number" class="form-control quantity" name="quantity[]" min="1" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>FOC Qty</label>
                                            <input type="number" class="form-control foc-quantity" name="foc_quantity[]" min="0" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Rate</label>
                                            <input type="number" class="form-control rate" name="rate[]" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Amount</label>
                                            <input type="text" class="form-control amount" readonly>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-danger mt-2 remove-product" style="display: none;">Remove</button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="button" id="add-product" class="btn btn-info">
                                    <i class="fas fa-plus-circle"></i> Add Another Product
                                </button>
                            </div>
                        </div>

                        <!-- Order Documents Upload Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-paperclip"></i> Order Documents (Prescriptions, etc.)</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="order_files" class="form-label">Upload Files</label>
                                            <div class="input-group">
                                                <input type="file" class="form-control" id="order_files" name="order_files" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-primary" onclick="addMoreOrderFiles()">
                                                        <i class="fas fa-plus"></i> Add More
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">Upload prescriptions, documents, etc. Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG, TXT</small>
                                        </div>

                                        <!-- File Preview Area -->
                                        <div id="order-file-preview-area" class="mt-3" style="display: none;">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Selected Files:</h6>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllOrderFiles()">
                                                    <i class="fas fa-trash"></i> Clear All
                                                </button>
                                            </div>
                                            <div id="order-file-list" class="row"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-6 offset-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <h5>Total Amount:</h5>
                                            <h5 id="total-amount">Rs. 0.00</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12 text-right">
                                <a href="{{ url_for('orders.index') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Place Order</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Handle division selection
        $(document).on('change', '.division-select', function() {
            var row = $(this).closest('.product-row');
            var divisionId = $(this).val();
            var productSelect = row.find('.product-select');

            // Store division ID and name
            row.find('.division-id').val(divisionId);
            row.find('.division-name').val($(this).find('option:selected').text());

            // Reset product selection
            productSelect.val('');
            row.find('.product-name').val('');
            row.find('.product-strength').val('');
            row.find('.rate').val('');
            row.find('.product-info').text('');

            // Filter products by division using the helper function
            filterProductsByDivision(row, divisionId);

            // Recalculate amount
            calculateAmount(row);
        });

        // Handle product selection
        $(document).on('change', '.product-select', function() {
            var selectedOption = $(this).find('option:selected');
            var row = $(this).closest('.product-row');

            // Set product details
            row.find('.product-name').val(selectedOption.data('name'));
            row.find('.product-strength').val(selectedOption.data('strength'));
            row.find('.rate').val(selectedOption.data('price'));

            // Show comprehensive product information
            if (selectedOption.val()) {
                var mrp = selectedOption.data('mrp') || 0;
                var tp = selectedOption.data('tp') || 0;
                var asp = selectedOption.data('asp') || 0;
                var packSize = selectedOption.data('pack-size') || 'N/A';
                var stock = selectedOption.data('stock') || 0;
                var country = selectedOption.data('country') || 'Unknown';
                var manufacturer = selectedOption.data('manufacturer') || 'Generic';
                var divisionName = selectedOption.data('division-name') || 'Unassigned';

                // Determine stock status
                var stockStatus = stock > 0 ?
                    `<span class="text-success"><i class="fas fa-check-circle"></i> Available (${stock})</span>` :
                    `<span class="text-danger"><i class="fas fa-times-circle"></i> Out of Stock</span>`;

                // Create comprehensive product info
                var productInfo = `
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>Pricing:</strong> MRP: ₹${mrp} | TP: ₹${tp} | ASP: ₹${asp}<br>
                            <strong>Pack Size:</strong> ${packSize} | <strong>Stock:</strong> ${stockStatus}
                        </div>
                        <div class="col-md-6">
                            <strong>Details:</strong> ${country} | ${manufacturer} | ${divisionName}
                        </div>
                    </div>
                `;

                row.find('.product-info').html(productInfo);
            } else {
                row.find('.product-info').html('');
            }

            // Auto-set division when product is selected (prevent infinite loop)
            var divisionSelect = row.find('.division-select');
            if (selectedOption.val()) {
                var divisionId = selectedOption.data('division-id');
                var divisionName = selectedOption.data('division-name');

                if (divisionId && divisionSelect.val() !== divisionId) {
                    // Only update if division is different to prevent loops
                    divisionSelect.val(divisionId);
                    row.find('.division-id').val(divisionId);
                    row.find('.division-name').val(divisionName);

                    // Manually filter products without triggering change event
                    filterProductsByDivision(row, divisionId);
                } else if (divisionName && !divisionId) {
                    // Fallback: Find the division option by name and select it
                    divisionSelect.find('option').each(function() {
                        if ($(this).text() === divisionName && divisionSelect.val() !== $(this).val()) {
                            divisionSelect.val($(this).val());
                            row.find('.division-id').val($(this).val());
                            row.find('.division-name').val(divisionName);

                            // Manually filter products
                            filterProductsByDivision(row, $(this).val());
                            return false; // break the loop
                        }
                    });
                }
            }

            // Recalculate amount
            calculateAmount(row);
        });

        // Handle quantity and rate changes
        $(document).on('input', '.quantity, .rate', function() {
            var row = $(this).closest('.product-row');
            calculateAmount(row);
        });

        // Add new product row
        $('#add-product').click(function() {
            var newRow = $('#products-container .product-row:first').clone();
            newRow.find('input').val('');
            newRow.find('select').val('');
            newRow.find('.remove-product').show();
            $('#products-container').append(newRow);
            calculateTotal();
        });

        // Remove product row
        $(document).on('click', '.remove-product', function() {
            $(this).closest('.product-row').remove();
            calculateTotal();
        });

        // Calculate amount for a row
        function calculateAmount(row) {
            var quantity = parseFloat(row.find('.quantity').val()) || 0;
            var rate = parseFloat(row.find('.rate').val()) || 0;
            var amount = quantity * rate;
            row.find('.amount').val(amount.toFixed(2));
            calculateTotal();
        }

        // Calculate total amount
        function calculateTotal() {
            var total = 0;
            $('.amount').each(function() {
                total += parseFloat($(this).val()) || 0;
            });
            $('#total-amount').text('Rs. ' + total.toFixed(2));
        }

        // Form validation
        $('#orderForm').submit(function(e) {
            var valid = true;

            // Check if at least one product is selected
            if ($('.product-select').length === 0 || $('.product-select:first').val() === '') {
                alert('Please select at least one product');
                valid = false;
            }

            if (!valid) {
                e.preventDefault();
            }
        });

        // Order file upload functionality with accumulation
        const orderFileInput = document.getElementById('order_files');
        const orderPreviewArea = document.getElementById('order-file-preview-area');
        const orderFileList = document.getElementById('order-file-list');
        let accumulatedFiles = []; // Store all selected files

        if (orderFileInput) {
            orderFileInput.addEventListener('change', function(e) {
                const newFiles = Array.from(e.target.files);

                // Add new files to accumulated files
                accumulatedFiles = accumulatedFiles.concat(newFiles);

                // Update the file input with all accumulated files
                updateFileInput();

                // Display all files
                displayAllFiles();
            });
        }

        function updateFileInput() {
            const dt = new DataTransfer();
            accumulatedFiles.forEach(file => {
                dt.items.add(file);
            });
            orderFileInput.files = dt.files;
        }

        function displayAllFiles() {
            if (accumulatedFiles.length > 0) {
                orderPreviewArea.style.display = 'block';
                orderFileList.innerHTML = '';

                accumulatedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'col-md-4 mb-3';

                    const fileCard = document.createElement('div');
                    fileCard.className = 'card border-secondary';

                    const fileIcon = getOrderFileIcon(file.type);
                    const fileSize = formatOrderFileSize(file.size);

                    fileCard.innerHTML = `
                        <div class="card-body text-center">
                            <i class="${fileIcon} fa-2x mb-2 text-primary"></i>
                            <h6 class="card-title">${file.name}</h6>
                            <p class="card-text small text-muted">${fileSize}</p>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOrderFile(${index})">
                                <i class="fas fa-trash"></i> Remove
                            </button>
                            ${file.type.startsWith('image/') ? `<button type="button" class="btn btn-sm btn-outline-info ml-1" onclick="previewOrderImage(${index})"><i class="fas fa-eye"></i> Preview</button>` : ''}
                        </div>
                    `;

                    fileItem.appendChild(fileCard);
                    orderFileList.appendChild(fileItem);
                });
            } else {
                orderPreviewArea.style.display = 'none';
            }
        }
    });

    // Helper function to filter products by division
    function filterProductsByDivision(row, divisionId) {
        var productSelect = row.find('.product-select');

        // Show all products first
        productSelect.find('option').show();

        if (divisionId) {
            // Hide products that don't belong to the selected division
            productSelect.find('option:not(:first)').each(function() {
                if ($(this).data('division-id') != divisionId) {
                    $(this).hide();
                }
            });
        }
    }

    function getOrderFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return 'fas fa-image';
        if (mimeType.includes('pdf')) return 'fas fa-file-pdf';
        if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
        if (mimeType.includes('text')) return 'fas fa-file-alt';
        return 'fas fa-file';
    }

    function formatOrderFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function removeOrderFile(index) {
        // Remove file from accumulated files array
        accumulatedFiles.splice(index, 1);

        // Update file input and display
        updateFileInput();
        displayAllFiles();
    }

    function addMoreOrderFiles() {
        const fileInput = document.getElementById('order_files');
        fileInput.click();
    }

    function clearAllOrderFiles() {
        const fileInput = document.getElementById('order_files');
        const previewArea = document.getElementById('order-file-preview-area');

        // Clear accumulated files
        accumulatedFiles = [];
        fileInput.value = '';
        previewArea.style.display = 'none';
    }

    function previewOrderImage(index) {
        const file = accumulatedFiles[index];

        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Create modal for image preview
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${file.name}</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${e.target.result}" class="img-fluid" alt="${file.name}">
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                $(modal).modal('show');

                // Remove modal after hiding
                $(modal).on('hidden.bs.modal', function() {
                    document.body.removeChild(modal);
                });
            };
            reader.readAsDataURL(file);
        }
    }
</script>
{% endblock %}
