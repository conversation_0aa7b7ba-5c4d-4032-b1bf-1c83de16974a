{% extends "base.html" %}

{% block title %}Enhanced Invoice Generation - Finance{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .finance-dashboard {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .finance-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .finance-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .finance-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        margin-bottom: 0;
    }
    
    .invoice-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }
    
    .invoice-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--secondary));
    }
    
    .invoice-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .order-info {
        flex: 1;
    }
    
    .order-id {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary);
        margin-bottom: 5px;
    }
    
    .customer-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 5px;
    }
    
    .order-details {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .amount-info {
        text-align: right;
    }
    
    .order-amount {
        font-size: 1.8rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 5px;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
    }
    
    .status-approved {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }
    
    .status-hold {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
    }
    
    .order-items {
        margin: 15px 0;
    }
    
    .item-summary {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .item-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .item-row:last-child {
        border-bottom: none;
    }
    
    .item-name {
        font-weight: 600;
        color: var(--dark);
    }
    
    .item-details {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .item-amount {
        font-weight: 600;
        color: #28a745;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-top: 15px;
    }
    
    .btn-modern {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .btn-modern:hover {
        transform: translateY(-1px);
    }
    
    .filter-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        text-align: center;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin: 0 auto 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: var(--dark);
    }
    
    .stat-label {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .invoice-modal .modal-content {
        border-radius: 15px;
        border: none;
    }
    
    .invoice-modal .modal-header {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    .hold-reason {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 8px;
        padding: 10px;
        margin-top: 10px;
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-file-invoice me-3"></i>Enhanced Invoice Generation
                    </h1>
                    <p class="finance-subtitle">Generate invoices with order details, customer ledger access, and hold functionality</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-modern" onclick="bulkInvoiceGeneration()">
                        <i class="fas fa-layer-group me-2"></i>Bulk Generate
                    </button>
                    <button class="btn btn-light btn-modern" onclick="exportPendingOrders()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Order Filters
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label fw-bold">Order ID</label>
                    <input type="text" class="form-control" name="order_id" 
                           value="{{ filters.order_id if filters else '' }}" placeholder="Order ID">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" name="customer" 
                           value="{{ filters.customer if filters else '' }}" placeholder="Customer name">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Status</label>
                    <select class="form-control" name="status">
                        <option value="all" {{ 'selected' if filters and filters.status == 'all' else '' }}>All Status</option>
                        <option value="pending" {{ 'selected' if filters and filters.status == 'pending' else '' }}>Pending Invoice</option>
                        <option value="approved" {{ 'selected' if filters and filters.status == 'approved' else '' }}>Approved</option>
                        <option value="hold" {{ 'selected' if filters and filters.status == 'hold' else '' }}>On Hold</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Date From</label>
                    <input type="date" class="form-control" name="date_from" value="{{ filters.date_from if filters else '' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Date To</label>
                    <input type="date" class="form-control" name="date_to" value="{{ filters.date_to if filters else '' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-modern d-block w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">{{ summary.pending_orders if summary else 0 }}</div>
                    <div class="stat-label">Pending Orders</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.pending_amount if summary and summary.pending_amount else 0) }}</div>
                    <div class="stat-label">Pending Amount</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-value">{{ summary.invoices_generated_today if summary else 0 }}</div>
                    <div class="stat-label">Generated Today</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-value">{{ summary.orders_on_hold if summary else 0 }}</div>
                    <div class="stat-label">Orders on Hold</div>
                </div>
            </div>
        </div>

        <!-- Invoice Generation Analytics -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>Invoice Generation Trends
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" onclick="showInvoiceChart('daily')">Daily</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showInvoiceChart('weekly')">Weekly</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 250px;">
                        <canvas id="invoiceGenerationChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card h-100">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie me-2 text-success"></i>Order Status Distribution
                    </h5>
                    <div style="position: relative; height: 200px;">
                        <canvas id="orderStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Orders List -->
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4 text-white">
                    <i class="fas fa-list me-2"></i>Pending Orders for Invoice Generation
                    <span class="badge bg-light text-dark ms-2">{{ orders|length if orders else 0 }} Orders</span>
                </h4>

                {% if orders %}
                    {% for order in orders %}
                    <div class="invoice-card">
                        <div class="invoice-header">
                            <div class="order-info">
                                <div class="order-id">{{ order.order_id }}</div>
                                <div class="customer-name">{{ order.customer_name }}</div>
                                <div class="order-details">
                                    <i class="fas fa-calendar me-1"></i>{{ safe_strftime(order.order_date, '%d %b %Y') if order.order_date else 'N/A' }}
                                    {% if order.sales_agent %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-user-tie me-1"></i>{{ order.sales_agent }}
                                    {% endif %}
                                    {% if order.division %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-building me-1"></i>{{ order.division }}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="amount-info">
                                <div class="order-amount">₹{{ "{:,.0f}".format(order.order_amount or 0) }}</div>
                                <span class="status-badge status-{{ order.status.lower() if order.status else 'pending' }}">
                                    {{ order.status or 'Pending' }}
                                </span>
                            </div>
                        </div>

                        <!-- Order Items Summary -->
                        {% if order.items %}
                        <div class="order-items">
                            <div class="item-summary">
                                <h6 class="mb-2">
                                    <i class="fas fa-boxes me-2"></i>Order Items ({{ order.items|length }})
                                </h6>
                                {% for item in order.items[:3] %}
                                <div class="item-row">
                                    <div>
                                        <div class="item-name">{{ item.product_name }}</div>
                                        <div class="item-details">Qty: {{ item.quantity }} | Rate: ₹{{ "{:,.0f}".format(item.rate or 0) }}</div>
                                    </div>
                                    <div class="item-amount">₹{{ "{:,.0f}".format((item.quantity or 0) * (item.rate or 0)) }}</div>
                                </div>
                                {% endfor %}
                                {% if order.items|length > 3 %}
                                <div class="text-center mt-2">
                                    <small class="text-muted">... and {{ order.items|length - 3 }} more items</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-success btn-modern btn-sm" onclick="generateInvoice('{{ order.order_id }}', '{{ order.customer_name }}', {{ order.order_amount }})">
                                <i class="fas fa-file-invoice me-1"></i>Generate Invoice
                            </button>

                            <button class="btn btn-info btn-modern btn-sm" onclick="viewOrderDetails('{{ order.order_id }}')">
                                <i class="fas fa-eye me-1"></i>View Details
                            </button>

                            <button class="btn btn-outline-primary btn-modern btn-sm" onclick="viewCustomerLedger('{{ order.customer_name }}')">
                                <i class="fas fa-user me-1"></i>Customer Ledger
                            </button>

                            <button class="btn btn-outline-warning btn-modern btn-sm" onclick="putOnHold('{{ order.order_id }}')">
                                <i class="fas fa-pause me-1"></i>Put on Hold
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <div class="stat-card">
                            <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No pending orders found</h5>
                            <p class="text-muted">All orders have been invoiced or no orders match your filters.</p>
                            <button class="btn btn-primary btn-modern" onclick="location.href='{{ url_for('finance_pending_invoices') }}'">
                                <i class="fas fa-refresh me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Invoice Generation Functions
function generateInvoice(orderId, customerName, orderAmount) {
    if (confirm(`Generate invoice for order ${orderId}?`)) {
        fetch('/finance/api/generate-invoice', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_id: orderId,
                customer_name: customerName,
                order_amount: orderAmount
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Invoice ${data.invoice_id} generated successfully!`);
                location.reload();
            } else {
                alert('Error generating invoice: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error generating invoice');
        });
    }
}

function viewOrderDetails(orderId) {
    window.open(`/orders/view/${orderId}`, '_blank');
}

function viewCustomerLedger(customerName) {
    window.open(`/finance/customer-ledger?customer=${encodeURIComponent(customerName)}`, '_blank');
}

function putOnHold(orderId) {
    const reason = prompt('Enter reason for putting order on hold:');
    if (reason && reason.trim()) {
        fetch('/finance/api/put-order-on-hold', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_id: orderId,
                hold_reason: reason.trim(),
                hold_notes: `Order put on hold by ${getCurrentUser()} on ${new Date().toLocaleString()}`
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Order ${orderId} has been put on hold successfully.`);
                location.reload(); // Refresh the page to update the status
            } else {
                alert('Error putting order on hold: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error putting order on hold. Please try again.');
        });
    }
}

function getCurrentUser() {
    // Get current user from the page context or session
    return document.querySelector('[data-current-user]')?.dataset.currentUser || 'Unknown User';
}

function exportPendingOrders() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '/finance/export-pending-orders?' + params.toString();
}

// Invoice Generation Charts
let invoiceChart = null;
let statusChart = null;

function initializeInvoiceCharts() {
    // Invoice Generation Trend Chart
    const invoiceCtx = document.getElementById('invoiceGenerationChart').getContext('2d');

    invoiceChart = new Chart(invoiceCtx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Invoices Generated',
                data: [15, 23, 18, 28, 32, 25, 30],
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(0, 123, 255, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 5
                    }
                }
            }
        }
    });

    // Order Status Distribution Chart
    const statusCtx = document.getElementById('orderStatusChart').getContext('2d');

    statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Pending', 'Generated', 'On Hold', 'Dispatched'],
            datasets: [{
                data: [45, 30, 8, 17],
                backgroundColor: [
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(0, 123, 255, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 193, 7, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(0, 123, 255, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

function showInvoiceChart(period) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (invoiceChart) {
        invoiceChart.destroy();
    }

    const ctx = document.getElementById('invoiceGenerationChart').getContext('2d');
    let chartData, chartLabels;

    switch(period) {
        case 'daily':
            chartLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            chartData = [15, 23, 18, 28, 32, 25, 30];
            break;
        case 'weekly':
            chartLabels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
            chartData = [120, 145, 98, 165];
            break;
    }

    invoiceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartLabels,
            datasets: [{
                label: 'Invoices Generated',
                data: chartData,
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(0, 123, 255, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: period === 'daily' ? 5 : 20
                    }
                }
            }
        }
    });
}

// Initialize charts on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeInvoiceCharts();
});
</script>

{% endblock %}
