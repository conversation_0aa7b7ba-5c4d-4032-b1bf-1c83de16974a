{% extends 'base.html' %}

{% block title %}Payment Processing{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Payment Processing</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('finance') }}" method="GET" class="form-inline">
                                <input type="hidden" name="view" value="payments">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="invoice_number" id="searchInput"
                                           placeholder="Search by Invoice/Order/Customer..."
                                           value="{{ search_query or request.args.get('invoice_number', '') }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="clearSearch()">
                                            <i class="fas fa-times"></i> Clear
                                        </button>
                                    </div>
                                </div>
                                <small class="form-text text-muted ml-2">Search by invoice number, order ID, or customer name</small>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Quick Links</h6>
                                </div>
                                <div class="card-body p-2">
                                    <div class="d-flex flex-wrap">
                                        <a href="{{ url_for('finance') }}?view=receivables" class="btn btn-sm btn-outline-primary m-1">
                                            <i class="fas fa-file-invoice-dollar"></i> Receivables
                                        </a>
                                        <a href="{{ url_for('finance') }}?view=tax" class="btn btn-sm btn-outline-secondary m-1">
                                            <i class="fas fa-chart-bar"></i> Tax Reports
                                        </a>
                                        <a href="{{ url_for('finance') }}?view=ledger" class="btn btn-sm btn-outline-info m-1">
                                            <i class="fas fa-book"></i> Customer Ledger
                                        </a>
                                        <a href="{{ url_for('finance_reports') }}" class="btn btn-sm btn-outline-primary m-1">
                                            <i class="fas fa-chart-line"></i> Advanced Reports
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if request.args.get('invoice_id') %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">Record Payment</h6>
                                </div>
                                <div class="card-body">
                                    <form action="{{ url_for('record_payment') }}" method="POST">
                                        <input type="hidden" name="invoice_id" value="{{ request.args.get('invoice_id') }}">

                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="payment_date">Payment Date</label>
                                                    <input type="date" class="form-control" id="payment_date" name="payment_date" value="{{ now.strftime('%Y-%m-%d') }}" required>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="payment_method">Payment Method</label>
                                                    <select class="form-control" id="payment_method" name="payment_method" required>
                                                        <option value="Cash">Cash</option>
                                                        <option value="Cheque">Cheque</option>
                                                        <option value="Bank Transfer">Bank Transfer</option>
                                                        <option value="Online">Online</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="amount">Amount</label>
                                                    <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="reference_number">Reference Number (optional)</label>
                                                    <input type="text" class="form-control" id="reference_number" name="reference_number">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="notes">Notes (optional)</label>
                                                    <input type="text" class="form-control" id="notes" name="notes">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-right">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-save"></i> Record Payment
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>Recent Unpaid Invoices</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Customer</th>
                                            <th>Date Range</th>
                                            <th>Entries</th>
                                            <th>Total Amount</th>
                                            <th>Paid</th>
                                            <th>Outstanding</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if recent_invoices %}
                                            {% for invoice in recent_invoices %}
                                            <tr>
                                                <td><strong>{{ invoice.invoice_number }}</strong></td>
                                                <td>{{ invoice.customer_name }}</td>
                                                <td>
                                                    {% if invoice.earliest_date == invoice.latest_date %}
                                                        {{ invoice.earliest_date|format_datetime if invoice.earliest_date else 'N/A' }}
                                                    {% else %}
                                                        {{ invoice.earliest_date|format_datetime if invoice.earliest_date else 'N/A' }} to
                                                        {{ invoice.latest_date|format_datetime if invoice.latest_date else 'N/A' }}
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge badge-info">{{ invoice.entry_count }} entries</span>
                                                </td>
                                                <td>{{ invoice.total_amount|format_currency }}</td>
                                                <td class="text-success">{{ invoice.paid_amount|format_currency }}</td>
                                                <td class="text-danger">{{ invoice.outstanding_amount|format_currency }}</td>
                                                <td>
                                                    <span class="badge
                                                        {% if invoice.payment_status == 'Paid' %}badge-success
                                                        {% elif invoice.payment_status == 'Partial' %}badge-warning
                                                        {% else %}badge-danger{% endif %}">
                                                        {{ invoice.payment_status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="{{ url_for('finance_invoice_details', invoice_number=invoice.invoice_number) }}" class="btn btn-sm btn-primary mr-1">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                    {% if invoice.outstanding_amount > 0 %}
                                                    <button class="btn btn-sm btn-success" onclick="knockOffPayment('{{ invoice.invoice_number }}', {{ invoice.outstanding_amount }})">
                                                        <i class="fas fa-check-circle"></i> Knock Off
                                                    </button>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="9" class="text-center">No unpaid invoices found</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Unpaid Invoices Section -->
                    {% if unpaid_invoices %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-exclamation-triangle"></i> Unpaid Invoices - Awaiting Payment
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Invoice #</th>
                                                    <th>Order ID</th>
                                                    <th>Customer</th>
                                                    <th>Date</th>
                                                    <th>Total Amount</th>
                                                    <th>Outstanding</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for invoice in unpaid_invoices %}
                                                <tr>
                                                    <td><strong>{{ invoice.invoice_number }}</strong></td>
                                                    <td>{{ invoice.order_id }}</td>
                                                    <td>{{ invoice.customer_name }}</td>
                                                    <td>{{ invoice.date_generated|format_datetime }}</td>
                                                    <td>{{ invoice.total_amount|format_currency }}</td>
                                                    <td>
                                                        <span class="badge badge-danger">
                                                            {{ invoice.outstanding_amount|format_currency }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="{{ url_for('finance_invoice_details', invoice_number=invoice.invoice_number) }}"
                                                           class="btn btn-sm btn-info mr-1">
                                                            <i class="fas fa-eye"></i> View Details
                                                        </a>
                                                        <a href="{{ url_for('finance') }}?view=payments&invoice_id={{ invoice.invoice_number }}"
                                                           class="btn btn-sm btn-success">
                                                            <i class="fas fa-money-bill"></i> Record Payment
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Payment History Section -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-history"></i> Payment History
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% if payments %}
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Payment ID</th>
                                                    <th>Invoice #</th>
                                                    <th>Order ID</th>
                                                    <th>Customer</th>
                                                    <th>Date</th>
                                                    <th>Method</th>
                                                    <th>Amount</th>
                                                    <th>Reference</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for payment in payments %}
                                                <tr>
                                                    <td><strong class="text-primary">{{ payment.payment_id }}</strong></td>
                                                    <td>
                                                        <a href="{{ url_for('finance_invoice_details', invoice_number=payment.invoice_number) }}"
                                                           class="text-primary font-weight-bold">
                                                            {{ payment.invoice_number }}
                                                        </a>
                                                    </td>
                                                    <td>{{ payment.order_id }}</td>
                                                    <td>{{ payment.customer_name }}</td>
                                                    <td>{{ payment.payment_date|format_datetime }}</td>
                                                    <td>
                                                        <span class="badge badge-{% if payment.payment_method == 'Cash' %}success{% elif payment.payment_method == 'Cheque' %}warning{% else %}info{% endif %}">
                                                            {{ payment.payment_method }}
                                                        </span>
                                                    </td>
                                                    <td>{{ payment.amount|format_currency }}</td>
                                                    <td>{{ payment.reference_number or 'N/A' }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-info" data-toggle="popover" data-content="{{ payment.notes or 'No notes available' }}">
                                                            <i class="fas fa-sticky-note"></i> Notes
                                                        </button>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info text-center">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>No payment records found.</strong><br>
                                        Payments will appear here once they are recorded for invoices.
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(function() {
        // Initialize popovers
        $('[data-toggle="popover"]').popover({
            trigger: 'hover',
            placement: 'top'
        });

        // Enhanced search functionality
        $('#searchInput').on('keyup', function(e) {
            if (e.keyCode === 13) { // Enter key
                $(this).closest('form').submit();
            }
        });

        // Auto-focus search input if there's a search query
        {% if search_query %}
        $('#searchInput').focus();
        {% endif %}
    });

    function clearSearch() {
        $('#searchInput').val('');
        window.location.href = "{{ url_for('finance', view='payments') }}";
    }

    // Function to record payment for specific invoice
    function recordPayment(invoiceNumber) {
        window.location.href = "{{ url_for('finance', view='payments') }}&invoice_id=" + invoiceNumber;
    }

    // Function to view invoice details
    function viewInvoiceDetails(invoiceNumber) {
        window.location.href = "{{ url_for('finance_invoice_details', invoice_number='') }}" + invoiceNumber;
    }

    // Function to knock off payment (mark as fully paid)
    function knockOffPayment(invoiceNumber, outstandingAmount) {
        if (confirm(`Are you sure you want to knock off the outstanding amount of ₨${outstandingAmount.toLocaleString()} for Invoice ${invoiceNumber}?\n\nThis will mark the invoice as fully paid.`)) {
            // Create a form and submit it
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = "{{ url_for('knock_off_payment') }}";

            var invoiceInput = document.createElement('input');
            invoiceInput.type = 'hidden';
            invoiceInput.name = 'invoice_id';
            invoiceInput.value = invoiceNumber;
            form.appendChild(invoiceInput);

            var amountInput = document.createElement('input');
            amountInput.type = 'hidden';
            amountInput.name = 'amount';
            amountInput.value = outstandingAmount;
            form.appendChild(amountInput);

            var methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = 'payment_method';
            methodInput.value = 'Knock Off';
            form.appendChild(methodInput);

            var notesInput = document.createElement('input');
            notesInput.type = 'hidden';
            notesInput.name = 'notes';
            notesInput.value = 'Payment knocked off - marked as fully paid';
            form.appendChild(notesInput);

            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}