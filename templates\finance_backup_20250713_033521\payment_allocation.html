{% extends "base.html" %}

{% block title %}Payment Allocation - Finance{% endblock %}

{% block head %}
<style>
.allocation-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s;
}
.allocation-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}
.payment-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}
.invoice-card {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}
.allocation-amount {
    font-size: 1.2em;
    font-weight: bold;
}
.drag-drop-area {
    min-height: 200px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
}
.drag-drop-area.drag-over {
    border-color: #007bff;
    background-color: #f8f9fa;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h2 class="mb-0">🔄 Payment Allocation & Knock-off</h2>
                    <p class="mb-0">Professional payment allocation system for invoice settlement</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Allocation Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h4>{{ unallocated_payments|length }}</h4>
                    <p class="mb-0">Unallocated Payments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4>{{ outstanding_invoices|length }}</h4>
                    <p class="mb-0">Outstanding Invoices</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 id="totalUnallocatedAmount">₹0.00</h4>
                    <p class="mb-0">Total Unallocated</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4 id="totalOutstandingAmount">₹0.00</h4>
                    <p class="mb-0">Total Outstanding</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Allocation Tools -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">⚡ Quick Allocation Tools</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success btn-block" onclick="autoAllocateAll()">
                                <i class="fas fa-magic"></i> Auto Allocate All
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-primary btn-block" onclick="allocateByCustomer()">
                                <i class="fas fa-user"></i> Allocate by Customer
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-info btn-block" onclick="showBulkAllocationModal()">
                                <i class="fas fa-list"></i> Bulk Allocation
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-warning btn-block" onclick="showPartialAllocationModal()">
                                <i class="fas fa-percentage"></i> Partial Allocation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Allocation Workspace -->
    <div class="row">
        <!-- Unallocated Payments -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">💰 Unallocated Payments</h5>
                    <small>Drag payments to invoices for allocation</small>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    {% for payment in unallocated_payments %}
                    <div class="allocation-card payment-card mb-3 p-3" 
                         draggable="true" 
                         data-payment-id="{{ payment.payment_id }}"
                         data-customer-id="{{ payment.customer_id }}"
                         data-amount="{{ payment.unallocated_amount }}"
                         ondragstart="dragPayment(event)">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-1">{{ payment.payment_id }}</h6>
                                <p class="mb-1">{{ payment.customer_name }}</p>
                                <small>{{ payment.payment_date|format_date }} | {{ payment.payment_method|title }}</small>
                                {% if payment.reference_number %}
                                <br><small>Ref: {{ payment.reference_number }}</small>
                                {% endif %}
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="allocation-amount">₹{{ "{:,.2f}".format(payment.unallocated_amount) }}</div>
                                <small>Unallocated</small>
                                {% if payment.allocated_amount > 0 %}
                                <br><small>Allocated: ₹{{ "{:,.2f}".format(payment.allocated_amount) }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-light" 
                                    onclick="quickAllocatePayment('{{ payment.payment_id }}')">
                                <i class="fas fa-bolt"></i> Quick Allocate
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" 
                                    onclick="viewPaymentDetails('{{ payment.payment_id }}')">
                                <i class="fas fa-eye"></i> Details
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not unallocated_payments %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>All Payments Allocated!</h5>
                        <p>No unallocated payments found.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Outstanding Invoices -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">📄 Outstanding Invoices</h5>
                    <small>Drop payments here to allocate</small>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    {% for invoice in outstanding_invoices %}
                    <div class="allocation-card invoice-card mb-3 p-3" 
                         data-invoice-id="{{ invoice.invoice_id }}"
                         data-customer-id="{{ invoice.customer_id }}"
                         data-amount="{{ invoice.outstanding_amount }}"
                         ondrop="dropPayment(event)" 
                         ondragover="allowDrop(event)">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-1">{{ invoice.invoice_number }}</h6>
                                <p class="mb-1">{{ invoice.customer_name }}</p>
                                <small>{{ invoice.invoice_date|format_date }} | Due: {{ invoice.due_date|format_date if invoice.due_date else 'N/A' }}</small>
                                {% if invoice.order_id %}
                                <br><small>Order: {{ invoice.order_id }}</small>
                                {% endif %}
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="allocation-amount">₹{{ "{:,.2f}".format(invoice.outstanding_amount) }}</div>
                                <small>Outstanding</small>
                                <br><small>Total: ₹{{ "{:,.2f}".format(invoice.total_amount) }}</small>
                            </div>
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-light" 
                                    onclick="findMatchingPayments('{{ invoice.invoice_id }}')">
                                <i class="fas fa-search"></i> Find Payments
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" 
                                    onclick="viewInvoiceDetails('{{ invoice.invoice_id }}')">
                                <i class="fas fa-eye"></i> Details
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not outstanding_invoices %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>All Invoices Paid!</h5>
                        <p>No outstanding invoices found.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Allocations -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📋 Recent Allocations</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Payment ID</th>
                                    <th>Invoice</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="recentAllocationsTable">
                                <!-- Recent allocations will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Allocation Confirmation Modal -->
<div class="modal fade" id="allocationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h4 class="modal-title">🔄 Confirm Payment Allocation</h4>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="allocationDetails">
                    <!-- Allocation details will be populated here -->
                </div>
                <div class="form-group">
                    <label>Allocation Amount</label>
                    <input type="number" step="0.01" class="form-control" id="allocationAmount">
                    <small class="form-text text-muted">Enter amount to allocate (partial allocation allowed)</small>
                </div>
                <div class="form-group">
                    <label>Allocation Type</label>
                    <select class="form-control" id="allocationType">
                        <option value="manual">Manual Allocation</option>
                        <option value="auto">Auto Allocation</option>
                        <option value="partial">Partial Allocation</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmAllocation()">
                    <i class="fas fa-check"></i> Confirm Allocation
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentAllocation = {};

function dragPayment(event) {
    const paymentData = {
        paymentId: event.target.dataset.paymentId,
        customerId: event.target.dataset.customerId,
        amount: parseFloat(event.target.dataset.amount)
    };
    event.dataTransfer.setData("text/plain", JSON.stringify(paymentData));
}

function allowDrop(event) {
    event.preventDefault();
    event.target.closest('.allocation-card').classList.add('drag-over');
}

function dropPayment(event) {
    event.preventDefault();
    event.target.closest('.allocation-card').classList.remove('drag-over');
    
    const paymentData = JSON.parse(event.dataTransfer.getData("text/plain"));
    const invoiceCard = event.target.closest('.allocation-card');
    const invoiceId = invoiceCard.dataset.invoiceId;
    const invoiceCustomerId = invoiceCard.dataset.customerId;
    const invoiceAmount = parseFloat(invoiceCard.dataset.amount);
    
    // Check if customer matches
    if (paymentData.customerId !== invoiceCustomerId) {
        alert('Payment and invoice must be for the same customer!');
        return;
    }
    
    // Prepare allocation
    currentAllocation = {
        paymentId: paymentData.paymentId,
        invoiceId: invoiceId,
        maxAmount: Math.min(paymentData.amount, invoiceAmount),
        paymentAmount: paymentData.amount,
        invoiceAmount: invoiceAmount
    };
    
    // Show allocation modal
    showAllocationModal();
}

function showAllocationModal() {
    document.getElementById('allocationAmount').value = currentAllocation.maxAmount;
    document.getElementById('allocationDetails').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Payment Details</h6>
                <p><strong>Payment ID:</strong> ${currentAllocation.paymentId}</p>
                <p><strong>Available:</strong> ₹${currentAllocation.paymentAmount.toFixed(2)}</p>
            </div>
            <div class="col-md-6">
                <h6>Invoice Details</h6>
                <p><strong>Invoice ID:</strong> ${currentAllocation.invoiceId}</p>
                <p><strong>Outstanding:</strong> ₹${currentAllocation.invoiceAmount.toFixed(2)}</p>
            </div>
        </div>
        <div class="alert alert-info">
            <strong>Maximum Allocation:</strong> ₹${currentAllocation.maxAmount.toFixed(2)}
        </div>
    `;
    $('#allocationModal').modal('show');
}

function confirmAllocation() {
    const amount = parseFloat(document.getElementById('allocationAmount').value);
    const type = document.getElementById('allocationType').value;
    
    if (amount <= 0 || amount > currentAllocation.maxAmount) {
        alert('Invalid allocation amount!');
        return;
    }
    
    // Submit allocation
    const formData = new FormData();
    formData.append('payment_id', currentAllocation.paymentId);
    formData.append('invoice_id', currentAllocation.invoiceId);
    formData.append('allocation_amount', amount);
    formData.append('allocation_type', type);
    
    fetch('/finance/allocate_payment', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error allocating payment');
    });
    
    $('#allocationModal').modal('hide');
}

function quickAllocatePayment(paymentId) {
    // Implementation for quick allocation
    console.log('Quick allocating payment:', paymentId);
}

function autoAllocateAll() {
    if (confirm('Auto-allocate all unallocated payments to oldest outstanding invoices?')) {
        // Implementation for auto allocation
        console.log('Auto-allocating all payments...');
    }
}

function allocateByCustomer() {
    // Implementation for customer-wise allocation
    console.log('Allocating by customer...');
}

function showBulkAllocationModal() {
    // Implementation for bulk allocation
    console.log('Showing bulk allocation modal...');
}

function showPartialAllocationModal() {
    // Implementation for partial allocation
    console.log('Showing partial allocation modal...');
}

// Calculate totals on page load
document.addEventListener('DOMContentLoaded', function() {
    let totalUnallocated = 0;
    let totalOutstanding = 0;
    
    document.querySelectorAll('.payment-card').forEach(card => {
        totalUnallocated += parseFloat(card.dataset.amount || 0);
    });
    
    document.querySelectorAll('.invoice-card').forEach(card => {
        totalOutstanding += parseFloat(card.dataset.amount || 0);
    });
    
    document.getElementById('totalUnallocatedAmount').textContent = `₹${totalUnallocated.toFixed(2)}`;
    document.getElementById('totalOutstandingAmount').textContent = `₹${totalOutstanding.toFixed(2)}`;
});
</script>
{% endblock %}
