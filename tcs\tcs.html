<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCS Express Tracking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .shadow-card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        #tcsFrame {
            height: 80vh;
            width: 100%;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4">
        <h1 class="text-3xl font-bold text-center mb-8 text-[#f0575d]">TCS Express Tracking</h1>
        
        <!-- Tracking Input Form -->
        <div class="flex justify-center mb-8">
            <div class="flex">
                <input 
                    type="number" 
                    id="trackingNumber" 
                    placeholder="Enter TCS Tracking Number" 
                    class="p-3 md:w-[27rem] sm:w-[20rem] w-[10rem] focus:outline-none opacity-75 rounded-l-sm border"
                    value="31442083394"
                >
                <button 
                    onclick="loadTCSTracking()"
                    class="bg-[#f0575d] text-white px-6 py-3 rounded-r-sm hover:bg-[#d84a50] transition"
                >
                    Track Shipment
                </button>
            </div>
        </div>
        
        <!-- TCS Website Embed -->
        <div id="tcsContainer" class="hidden">
            <iframe 
                id="tcsFrame"
                src=""
                title="TCS Tracking"
                allow="fullscreen"
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
            ></iframe>
            <p class="text-sm text-gray-500 mt-2">
                Note: This displays the official TCS tracking page. Some features may be limited in the embedded view.
                <a href="#" id="openInNewTab" class="text-[#f0575d] underline">Open in new tab</a> for full functionality.
            </p>
        </div>
    </div>

    <script>
        function loadTCSTracking() {
            const trackingNumber = document.getElementById('trackingNumber').value.trim();
            const tcsContainer = document.getElementById('tcsContainer');
            const tcsFrame = document.getElementById('tcsFrame');
            const openInNewTab = document.getElementById('openInNewTab');
            
            if (!trackingNumber) {
                alert('Please enter a tracking number');
                return;
            }
            
            // Construct the TCS tracking URL
            const tcsUrl = `https://www.tcsexpress.com/track/${trackingNumber}`;
            
            // Load in iframe
            tcsFrame.src = tcsUrl;
            tcsContainer.classList.remove('hidden');
            
            // Set up new tab link
            openInNewTab.href = tcsUrl;
            openInNewTab.onclick = function(e) {
                e.preventDefault();
                window.open(tcsUrl, '_blank');
            };
        }
        
        // Load default tracking on page load
        window.onload = function() {
            const defaultTracking = document.getElementById('trackingNumber').value;
            if (defaultTracking) {
                loadTCSTracking();
            }
        };
    </script>
</body>
</html>