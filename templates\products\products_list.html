{% extends "base.html" %}

{% block title %}Products Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-pills"></i> Products Management
                            </h4>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('new_product') }}" class="btn btn-light">
                                <i class="fas fa-plus"></i> Add Product
                            </a>
                            <a href="{{ url_for('inventory') }}" class="btn btn-info">
                                <i class="fas fa-warehouse"></i> Inventory
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ url_for('products') }}" class="form-inline">
                                <div class="input-group" style="width: 100%;">
                                    <input type="text" name="q" class="form-control" 
                                           placeholder="Search products by name, generic name, category..." 
                                           value="{{ search_query }}" id="search-input">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-outline-success">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        {% if search_query %}
                                        <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{{ url_for('products') }}">All Products</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Tablets">Tablets</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Capsules">Capsules</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Syrup">Syrups</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Injections">Injections</a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?status=active">Active Only</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?status=inactive">Inactive Only</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|length }}</h5>
                                    <small>Total Products</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|selectattr('is_active', 'equalto', True)|list|length if products else 0 }}</h5>
                                    <small>Active Products</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|selectattr('type', 'equalto', 'Tablets')|list|length if products else 0 }}</h5>
                                    <small>Tablets</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|selectattr('type', 'equalto', 'Capsules')|list|length if products else 0 }}</h5>
                                    <small>Capsules</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="products-table">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product ID</th>
                                    <th>Product Name</th>
                                    <th>Generic Name</th>
                                    <th>Type</th>
                                    <th>Strength</th>
                                    <th>Category</th>
                                    <th>Cost Price</th>
                                    <th>Selling Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td><strong>{{ product.product_id }}</strong></td>
                                    <td>
                                        <div>
                                            <strong>{{ product.name }}</strong>
                                            {% if product.manufacturer %}
                                            <br><small class="text-muted">{{ product.manufacturer }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>{{ product.generic_name or 'N/A' }}</td>
                                    <td>
                                        <span class="badge badge-{% if product.type == 'Tablets' %}primary{% elif product.type == 'Capsules' %}info{% elif product.type == 'Syrup' %}warning{% else %}secondary{% endif %}">
                                            {{ product.type }}
                                        </span>
                                    </td>
                                    <td>{{ product.strength or 'N/A' }}</td>
                                    <td>{{ product.category or 'General' }}</td>
                                    <td>₹{{ product.cost_price | format_currency }}</td>
                                    <td>₹{{ product.selling_price | format_currency }}</td>
                                    <td>
                                        {% if product.total_stock is defined %}
                                            {% if product.total_stock <= (product.reorder_level or 0) %}
                                            <span class="badge badge-danger">{{ product.total_stock }}</span>
                                            {% elif product.total_stock <= (product.reorder_level or 0) * 2 %}
                                            <span class="badge badge-warning">{{ product.total_stock }}</span>
                                            {% else %}
                                            <span class="badge badge-success">{{ product.total_stock }}</span>
                                            {% endif %}
                                        {% else %}
                                        <span class="badge badge-secondary">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.is_active == False %}
                                        <span class="badge badge-danger">Inactive</span>
                                        {% else %}
                                        <span class="badge badge-success">Active</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-toggle="dropdown">
                                                Actions
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="{{ url_for('products') }}/{{ product.product_id }}">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('update_product', product_id=product.product_id) }}">
                                                    <i class="fas fa-edit"></i> Edit Product
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="{{ url_for('inventory') }}?product={{ product.product_id }}">
                                                    <i class="fas fa-warehouse"></i> View Inventory
                                                </a>
                                                <a class="dropdown-item" href="{{ url_for('inventory_add') }}?product={{ product.product_id }}">
                                                    <i class="fas fa-plus"></i> Add Stock
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="{{ url_for('reports_dashboard') }}?type=product&id={{ product.product_id }}">
                                                    <i class="fas fa-chart-line"></i> Sales Report
                                                </a>
                                                {% if product.is_active != False %}
                                                <a class="dropdown-item text-warning" href="#" onclick="toggleProductStatus('{{ product.product_id }}', false)">
                                                    <i class="fas fa-pause"></i> Deactivate
                                                </a>
                                                {% else %}
                                                <a class="dropdown-item text-success" href="#" onclick="toggleProductStatus('{{ product.product_id }}', true)">
                                                    <i class="fas fa-play"></i> Activate
                                                </a>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <input type="checkbox" class="form-check-input ml-2" data-product-id="{{ product.product_id }}">
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="11" class="text-center text-muted">
                                        <i class="fas fa-pills fa-3x mb-3"></i>
                                        <br>No products found
                                        {% if search_query %}
                                        for "{{ search_query }}"
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label" for="select-all">
                                    Select All
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="bulkAction('export')">
                                    <i class="fas fa-download"></i> Export Selected
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="bulkAction('update-prices')">
                                    <i class="fas fa-tag"></i> Update Prices
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="bulkAction('reorder-levels')">
                                    <i class="fas fa-level-up-alt"></i> Set Reorder Levels
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make table sortable and searchable
    $('#products-table').DataTable({
        "pageLength": 25,
        "order": [[ 1, "asc" ]], // Sort by product name
        "columnDefs": [
            { "orderable": false, "targets": 10 } // Disable sorting on Actions column
        ]
    });

    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][data-product-id]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function bulkAction(action) {
    const selectedProducts = [];
    const checkboxes = document.querySelectorAll('input[type="checkbox"][data-product-id]:checked');

    checkboxes.forEach(checkbox => {
        selectedProducts.push(checkbox.dataset.productId);
    });

    if (selectedProducts.length === 0) {
        alert('Please select at least one product.');
        return;
    }

    switch(action) {
        case 'export':
            window.location.href = `/products/export?ids=${selectedProducts.join(',')}`;
            break;
        case 'update-prices':
            // Open modal for bulk price update
            $('#bulk-price-modal').modal('show');
            break;
        case 'reorder-levels':
            // Open modal for bulk reorder level update
            $('#bulk-reorder-modal').modal('show');
            break;
    }
}

function toggleProductStatus(productId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    const message = activate ? 'Activate this product?' : 'Deactivate this product?';

    if (confirm(message)) {
        fetch(`/products/${productId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ activate: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating product status');
        });
    }
}
</script>
{% endblock %}
