{% extends 'base.html' %}

{% block title %}🚀 Ultra Modern Finance Dashboard - Medivent ERP 2025{% endblock %}

{% block content %}
<style>
/* 🎨 ULTRA MODERN 2025 FINANCE DASHBOARD STYLES */
.ultra-modern-finance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
    position: relative;
    overflow-x: hidden;
}

.ultra-modern-finance::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.finance-hero-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 50px;
    margin-bottom: 40px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.finance-hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #4e73df, #1cc88a, #f6c23e, #e74a3b);
}

.finance-hero-title {
    background: linear-gradient(45deg, #4e73df, #1cc88a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 3.5rem;
    margin: 0;
    text-align: center;
}

.finance-hero-subtitle {
    text-align: center;
    color: #5a5c69;
    font-size: 1.2rem;
    margin-top: 10px;
    font-weight: 500;
}

.ultra-stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 35px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.ultra-stat-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 40px 80px rgba(0, 0, 0, 0.25);
}

.ultra-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
}

.ultra-stat-card.revenue {
    --card-color: #1cc88a;
    --card-color-light: #17a673;
}

.ultra-stat-card.outstanding {
    --card-color: #e74a3b;
    --card-color-light: #c0392b;
}

.ultra-stat-card.pending {
    --card-color: #f6c23e;
    --card-color-light: #dda20a;
}

.ultra-stat-card.collections {
    --card-color: #36b9cc;
    --card-color-light: #258391;
}

.stat-number-ultra {
    font-size: 3.5rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--card-color), var(--card-color-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    line-height: 1;
}

.stat-label-ultra {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 10px;
}

.stat-growth {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 20px;
    display: inline-block;
}

.stat-growth.positive {
    background: rgba(28, 200, 138, 0.1);
    color: #1cc88a;
}

.stat-growth.negative {
    background: rgba(231, 74, 59, 0.1);
    color: #e74a3b;
}

.ultra-chart-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 35px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ultra-nav-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    color: inherit;
}

.ultra-nav-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    color: inherit;
}

.nav-icon-ultra {
    font-size: 3.5rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #4e73df, #1cc88a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-title-ultra {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.nav-desc-ultra {
    color: #7f8c8d;
    font-size: 0.95rem;
    margin: 0;
}

.alert-ultra {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border: none;
    border-left: 5px solid var(--alert-color);
}

.alert-ultra.danger {
    --alert-color: #e74a3b;
}

.alert-ultra.warning {
    --alert-color: #f6c23e;
}

.alert-ultra.success {
    --alert-color: #1cc88a;
}

.customer-row-ultra {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.customer-row-ultra:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .finance-hero-title {
        font-size: 2.5rem;
    }
    .stat-number-ultra {
        font-size: 2.5rem;
    }
    .nav-icon-ultra {
        font-size: 2.5rem;
    }
}

/* 🎯 AGING ANALYSIS COLORS */
.aging-current { color: #1cc88a; font-weight: 600; }
.aging-30 { color: #f6c23e; font-weight: 600; }
.aging-60 { color: #fd7e14; font-weight: 600; }
.aging-90 { color: #e74a3b; font-weight: 600; }

/* ✨ FLOATING ANIMATION */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating {
    animation: float 3s ease-in-out infinite;
}
</style>

<div class="ultra-modern-finance">
    <div class="container-fluid">
        <!-- 🎯 HERO SECTION -->
        <div class="finance-hero-card floating">
            <h1 class="finance-hero-title">
                <i class="fas fa-chart-line mr-3"></i>Finance Command Center
            </h1>
            <p class="finance-hero-subtitle">
                Real-time financial analytics and comprehensive business intelligence
            </p>
            <div class="text-center mt-4">
                <span class="badge badge-primary badge-lg px-4 py-2">
                    <i class="fas fa-clock mr-2"></i>Last Updated: {{ now.strftime('%d %b %Y, %I:%M %p') }}
                </span>
            </div>
        </div>

        <!-- 🚨 CRITICAL ALERTS -->
        {% if critical_alerts %}
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-white mb-3">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Critical Alerts
                </h4>
                {% for alert in critical_alerts %}
                <div class="alert-ultra {{ alert.type }}">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="{{ alert.icon }} fa-2x mr-3"></i>
                            <div>
                                <h6 class="mb-1 font-weight-bold">{{ alert.title }}</h6>
                                <p class="mb-0">{{ alert.message }}</p>
                            </div>
                        </div>
                        <a href="{{ alert.action_url }}" class="btn btn-outline-dark btn-sm">
                            <i class="fas fa-arrow-right"></i> Action
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 📊 ULTRA MODERN STATS -->
        <div class="row mb-5">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="ultra-stat-card revenue">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-ultra">Rs. {{ "{:,.0f}"|format(current_month_revenue) }}</div>
                            <div class="stat-label-ultra">
                                <i class="fas fa-chart-line mr-2"></i>Monthly Revenue
                            </div>
                            {% if revenue_growth != 0 %}
                            <div class="stat-growth {{ 'positive' if revenue_growth > 0 else 'negative' }}">
                                <i class="fas fa-arrow-{{ 'up' if revenue_growth > 0 else 'down' }} mr-1"></i>
                                {{ "{:.1f}"|format(revenue_growth|abs) }}%
                            </div>
                            {% endif %}
                        </div>
                        <div class="text-success">
                            <i class="fas fa-dollar-sign fa-4x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="ultra-stat-card outstanding">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-ultra">Rs. {{ "{:,.0f}"|format(outstanding_data.total_outstanding) }}</div>
                            <div class="stat-label-ultra">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Total Outstanding
                            </div>
                            <div class="small text-muted">
                                <span class="aging-current">Current: Rs. {{ "{:,.0f}"|format(outstanding_data.current_30) }}</span><br>
                                <span class="aging-90">90+ Days: Rs. {{ "{:,.0f}"|format(outstanding_data.over_90_days) }}</span>
                            </div>
                        </div>
                        <div class="text-danger">
                            <i class="fas fa-balance-scale fa-4x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="ultra-stat-card pending">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-ultra">{{ pending_invoices_count }}</div>
                            <div class="stat-label-ultra">
                                <i class="fas fa-file-invoice-dollar mr-2"></i>Pending Invoices
                            </div>
                            <div class="small text-muted">Awaiting processing</div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-4x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="ultra-stat-card collections">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-ultra">Rs. {{ "{:,.0f}"|format(todays_collections) }}</div>
                            <div class="stat-label-ultra">
                                <i class="fas fa-hand-holding-usd mr-2"></i>Today's Collections
                            </div>
                            <div class="small text-muted">Real-time updates</div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-money-bill-wave fa-4x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 🎯 NAVIGATION CARDS -->
        <div class="row mb-5">
            <div class="col-12">
                <h4 class="text-white mb-4">
                    <i class="fas fa-rocket mr-2"></i>Finance Operations
                </h4>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="/finance/pending-invoices" class="ultra-nav-card">
                    <div class="nav-icon-ultra">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="nav-title-ultra">Pending Invoices</div>
                    <p class="nav-desc-ultra">Process and manage pending invoices with advanced filtering</p>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="/finance/customer-ledger" class="ultra-nav-card">
                    <div class="nav-icon-ultra">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="nav-title-ultra">Customer Ledger</div>
                    <p class="nav-desc-ultra">Comprehensive customer aging and payment analysis</p>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="/finance/payment-collection" class="ultra-nav-card">
                    <div class="nav-icon-ultra">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                    <div class="nav-title-ultra">Payment Collection</div>
                    <p class="nav-desc-ultra">Record payments and manage collection activities</p>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <a href="/finance/financial-reports" class="ultra-nav-card">
                    <div class="nav-icon-ultra">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="nav-title-ultra">Financial Reports</div>
                    <p class="nav-desc-ultra">Advanced analytics and comprehensive reporting</p>
                </a>
            </div>
        </div>

        <!-- 🏆 TOP CUSTOMERS -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="ultra-chart-card">
                    <h5 class="font-weight-bold text-primary mb-4">
                        <i class="fas fa-crown mr-2"></i>Top Customers by Revenue
                    </h5>
                    {% for customer in top_customers[:5] %}
                    <div class="customer-row-ultra">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="font-weight-bold text-primary mb-1">{{ customer.name }}</h6>
                                <small class="text-muted">ID: {{ customer.customer_id }}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="font-weight-bold text-success">Rs. {{ "{:,.0f}"|format(customer.total_revenue) }}</div>
                                <small class="text-muted">Total Revenue</small>
                            </div>
                            <div class="col-md-2">
                                <div class="font-weight-bold">{{ customer.total_orders }}</div>
                                <small class="text-muted">Orders</small>
                            </div>
                            <div class="col-md-3">
                                <div class="font-weight-bold {{ 'text-danger' if customer.outstanding > 0 else 'text-success' }}">
                                    Rs. {{ "{:,.0f}"|format(customer.outstanding) }}
                                </div>
                                <small class="text-muted">Outstanding</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="col-lg-4 mb-4">
                <div class="ultra-chart-card">
                    <h5 class="font-weight-bold text-primary mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>Aging Analysis
                    </h5>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="aging-current">Current (0-30 days)</span>
                            <span class="aging-current">Rs. {{ "{:,.0f}"|format(outstanding_data.current_30) }}</span>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar bg-success" style="width: {{ (outstanding_data.current_30 / outstanding_data.total_outstanding * 100) if outstanding_data.total_outstanding > 0 else 0 }}%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="aging-30">31-60 days</span>
                            <span class="aging-30">Rs. {{ "{:,.0f}"|format(outstanding_data.days_31_60) }}</span>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar bg-warning" style="width: {{ (outstanding_data.days_31_60 / outstanding_data.total_outstanding * 100) if outstanding_data.total_outstanding > 0 else 0 }}%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="aging-60">61-90 days</span>
                            <span class="aging-60">Rs. {{ "{:,.0f}"|format(outstanding_data.days_61_90) }}</span>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar bg-orange" style="width: {{ (outstanding_data.days_61_90 / outstanding_data.total_outstanding * 100) if outstanding_data.total_outstanding > 0 else 0 }}%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="aging-90">90+ days</span>
                            <span class="aging-90">Rs. {{ "{:,.0f}"|format(outstanding_data.over_90_days) }}</span>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar bg-danger" style="width: {{ (outstanding_data.over_90_days / outstanding_data.total_outstanding * 100) if outstanding_data.total_outstanding > 0 else 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 🚀 ULTRA MODERN FINANCE DASHBOARD JAVASCRIPT
document.addEventListener('DOMContentLoaded', function() {
    // Add floating animation to cards
    const cards = document.querySelectorAll('.ultra-stat-card, .ultra-nav-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = 'all 0.6s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
    
    // Auto-refresh data every 30 seconds
    setInterval(refreshFinanceData, 30000);
});

function refreshFinanceData() {
    fetch('/api/finance-data')
        .then(response => response.json())
        .then(data => {
            console.log('Finance data refreshed:', data);
            // Update UI elements with new data
        })
        .catch(error => {
            console.log('Error refreshing finance data:', error);
        });
}

// 🎯 NOTIFICATION SYSTEM
function showFinanceNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; border-radius: 15px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} mr-2"></i>
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
