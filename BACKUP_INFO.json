{"backup_date": "2025-06-29T01:50:33.058279", "backup_name": "COMPREHENSIVE_ERP_BACKUP_20250629_015033", "source_directory": "C:\\Users\\<USER>\\Desktop\\ERP\\professional-workflow", "backup_type": "COMPREHENSIVE_ERP_BACKUP", "description": "Complete backup of Medivent Pharmaceuticals ERP System including all code, templates, static files, and database", "backed_up_items": [{"name": "app.py", "type": "file", "size_bytes": 777664, "status": "success"}, {"name": "schema.sql", "type": "file", "size_bytes": 15016, "status": "success"}, {"name": "requirements.txt", "type": "file", "size_bytes": 595, "status": "success"}, {"name": "erp_system.db", "type": "file", "size_bytes": 98304, "status": "success"}, {"name": "templates", "type": "directory", "files_count": 168, "size_bytes": 2362220, "status": "success"}, {"name": "static", "type": "directory", "files_count": 29, "size_bytes": 13860676, "status": "success"}, {"name": "routes", "type": "directory", "files_count": 13, "size_bytes": 155826, "status": "success"}, {"name": "utils", "type": "directory", "files_count": 18, "size_bytes": 131871, "status": "success"}, {"name": "instance", "type": "directory", "files_count": 3, "size_bytes": 48893952, "status": "success"}], "total_files": 235, "total_size_mb": 63.22, "status": "completed"}