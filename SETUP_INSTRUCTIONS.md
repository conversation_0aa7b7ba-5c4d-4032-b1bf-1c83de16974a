# Medivent ERP System - Setup Instructions

## Quick Start (Recommended)

### Option 1: Automated Setup (First Time Users)
1. **Double-click `setup_erp.bat`** - This will:
   - Verify Python installation
   - Install all dependencies automatically
   - Verify application integrity
   - Create necessary directories
   - Test the application
   - Optionally start the system

### Option 2: Quick Launch (After Setup)
1. **Double-click `start_erp.bat`** - This will:
   - Check dependencies
   - Start the application on port 3000
   - Open browser automatically

## System Requirements

### Prerequisites
- **Python 3.7 or higher** (Download from https://python.org)
- **Windows Operating System**
- **Internet connection** (for dependency installation)
- **4GB RAM minimum** (8GB recommended)
- **2GB free disk space**

### Supported Browsers
- Chrome (Recommended)
- Firefox
- Edge
- Safari

## Manual Setup (Advanced Users)

### Step 1: Install Python Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Verify Installation
```bash
python -c "import app; print('Success')"
```

### Step 3: Start Application
```bash
python run_app.py
```

## Application Access

### Default URLs
- **Local Access**: http://localhost:3000
- **Network Access**: http://[YOUR_IP]:3000

### Default Login Credentials
- **Username**: admin
- **Password**: admin123

## System Features

### Core Modules
- **Inventory Management**: Stock tracking, batch management, expiry monitoring
- **Order Processing**: Order creation, approval workflow, delivery challans
- **Financial Management**: Invoicing, payment tracking, financial reports
- **Customer Management**: Customer profiles, ledgers, credit management
- **Reporting & Analytics**: Comprehensive reports and dashboards
- **User Management**: Role-based access control

### Key Capabilities
- Real-time inventory tracking
- Automated order workflows
- Financial reporting and analytics
- Multi-warehouse support
- Batch and expiry management
- PDF report generation
- Excel export functionality
- Advanced search and filtering

## Troubleshooting

### Common Issues

#### 1. Python Not Found
**Error**: "Python is not installed or not in PATH"
**Solution**: 
- Install Python from https://python.org
- Ensure "Add Python to PATH" is checked during installation
- Restart command prompt after installation

#### 2. Port Already in Use
**Error**: "Address already in use"
**Solution**: 
- Close any existing instances of the application
- Check if another application is using port 3000
- The application will automatically try port 8080 as fallback

#### 3. Dependencies Installation Failed
**Error**: "Failed to install dependencies"
**Solution**: 
- Check internet connection
- Run command prompt as Administrator
- Try manual installation: `pip install Flask pandas matplotlib`

#### 4. Database Issues
**Error**: Database-related errors
**Solution**: 
- Ensure `instance` directory exists
- Check file permissions
- Database will be created automatically on first run

### Performance Optimization

#### For Better Performance
- Close unnecessary applications
- Ensure adequate RAM (8GB recommended)
- Use SSD storage if available
- Keep Python and dependencies updated

## File Structure

```
COMPREHENSIVE_ERP_BACKUP_20250629_015033/
├── app.py                 # Main application file
├── run_app.py            # Application launcher
├── requirements.txt      # Python dependencies
├── start_erp.bat        # Quick start script
├── setup_erp.bat        # First-time setup script
├── instance/            # Database and uploads
├── static/              # CSS, JS, images
├── templates/           # HTML templates
├── routes/              # Application routes
└── utils/               # Utility functions
```

## Security Notes

### Important Security Considerations
- Change default admin password after first login
- Configure proper user roles and permissions
- Regular database backups recommended
- Keep the system updated
- Use HTTPS in production environments

### Default Security Settings
- Session timeout: 24 hours
- CSRF protection enabled
- File upload restrictions in place
- SQL injection protection active

## Support and Maintenance

### Regular Maintenance
- **Database Backup**: Backup `instance/medivent.db` regularly
- **Log Monitoring**: Check `medivent.log` for errors
- **Updates**: Keep dependencies updated with `pip install -r requirements.txt --upgrade`

### Getting Help
- Check the troubleshooting section above
- Review application logs in `medivent.log`
- Ensure all prerequisites are met
- Contact system administrator for advanced issues

## Advanced Configuration

### Environment Variables (Optional)
- `FLASK_ENV=production` for production mode
- `SECRET_KEY=your_secret_key` for custom security key
- `DATABASE_URL=sqlite:///custom_path.db` for custom database location

### Custom Port Configuration
Edit `run_app.py` or `app.py` to change the default port from 3000 to your preferred port.

---

**Last Updated**: January 2025
**Version**: 1.0
**Compatibility**: Windows 10/11, Python 3.7+
