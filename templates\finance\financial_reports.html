{% extends "base.html" %}

{% block title %}Financial Reports - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .finance-dashboard {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .finance-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .finance-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .finance-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--secondary));
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 20px;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: var(--dark);
    }

    .stat-label {
        color: var(--muted);
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .stat-change {
        font-size: 0.8rem;
        font-weight: 600;
    }

    .chart-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .action-btn {
        background: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
    }

    .filter-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-chart-line me-3"></i>Financial Reports
                    </h1>
                    <p class="finance-subtitle">Comprehensive financial analytics and reporting</p>
                </div>
                <div class="action-buttons">
                    <button class="action-btn" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                    <button class="action-btn" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                    <button class="action-btn" onclick="generatePDF()">
                        <i class="fas fa-file-pdf me-2"></i>Generate PDF
                    </button>
                </div>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Report Filters
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label fw-bold">Start Date</label>
                    <input type="date" class="form-control" name="start_date"
                           value="{{ start_date }}" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">End Date</label>
                    <input type="date" class="form-control" name="end_date"
                           value="{{ end_date }}" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Report Type</label>
                    <select class="form-control" name="report_type">
                        <option value="summary">Summary Report</option>
                        <option value="detailed">Detailed Report</option>
                        <option value="aging">Aging Analysis</option>
                        <option value="customer">Customer-wise</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-chart-bar me-2"></i>Generate Report
                    </button>
                </div>
            </form>
            <div class="mt-3 p-3 bg-light rounded">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Report Period: <strong>{{ start_date or 'Not specified' }} to {{ end_date or 'Not specified' }}</strong>
                </small>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.total_revenue if summary and summary.total_revenue else 0) }}</div>
                    <div class="stat-label">Total Revenue</div>
                    <div class="stat-change text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        {{ revenue_data|length if revenue_data else 0 }} orders
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #007bff, #6610f2);">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.total_payments if summary and summary.total_payments else 0) }}</div>
                    <div class="stat-label">Total Payments</div>
                    <div class="stat-change text-info">
                        <i class="fas fa-info-circle me-1"></i>
                        {{ payment_data|length if payment_data else 0 }} transactions
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14, #e83e8c);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value">{{ summary.unique_customers if summary and summary.unique_customers else 0 }}</div>
                    <div class="stat-label">Active Customers</div>
                    <div class="stat-change text-warning">
                        <i class="fas fa-chart-line me-1"></i>
                        This period
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.avg_order_value if summary and summary.avg_order_value else 0) }}</div>
                    <div class="stat-label">Average Order Value</div>
                    <div class="stat-change text-secondary">
                        <i class="fas fa-percentage me-1"></i>
                        Per transaction
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Trend Chart -->
        <div class="chart-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2 text-primary"></i>Revenue Trend Analysis
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm active" onclick="showChart('revenue')">Revenue</button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="showChart('orders')">Orders</button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="showChart('customers')">Customers</button>
                </div>
            </div>
            <div style="position: relative; height: 400px;">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- Detailed Tables -->
        <div class="row">
            <!-- Revenue Details -->
            <div class="col-md-6 mb-4">
                <div class="chart-card h-100">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2 text-primary"></i>Daily Revenue Breakdown
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportTable('revenue')">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="revenueTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Orders</th>
                                    <th>Revenue</th>
                                    <th>Avg Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if revenue_data %}
                                    {% for row in revenue_data[:10] %}
                                    <tr>
                                        <td>
                                            <span class="fw-bold">{{ row.date }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary rounded-pill">{{ row.order_count }}</span>
                                        </td>
                                        <td class="text-success fw-bold">₹{{ "{:,.0f}".format(row.total_revenue) }}</td>
                                        <td class="text-muted">₹{{ "{:,.0f}".format(row.avg_order_value) }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>No revenue data available for this period
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Top Customers -->
            <div class="col-md-6 mb-4">
                <div class="chart-card h-100">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-crown me-2 text-warning"></i>Top Customers
                        </h5>
                        <button class="btn btn-outline-warning btn-sm" onclick="exportTable('customers')">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="customersTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Rank</th>
                                    <th>Customer</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if customer_data %}
                                    {% for customer in customer_data[:10] %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-warning text-dark rounded-pill">#{{ loop.index }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-gradient text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                                     style="background: linear-gradient(135deg, #007bff, #6610f2) !important;">
                                                    {{ customer.name[0].upper() if customer.name else 'N' }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ customer.name or 'Unknown Customer' }}</div>
                                                    <small class="text-muted">Customer ID: {{ customer.customer_id if customer.customer_id else 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info rounded-pill">{{ customer.order_count if customer.order_count else 0 }}</span>
                                        </td>
                                        <td class="text-success fw-bold">₹{{ "{:,.0f}".format(customer.total_spent if customer.total_spent else 0) }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>No customer data available for this period
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Analysis -->
        <div class="chart-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2 text-success"></i>Payment Analysis
                </h5>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-success btn-sm" onclick="exportTable('payments')">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="showPaymentChart()">
                        <i class="fas fa-chart-pie me-1"></i>Chart View
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover" id="paymentsTable">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Payment Method</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if payment_data %}
                            {% for payment in payment_data[:15] %}
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ payment.payment_date if payment.payment_date else 'N/A' }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary rounded-pill">{{ payment.payment_method if payment.payment_method else 'Cash' }}</span>
                                </td>
                                <td class="text-success fw-bold">₹{{ "{:,.0f}".format(payment.total_amount if payment.total_amount else 0) }}</td>
                                <td>
                                    <span class="badge bg-success rounded-pill">Completed</span>
                                </td>
                                <td>
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewPaymentDetails('{{ payment.id if payment.id else '' }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle me-2"></i>No payment data available for this period
                                    <br><small>Try adjusting the date range or check if payments have been recorded</small>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for Revenue Chart -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Global chart variable
let currentChart = null;

// Initialize Revenue Chart
function initializeChart() {
    const ctx = document.getElementById('revenueChart').getContext('2d');

    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [
                {% if revenue_data %}
                    {% for row in revenue_data %}
                        '{{ row.date }}'{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ],
            datasets: [{
                label: 'Daily Revenue',
                data: [
                    {% if revenue_data %}
                        {% for row in revenue_data %}
                            {{ row.total_revenue }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    {% endif %}
                ],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#28a745',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#28a745',
                    borderWidth: 1
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        }
    });
}

// Show different chart types
function showChart(type) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (currentChart) {
        currentChart.destroy();
    }

    const ctx = document.getElementById('revenueChart').getContext('2d');

    let chartData, chartColor, chartLabel;

    switch(type) {
        case 'revenue':
            chartData = [
                {% if revenue_data %}
                    {% for row in revenue_data %}
                        {{ row.total_revenue }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ];
            chartColor = '#28a745';
            chartLabel = 'Daily Revenue';
            break;
        case 'orders':
            chartData = [
                {% if revenue_data %}
                    {% for row in revenue_data %}
                        {{ row.order_count }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ];
            chartColor = '#007bff';
            chartLabel = 'Daily Orders';
            break;
        case 'customers':
            chartData = [
                {% if revenue_data %}
                    {% for row in revenue_data %}
                        {{ row.order_count }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ];
            chartColor = '#fd7e14';
            chartLabel = 'Daily Customers';
            break;
    }

    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [
                {% if revenue_data %}
                    {% for row in revenue_data %}
                        '{{ row.date }}'{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ],
            datasets: [{
                label: chartLabel,
                data: chartData,
                borderColor: chartColor,
                backgroundColor: chartColor + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: chartColor,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return type === 'revenue' ? '₹' + value.toLocaleString() : value;
                        }
                    }
                }
            }
        }
    });
}

// Export functions
function exportToExcel() {
    let csv = 'Date,Orders,Revenue,Average Value\n';
    {% if revenue_data %}
        {% for row in revenue_data %}
            csv += '{{ row.date }},{{ row.order_count }},{{ row.total_revenue }},{{ row.avg_order_value }}\n';
        {% endfor %}
    {% endif %}

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'financial_report_{{ start_date or "latest" }}_to_{{ end_date or "latest" }}.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

function exportTable(tableType) {
    let csv = '';
    let filename = '';

    switch(tableType) {
        case 'revenue':
            csv = 'Date,Orders,Revenue,Average Value\n';
            {% if revenue_data %}
                {% for row in revenue_data %}
                    csv += '{{ row.date }},{{ row.order_count }},{{ row.total_revenue }},{{ row.avg_order_value }}\n';
                {% endfor %}
            {% endif %}
            filename = 'revenue_breakdown.csv';
            break;
        case 'customers':
            csv = 'Rank,Customer,Orders,Total Spent\n';
            {% if customer_data %}
                {% for customer in customer_data %}
                    csv += '{{ loop.index }},{{ customer.name }},{{ customer.order_count }},{{ customer.total_spent }}\n';
                {% endfor %}
            {% endif %}
            filename = 'top_customers.csv';
            break;
        case 'payments':
            csv = 'Date,Payment Method,Amount,Status\n';
            {% if payment_data %}
                {% for payment in payment_data %}
                    csv += '{{ payment.payment_date }},{{ payment.payment_method }},{{ payment.total_amount }},Completed\n';
                {% endfor %}
            {% endif %}
            filename = 'payment_analysis.csv';
            break;
    }

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

function generatePDF() {
    // Show loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    btn.disabled = true;

    // Generate PDF report
    fetch('/finance/api/generate-pdf-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            report_type: 'financial_summary',
            date_range: document.querySelector('select[name="date_range"]').value,
            format: 'pdf'
        })
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `financial_report_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        btn.innerHTML = originalText;
        btn.disabled = false;
    })
    .catch(error => {
        console.error('Error generating PDF:', error);
        alert('Error generating PDF report. Please try again.');
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function showPaymentChart() {
    // Create modal for payment chart
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Payment Trends Chart</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <canvas id="paymentTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    // Show modal
    $(modal).modal('show');

    // Generate chart data
    fetch('/finance/api/payment-trends')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('paymentTrendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'Payment Amount',
                        data: data.amounts,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        });

    // Clean up modal when closed
    $(modal).on('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function viewPaymentDetails(paymentId) {
    if (paymentId) {
        // Fetch payment details and show in modal
        fetch(`/finance/api/payment-details/${paymentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const payment = data.payment;
                    const modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.innerHTML = `
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Payment Details - ${payment.payment_id}</h5>
                                    <button type="button" class="close" data-dismiss="modal">
                                        <span>&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Amount:</strong> ₹${payment.amount.toLocaleString()}</p>
                                            <p><strong>Method:</strong> ${payment.payment_method}</p>
                                            <p><strong>Date:</strong> ${payment.payment_date}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Customer:</strong> ${payment.customer_name}</p>
                                            <p><strong>Reference:</strong> ${payment.reference_number || 'N/A'}</p>
                                            <p><strong>Status:</strong> <span class="badge badge-success">${payment.status}</span></p>
                                        </div>
                                    </div>
                                    ${payment.notes ? `<div class="mt-3"><strong>Notes:</strong><br>${payment.notes}</div>` : ''}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <a href="/finance/payment/${paymentId}/attachments" class="btn btn-primary">View Attachments</a>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(modal);
                    $(modal).modal('show');

                    // Clean up modal when closed
                    $(modal).on('hidden.bs.modal', function() {
                        document.body.removeChild(modal);
                    });
                } else {
                    alert('Error loading payment details: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error loading payment details');
            });
    } else {
        alert('Payment ID not available');
    }
}

// Initialize chart on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
});

// Auto-refresh every 10 minutes
setTimeout(function() {
    location.reload();
}, 600000);
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
}

.bg-gradient {
    background: linear-gradient(135deg, #007bff, #6610f2) !important;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.btn-group .btn {
    border-radius: 6px !important;
    margin-right: 5px;
}

.btn-group .btn.active {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

@media print {
    .action-buttons, .btn, .filter-card {
        display: none !important;
    }

    .finance-dashboard {
        background: white !important;
    }

    .finance-header {
        background: white !important;
        color: black !important;
    }

    .finance-title {
        color: black !important;
    }
}

@media (max-width: 768px) {
    .finance-title {
        font-size: 1.8rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .stat-card {
        margin-bottom: 20px;
    }
}
</style>
{% endblock %}
