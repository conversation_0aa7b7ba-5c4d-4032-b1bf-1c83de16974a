{% extends 'base.html' %}

{% block title %}Generate Delivery Challan{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-file-alt"></i> Generate Delivery Challan
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Warehouse Step 2:</strong> Generate Delivery Challan after boss approval. 
                        This will send the order to Finance for invoice generation.
                    </div>

                    <!-- Order Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Order Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Order ID:</strong></td>
                                            <td>{{ order.order_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Customer:</strong></td>
                                            <td>{{ order.customer_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Order Date:</strong></td>
                                            <td>{{ order.order_date }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge badge-primary">{{ order.status }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Amount:</strong></td>
                                            <td><strong>₨{{ "{:,.2f}".format(order.order_amount) }}</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Customer Details</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td>{{ order.customer_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td>{{ order.customer_phone or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Address:</strong></td>
                                            <td>{{ order.customer_address or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Sales Agent:</strong></td>
                                            <td>{{ order.sales_agent or 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Approved By:</strong></td>
                                            <td>{{ order.approved_by or 'N/A' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Workflow Progress -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Workflow Progress</h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress-steps">
                                        <div class="step completed">
                                            <div class="step-icon">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div class="step-label">Order Placed</div>
                                        </div>
                                        <div class="step completed">
                                            <div class="step-icon">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div class="step-label">Boss Approved</div>
                                        </div>
                                        <div class="step active">
                                            <div class="step-icon">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <div class="step-label">Generate DC</div>
                                        </div>
                                        <div class="step">
                                            <div class="step-icon">
                                                <i class="fas fa-file-invoice"></i>
                                            </div>
                                            <div class="step-label">Finance Invoice</div>
                                        </div>
                                        <div class="step">
                                            <div class="step-icon">
                                                <i class="fas fa-truck"></i>
                                            </div>
                                            <div class="step-label">Dispatch</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-12">
                            <form method="POST" class="d-inline">
                                <button type="submit" class="btn btn-warning btn-lg">
                                    <i class="fas fa-file-alt"></i> Generate Delivery Challan
                                </button>
                            </form>
                            <a href="javascript:history.back()" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <a href="{{ url_for('workflow') }}" class="btn btn-info btn-lg ml-2">
                                <i class="fas fa-tasks"></i> Workflow
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
}

.step {
    text-align: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.completed:not(:last-child)::after {
    background-color: #28a745;
}

.step.active:not(:last-child)::after {
    background-color: #ffc107;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    position: relative;
    z-index: 2;
}

.step.completed .step-icon {
    background-color: #28a745;
    color: white;
}

.step.active .step-icon {
    background-color: #ffc107;
    color: #212529;
}

.step-label {
    font-size: 12px;
    font-weight: 500;
}
</style>
{% endblock %}
