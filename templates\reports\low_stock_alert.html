{% extends 'base.html' %}

{% block title %}Low Stock Alert Report - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alert Report
        </h1>
        <div>
            <button onclick="window.print()" class="btn btn-primary shadow-sm">
                <i class="fas fa-print fa-sm text-white-50"></i> Print Report
            </button>
        </div>
    </div>

    <!-- Alert Summary -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Critical Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_products|selectattr('stock_level', 'lt', 5)|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Out of Stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_products|selectattr('stock_level', 'eq', 0)|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_products|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Reorder Required
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_products|selectattr('stock_level', 'lt', 10)|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Products Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Low Stock Products</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="lowStockTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Product ID</th>
                            <th>Product Name</th>
                            <th>Category</th>
                            <th>Current Stock</th>
                            <th>Minimum Level</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in low_stock_products %}
                        <tr>
                            <td><strong>{{ product.product_id }}</strong></td>
                            <td>{{ product.name }}</td>
                            <td>{{ product.category or 'N/A' }}</td>
                            <td>
                                <span class="badge badge-{% if product.stock_level == 0 %}danger{% elif product.stock_level < 5 %}warning{% else %}info{% endif %}">
                                    {{ product.stock_level }}
                                </span>
                            </td>
                            <td>{{ product.minimum_level or 10 }}</td>
                            <td>
                                {% if product.stock_level == 0 %}
                                    <span class="badge badge-danger">Out of Stock</span>
                                {% elif product.stock_level < 5 %}
                                    <span class="badge badge-warning">Critical</span>
                                {% else %}
                                    <span class="badge badge-info">Low Stock</span>
                                {% endif %}
                            </td>
                            <td>{{ product.updated_at or now.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <a href="/products/{{ product.product_id }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="/inventory/add?product_id={{ product.product_id }}" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus"></i> Restock
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
$(document).ready(function() {
    $('#lowStockTable').DataTable({
        "pageLength": 25,
        "order": [[ 3, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 7 }
        ]
    });
});
</script>
{% endblock %}
{% endblock %}
