{% extends 'base.html' %}

{% block title %}Register Rider - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Modern Header -->
    <div class="d-flex justify-content-between align-items-center mb-5">
        <div>
            <h1 class="h2 mb-1 text-gray-900 font-weight-bold">
                <i class="fas fa-motorcycle text-primary me-3"></i>Register New Rider
            </h1>
            <p class="text-muted mb-0">Add a professional delivery rider to your team</p>
        </div>
        <div>
            <a href="{{ url_for('riders') }}" class="btn btn-outline-secondary btn-lg shadow-sm">
                <i class="fas fa-arrow-left me-2"></i>Back to Riders
            </a>
        </div>
    </div>

    <!-- Modern Registration Form -->
    <div class="row justify-content-center">
        <div class="col-xl-10">
            <div class="card border-0 shadow-lg">
                <!-- Card Header with Gradient -->
                <div class="card-header bg-gradient-primary text-white py-4">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-user-plus text-white"></i>
                        </div>
                        <div>
                            <h4 class="mb-0 font-weight-bold">Rider Registration</h4>
                            <small class="opacity-75">Complete the form below to register a new rider</small>
                        </div>
                    </div>
                </div>

                <!-- Form Body -->
                <div class="card-body p-5">
                    <form method="POST" action="{{ url_for('register_rider') }}" id="riderRegistrationForm">
                        <!-- Personal Information Section -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary font-weight-bold mb-1">
                                    <i class="fas fa-user me-2"></i>Personal Information
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-12">
                                    <div class="form-floating">
                                        <input type="text" class="form-control form-control-lg" id="name" name="name"
                                               placeholder="Full Name" required>
                                        <label for="name">Full Name <span class="text-danger">*</span></label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" class="form-control form-control-lg" id="email" name="email"
                                               placeholder="Email Address" required>
                                        <label for="email">Email Address <span class="text-danger">*</span></label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" class="form-control form-control-lg" id="phone" name="phone"
                                               placeholder="Phone Number" required>
                                        <label for="phone">Phone Number <span class="text-danger">*</span></label>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-floating">
                                        <input type="text" class="form-control form-control-lg" id="current_location" name="current_location"
                                               placeholder="Current Location">
                                        <label for="current_location">Current Location</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Vehicle Information Section -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary font-weight-bold mb-1">
                                    <i class="fas fa-motorcycle me-2"></i>Vehicle Information
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select form-control-lg" id="vehicle_type" name="vehicle_type" required>
                                            <option value="">Select Vehicle Type</option>
                                            <option value="Motorcycle" selected>Motorcycle</option>
                                            <option value="Scooter">Scooter</option>
                                            <option value="Bicycle">Bicycle</option>
                                            <option value="Car">Car</option>
                                        </select>
                                        <label for="vehicle_type">Vehicle Type <span class="text-danger">*</span></label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control form-control-lg" id="vehicle_number" name="vehicle_number"
                                               placeholder="Vehicle Number">
                                        <label for="vehicle_number">Vehicle Number</label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control form-control-lg" id="license_number" name="license_number"
                                               placeholder="License Number" required>
                                        <label for="license_number">License Number <span class="text-danger">*</span></label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" class="form-control form-control-lg" id="max_orders_per_day" name="max_orders_per_day"
                                               placeholder="Max Orders Per Day" value="10" min="1" max="50">
                                        <label for="max_orders_per_day">Max Orders Per Day</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="form-actions">
                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary btn-lg px-4" onclick="window.history.back()">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                                <button type="submit" class="btn btn-primary btn-lg px-5 shadow">
                                    <i class="fas fa-user-plus me-2"></i>Register Rider
                                </button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern CSS Styles -->
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
}

.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.form-section {
    position: relative;
}

.section-header {
    position: relative;
}

.section-divider {
    height: 3px;
    background: linear-gradient(90deg, #4e73df 0%, #e3f2fd 100%);
    border-radius: 2px;
    margin-top: 8px;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-control-lg {
    border-radius: 12px;
    border: 2px solid #e3f2fd;
    transition: all 0.3s ease;
    font-size: 1rem;
    padding: 1rem;
}

.form-control-lg:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    transform: translateY(-2px);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%234e73df' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 2px solid #e3f2fd;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    color: #495057;
    background-color: #fff;
}

.btn-lg {
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
}

.card {
    border-radius: 20px;
    overflow: hidden;
}

.form-actions {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e3f2fd;
}

@media (max-width: 768px) {
    .form-actions .d-flex {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* Animation for form sections */
.form-section {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effects for form controls */
.form-floating {
    position: relative;
}

.form-floating:hover .form-control {
    border-color: #4e73df;
}

/* Success state styling */
.form-control.is-valid {
    border-color: #1cc88a;
}

.form-control.is-invalid {
    border-color: #e74a3b;
}
</style>

<!-- Modern JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('riderRegistrationForm');
    const inputs = form.querySelectorAll('input, select');

    // Add real-time validation
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    // Form submission with loading state
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (validateForm()) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Registering...';
            submitBtn.disabled = true;

            // Simulate processing time then submit
            setTimeout(() => {
                form.submit();
            }, 1000);
        }
    });

    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');

        if (isRequired && !value) {
            field.classList.add('is-invalid');
            field.classList.remove('is-valid');
            return false;
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                return false;
            }
        }

        // Phone validation
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\-\s\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                return false;
            }
        }

        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        return true;
    }

    function validateForm() {
        let isValid = true;
        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });
        return isValid;
    }
});
</script>

{% endblock %}
