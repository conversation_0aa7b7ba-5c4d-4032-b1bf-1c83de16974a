<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCS Bulk Tracking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Tracking container styling */
        .tcs-container {
            position: relative;
            width: 100%;
            height: 600px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
        
        .tcs-iframe {
            position: absolute;
            top: -180px;
            left: 0;
            width: 100%;
            height: 900px;
            border: none;
        }
        
        /* Progress bar styling */
        .progress-container {
            height: 6px;
            background-color: #e5e7eb;
            border-radius: 3px;
            margin: 1rem 0;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 3px;
            background-color: #f0575d;
            transition: width 0.3s ease;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .tcs-container {
                height: 500px;
            }
            .tcs-iframe {
                top: -150px;
                height: 800px;
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4">
        <h1 class="text-3xl font-bold text-center mb-8 text-[#f0575d]">TCS Bulk Tracking</h1>
        
        <!-- Bulk Input Form -->
        <div class="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">Enter Tracking Numbers</h2>
            <textarea 
                id="trackingNumbers" 
                class="w-full p-3 border rounded focus:outline-none mb-4" 
                rows="6"
                placeholder="Enter tracking numbers, one per line or separated by commas"
            >31442083525
31442083394
306059323428</textarea>
            
            <div class="flex justify-between items-center">
                <div>
                    <button 
                        onclick="startBulkTracking()" 
                        class="bg-[#f0575d] text-white px-6 py-2 rounded hover:bg-[#d84a50] transition"
                    >
                        Track All
                    </button>
                    <button 
                        onclick="clearAll()" 
                        class="ml-2 bg-gray-300 text-gray-700 px-6 py-2 rounded hover:bg-gray-400 transition"
                    >
                        Clear All
                    </button>
                </div>
                <div id="counter" class="text-gray-600">3 numbers entered</div>
            </div>
        </div>
        
        <!-- Progress Indicator -->
        <div id="progressContainer" class="max-w-4xl mx-auto hidden">
            <div class="flex justify-between mb-1">
                <span id="progressStatus">Processing tracking numbers...</span>
                <span id="progressCount">0/0</span>
            </div>
            <div class="progress-container">
                <div id="progressBar" class="progress-bar" style="width: 0%"></div>
            </div>
        </div>
        
        <!-- Results Container -->
        <div id="resultsContainer" class="max-w-4xl mx-auto"></div>
    </div>

    <script>
        // Get tracking numbers from textarea
        function getTrackingNumbers() {
            const textarea = document.getElementById('trackingNumbers');
            return textarea.value
                .split('\n')
                .flatMap(line => line.split(','))
                .map(num => num.trim())
                .filter(num => num !== '');
        }
        
        // Update counter
        function updateCounter() {
            const numbers = getTrackingNumbers();
            document.getElementById('counter').textContent = 
                `${numbers.length} number${numbers.length !== 1 ? 's' : ''} entered`;
        }
        
        // Clear all results
        function clearAll() {
            document.getElementById('trackingNumbers').value = '';
            document.getElementById('resultsContainer').innerHTML = '';
            document.getElementById('progressContainer').classList.add('hidden');
            updateCounter();
        }
        
        // Create tracking iframe
        function createTrackingFrame(trackingNumber) {
            const container = document.createElement('div');
            container.className = 'tcs-container';
            container.id = `tracking-${trackingNumber}`;
            
            const iframe = document.createElement('iframe');
            iframe.className = 'tcs-iframe';
            iframe.src = `https://www.tcsexpress.com/track/${trackingNumber}`;
            iframe.onload = function() {
                try {
                    // Attempt to hide footer via JavaScript (may not work due to CORS)
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const footer = iframeDoc.querySelector('footer, #main-footer, .main-footer');
                    if (footer) footer.style.display = 'none';
                } catch (e) {
                    console.log(`Using CSS cropping for ${trackingNumber}`);
                }
            };
            
            const label = document.createElement('div');
            label.className = 'text-sm font-medium mt-2 mb-1';
            label.textContent = `Tracking #${trackingNumber}`;
            
            container.appendChild(iframe);
            
            const wrapper = document.createElement('div');
            wrapper.className = 'mb-8';
            wrapper.appendChild(label);
            wrapper.appendChild(container);
            
            return wrapper;
        }
        
        // Process bulk tracking
        async function startBulkTracking() {
            const trackingNumbers = getTrackingNumbers();
            const resultsContainer = document.getElementById('resultsContainer');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressCount = document.getElementById('progressCount');
            const progressStatus = document.getElementById('progressStatus');
            
            if (trackingNumbers.length === 0) {
                alert('Please enter at least one tracking number');
                return;
            }
            
            // Clear previous results
            resultsContainer.innerHTML = '';
            progressContainer.classList.remove('hidden');
            
            // Set up progress tracking
            let completed = 0;
            const total = trackingNumbers.length;
            progressCount.textContent = `0/${total}`;
            progressBar.style.width = '0%';
            
            // Process each number with delay to avoid rate limiting
            for (const trackingNumber of trackingNumbers) {
                progressStatus.textContent = `Processing #${trackingNumber}...`;
                
                // Create and append tracking frame
                resultsContainer.appendChild(createTrackingFrame(trackingNumber));
                
                // Update progress
                completed++;
                progressCount.textContent = `${completed}/${total}`;
                progressBar.style.width = `${(completed / total) * 100}%`;
                
                // Add slight delay between requests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            progressStatus.textContent = 'Completed all tracking numbers';
        }
        
        // Initialize counter
        document.getElementById('trackingNumbers').addEventListener('input', updateCounter);
        window.onload = updateCounter;
    </script>
</body>
</html>